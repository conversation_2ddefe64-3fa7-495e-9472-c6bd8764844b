-- 向各表添加用户ID和部门ID字段

-- 为项目信息表添加字段
ALTER TABLE zz_project_info ADD COLUMN user_id bigint COMMENT '用户ID';
ALTER TABLE zz_project_info ADD COLUMN dept_id bigint COMMENT '部门ID';

-- 为专家信息表添加字段
ALTER TABLE zz_expert_info ADD COLUMN user_id bigint COMMENT '用户ID';
ALTER TABLE zz_expert_info ADD COLUMN dept_id bigint COMMENT '部门ID';

-- 为客户信息表添加字段
ALTER TABLE zz_customer ADD COLUMN user_id bigint COMMENT '用户ID';
ALTER TABLE zz_customer ADD COLUMN dept_id bigint COMMENT '部门ID';

-- 为项目任务表添加字段
ALTER TABLE zz_project_tasks ADD COLUMN user_id bigint COMMENT '用户ID';
ALTER TABLE zz_project_tasks ADD COLUMN dept_id bigint COMMENT '部门ID';

-- 为专家咨询记录表添加字段
ALTER TABLE zz_expert_consultation ADD COLUMN user_id bigint COMMENT '用户ID';
ALTER TABLE zz_expert_consultation ADD COLUMN dept_id bigint COMMENT '部门ID'; 