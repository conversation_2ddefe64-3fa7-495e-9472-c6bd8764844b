-- 为zz_expert_consultation表添加project_task_id列
ALTER TABLE zz_expert_consultation ADD COLUMN project_task_id VARCHAR(64) NULL COMMENT '项目任务ID';

-- 更新表中已有数据
-- 注意：此处需根据实际业务情况填充数据。如果可以通过关联查询确定正确的project_task_id，可以使用类似以下的SQL：
-- UPDATE zz_expert_consultation ec
-- JOIN zz_project_tasks pt ON ec.project_info_id = pt.project_info_id
-- SET ec.project_task_id = pt.id
-- WHERE ec.customer_id IS NOT NULL;

-- 删除customer_id列
ALTER TABLE zz_expert_consultation DROP COLUMN customer_id;

-- 添加外键约束（如果需要）
ALTER TABLE zz_expert_consultation ADD CONSTRAINT fk_expert_consultation_project_task
FOREIGN KEY (project_task_id) REFERENCES zz_project_tasks (id); 