-- 添加导入权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('劳务费导入', (SELECT menu_id FROM sys_menu WHERE perms = 'task:labor:list'), 6, '#', 'F', '0', 'task:labor:import', '#', 'admin', NOW(), 'admin', NOW(), '');

-- 添加受访者导入权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('受访者导入', (SELECT menu_id FROM sys_menu WHERE perms = 'task:respondent:list'), 6, '#', 'F', '0', 'task:respondent:import', '#', 'admin', NOW(), 'admin', NOW(), '');

-- 添加专家信息导入权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('专家信息导入', (SELECT menu_id FROM sys_menu WHERE perms = 'pmdata:expertinfo:list'), 6, '#', 'F', '0', 'pmdata:expertinfo:import', '#', 'admin', NOW(), 'admin', NOW(), '');

-- 添加专家咨询记录导入权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('专家咨询记录导入', (SELECT menu_id FROM sys_menu WHERE perms = 'task:consultation:list'), 6, '#', 'F', '0', 'task:consultation:import', '#', 'admin', NOW(), 'admin', NOW(), ''); 