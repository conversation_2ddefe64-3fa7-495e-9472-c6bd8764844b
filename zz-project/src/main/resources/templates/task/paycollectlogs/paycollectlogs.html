<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目任务 ID：</label>
                                <input type="text" name="projectTaskId"/>
                            </li>
                            <li>
                                <label>收款金额：</label>
                                <input type="text" name="amount"/>
                            </li>
                            <li class="select-time">
                                <label>提交时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginPaymentTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endPaymentTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="task:paycollectlogs:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="task:paycollectlogs:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="task:paycollectlogs:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:paycollectlogs:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:paycollectlogs:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:paycollectlogs:remove')}]];
        var prefix = ctx + "task/paycollectlogs";

        var projectTaskId = [[${projectTaskId}]];
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add?projectTaskId=" + projectTaskId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "收款记录",
                queryParams: function(params) {
                    params.projectTaskId = projectTaskId;
                    return params;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectNo',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'taskName',
                    title: '任务名称'
                },
                {
                    field: 'amount',
                    title: '收款金额'
                },
                {
                    field: 'paymentTime',
                    title: '提交时间'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>