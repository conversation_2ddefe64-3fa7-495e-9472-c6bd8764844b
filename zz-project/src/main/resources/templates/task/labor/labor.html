<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务劳务费列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>收款人姓名：</label>
                                <input type="text" name="recipientName"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="contactPhone"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="task:labor:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="task:labor:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="task:labor:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="task:labor:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:labor:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
            <!-- 统计信息区域 -->
            <div class="col-sm-12">
                <div class="panel panel-default">
                    <div class="panel-heading">统计信息</div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-2">
                                <label>天数合计：</label>
                                <span id="totalWorkDays">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>加班合计：</label>
                                <span id="totalOvertimeAmount">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>应发合计：</label>
                                <span id="totalAmountPayable">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>实发合计：</label>
                                <span id="totalAmountPaid">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>支付合计：</label>
                                <span id="totalPaymentAmount">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:labor:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:labor:remove')}]];
        var prefix = ctx + "task/labor";

        var projectTaskId = [[${projectTaskId}]];
        $(function() {
            var options = {
                url: prefix + "/list?projectTaskId=" + projectTaskId,
                createUrl: prefix + "/add?projectTaskId=" + projectTaskId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "项目任务劳务费",
                responseHandler: function(res) {
                    // 更新统计信息
                    if (res.total) {
                        $("#totalWorkDays").text(res.otherData.workDays || 0);
                        $("#totalOvertimeAmount").text(res.otherData.overtimeAmount || 0);
                        $("#totalAmountPayable").text(res.otherData.amountPayable || 0);
                        $("#totalAmountPaid").text(res.otherData.amountPaid || 0);
                        $("#totalPaymentAmount").text(res.otherData.paymentAmount || 0);
                    }
                    return res;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectNo',
                    title: '项目编号'
                },
                {
                    field: 'projectTaskName',
                    title: '项目[任务]'
                },
                {
                    field: 'supervisorName',
                    title: '督导'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'job',
                    title: '工作职位'
                },
                {
                    field: 'workDays',
                    title: '工作天数(有效份数)'
                },
                {
                    field: 'recipientName',
                    title: '收款人姓名'
                },
                {
                    field: 'laborPrice',
                    title: '单价'
                },
                {
                    field: 'overtimeAmount',
                    title: '加班补贴'
                },
                {
                    field: 'taxTate',
                    title: '税点'
                },
                {
                    field: 'amountPayable',
                    title: '应发'
                },
                {
                    field: 'amountPaid',
                    title: '实发'
                },
                {
                    field: 'paymentAmount',
                    title: '支付金额'
                },
                {
                    field: 'paymentTime',
                    title: '支付时间'
                },
                {
                    field: 'paymentAccount',
                    title: '付款账号'
                },
                {
                    field: 'contactPhone',
                    title: '联系电话'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果相同姓名和联系电话的记录已经存在，更新这条数据。"> 是否更新已经存在的劳务费数据
            <input type="hidden" id="projectTaskId" name="projectTaskId" th:value="${projectTaskId}">
             &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>