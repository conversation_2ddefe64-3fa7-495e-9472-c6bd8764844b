<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务劳务费列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="contactPhone"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:labor:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
            <!-- 统计信息区域 -->
            <div class="col-sm-12">
                <div class="panel panel-default">
                    <div class="panel-heading">统计信息</div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-2">
                                <label>工作天数汇总：</label>
                                <span id="totalWorkDays">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>加班补贴汇总：</label>
                                <span id="totalOvertimeAmount">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>应发汇总：</label>
                                <span id="totalAmountPayable">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>实发汇总：</label>
                                <span id="totalAmountPaid">0</span>
                            </div>
                            <div class="col-sm-2">
                                <label>支付金额汇总：</label>
                                <span id="totalPaymentAmount">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:labor:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:labor:remove')}]];
        var laborTypeCodeDatas = [[${@dict.getType('zzpm_labor_type')}]];
        var prefix = ctx + "task/labor";

        var projectTaskId = [[${projectTaskId}]];
        $(function() {
            var options = {
                url: prefix + "/list?projectTaskId=" + projectTaskId,
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目任务劳务费",
                responseHandler: function(res) {
                    // 更新统计信息
                    if (res.total) {
                        $("#totalWorkDays").text(res.otherData.workDays || 0);
                        $("#totalOvertimeAmount").text(res.otherData.overtimeAmount || 0);
                        $("#totalAmountPayable").text(res.otherData.amountPayable || 0);
                        $("#totalAmountPaid").text(res.otherData.amountPaid || 0);
                        $("#totalPaymentAmount").text(res.otherData.paymentAmount || 0);
                    }
                    return res;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'job',
                    title: '工作职位'
                },
                {
                    field: 'bTime',
                    title: '开始时间'
                },
                {
                    field: 'eTime',
                    title: '结束时间'
                },
                {
                    field: 'workDays',
                    title: '时长'
                },
                {
                    field: 'laborPrice',
                    title: '单价'
                },
                {
                    field: 'overtimeAmount',
                    title: '加班补贴'
                },
                {
                    field: 'taxTate',
                    title: '税点'
                },
                {
                    field: 'amountPayable',
                    title: '应发'
                },
                {
                    field: 'amountPaid',
                    title: '实发'
                },
                {
                    field: 'paymentAccount',
                    title: '付款账号'
                },
                {
                    field: 'contactPhone',
                    title: '联系电话'
                },
                {
                    field: 'remarks',
                    title: '备注'
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>