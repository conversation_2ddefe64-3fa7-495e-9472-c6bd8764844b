<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('劳务费付款')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-labor-pay" th:object="${projectTaskLabor}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">姓名：</label>
                    <div class="col-sm-8">
                        <input name="name" th:field="*{name}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">应发金额：</label>
                    <div class="col-sm-8">
                        <input name="amountPayable" th:field="*{amountPayable}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">实发金额：</label>
                    <div class="col-sm-8">
                        <input name="amountPaid" th:field="*{amountPaid}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">付款账号：</label>
                    <div class="col-sm-8">
                        <input name="paymentAccount" th:field="*{paymentAccount}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">支付金额：</label>
                    <div class="col-sm-8">
                        <input name="paymentAmount" th:field="*{paymentAmount}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">支付时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="paymentTime" th:value="${#dates.format(projectTaskLabor.paymentTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">付款说明：</label>
                    <div class="col-sm-8">
                        <textarea name="paymentDescription" th:field="*{paymentDescription}" class="form-control"></textarea>
                    </div>
                </div>
            </div>
           
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "task/labor";
        
        $("#form-labor-pay").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/pay", $('#form-labor-pay').serialize());
            }
        }

        $("input[name='paymentTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html> 