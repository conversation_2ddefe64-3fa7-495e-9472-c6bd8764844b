<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('劳务费查询')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="projectNo"/>
                            </li>
                            <li>
                                <label>督导：</label>
                                <input type="text" name="supervisorName"/>
                            </li>
                            <li>
                                <label>兼职人姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="contactPhone"/>
                            </li>
                            <li>
                                <label>开始时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="params[beginTime]"/>
                            </li>
                            <li>
                                <label>结束时间：</label>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <label>未完成付款：</label>
                                <input type="checkbox" name="params[unpaidOnly]" value="true"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:labor:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-success single disabled" onclick="paySelected()" shiro:hasPermission="task:labor:pay">
                    <i class="fa fa-money"></i> 付款
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var laborTypeCodeDatas = [[${@dict.getType('zzpm_labor_type')}]];
        var prefix = ctx + "task/labor";
        var payFlag = [[${@permission.hasPermi('task:labor:pay')}]];

        $(function() {
            var options = {
                url: prefix + "/list?settleStatusCode=4",
                exportUrl: prefix + "/export?settleStatusCode=4",
                modalName: "劳务费查询",
                showSearch: false,  // 隐藏搜索按钮
                showRefresh: false, // 隐藏刷新按钮
                showToggle: false,  // 隐藏切换按钮
                showColumns: false, // 隐藏列选项按钮
                columns: [{
                    checkbox: true
                },{
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectNo',
                    title: '项目编号'
                },
                {
                    field: 'projectTaskName',
                    title: '项目[任务]'
                },
                {
                    field: 'supervisorName',
                    title: '督导'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'job',
                    title: '工作职位'
                },
                {
                    field: 'workDays',
                    title: '工作天数(有效份数)'
                },
                {
                    field: 'recipientName',
                    title: '收款人姓名'
                },
                {
                    field: 'laborPrice',
                    title: '单价'
                },
                {
                    field: 'overtimeAmount',
                    title: '加班补贴'
                },
                {
                    field: 'taxTate',
                    title: '税点'
                },
                {
                    field: 'amountPayable',
                    title: '应发'
                },
                {
                    field: 'amountPaid',
                    title: '实发'
                },
                {
                    field: 'unpaidAmount',
                    title: '待付款金额',
                    formatter: function(value, row, index) {
                        var amountPaid = row.amountPaid || 0;
                        var paymentAmount = row.paymentAmount || 0;
                        return (amountPaid - paymentAmount).toFixed(2);
                    }
                },
                {
                    field: 'paymentAmount',
                    title: '支付金额'
                },
                {
                    field: 'paymentTime',
                    title: '支付时间'
                },
                {
                    field: 'paymentDescription',
                    title: '付款说明'
                },
                {
                    field: 'paymentAccount',
                    title: '付款账号'
                },
                {
                    field: 'contactPhone',
                    title: '联系电话'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + payFlag + '" href="javascript:void(0)" onclick="$.operate.pay(\'' + row.id + '\')"><i class="fa fa-money"></i>付款</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 付款按钮点击事件
        $.operate.pay = function(id) {
            var url = prefix + "/pay";
            var modalName = "劳务费付款";
            $.modal.open(modalName, url + "?id=" + id);
        };
        
        // 付款选中的行
        function paySelected() {
            var rows = $.table.selectFirstColumns();
            if (rows.length != 1) {
                $.modal.alertWarning("请选择一条记录");
                return;
            }
            $.operate.pay(rows[0]);
        }
    </script>
</body>
</html> 