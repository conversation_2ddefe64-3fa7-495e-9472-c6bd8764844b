<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目任务劳务费')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-labor-edit" th:object="${projectTaskLabor}">
            <input name="id" th:field="*{id}" type="hidden">
            <input name="projectTaskId" th:field="*{projectTaskId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">姓名：</label>
                    <div class="col-sm-8">
                        <input name="name" th:field="*{name}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工作职位：</label>
                    <div class="col-sm-8">
                        <input name="job" th:field="*{job}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工作天数(有效份数)：</label>
                    <div class="col-sm-8">
                        <input name="workDays" th:field="*{workDays}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">收款人姓名：</label>
                    <div class="col-sm-8">
                        <input name="recipientName" th:field="*{recipientName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单价：</label>
                    <div class="col-sm-8">
                        <input name="laborPrice" th:field="*{laborPrice}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">加班补贴：</label>
                    <div class="col-sm-8">
                        <input name="overtimeAmount" th:field="*{overtimeAmount}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">税点：</label>
                    <div class="col-sm-8">
                        <input name="taxTate" th:field="*{taxTate}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">应发：</label>
                    <div class="col-sm-8">
                        <input name="amountPayable" th:field="*{amountPayable}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">实发：</label>
                    <div class="col-sm-8">
                        <input name="amountPaid" th:field="*{amountPaid}" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">付款账号：</label>
                    <div class="col-sm-8">
                        <input name="paymentAccount" th:field="*{paymentAccount}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">联系电话：</label>
                    <div class="col-sm-8">
                        <input name="contactPhone" th:field="*{contactPhone}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control">[[*{remarks}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "task/labor";
        $("#form-labor-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-labor-edit').serialize());
            }
        }
    </script>
</body>
</html>