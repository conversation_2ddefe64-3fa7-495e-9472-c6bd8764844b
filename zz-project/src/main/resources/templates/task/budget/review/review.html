<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目任务预算')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: footer" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-horizontal m" id="form-budget-add">
            <th:block th:include="~{fragment/project.html :: project(${projectTaskBudget.projectTasks.projectInfo})}"/>

            <th:block th:include="~{fragment/project.html :: task(${projectTaskBudget.projectTasks})}"/>

            <th:block th:include="~{fragment/project.html :: taskTotal(${projectTaskBudget.projectTasks.id})}"/>

            <th:block th:include="~{fragment/project.html :: budgetList(${projectTaskBudget.projectTasks.id})}"/>

            <th:block th:include="~{fragment/project.html :: budget(${projectTaskBudget})}"/>

            <th:block th:include="~{fragment/project.html :: review}"/>
        </div>
    </div>
    <script th:inline="javascript">
        var successStatus = [[${review}]];
        function submitHandler(pass) {
            if ($.validate.form()) {
                if(pass){
                    $("input[name='review']").val(successStatus);
                }else{
                    if($.common.isEmpty($('[name="remarks"]').val())){
                        $.modal.alertError("请填写拒绝理由");
                        return;
                    }
                    $("input[name='review']").val(-2);
                }
                $.operate.save(ctx + "task/budget/[(${projectTaskBudget.id})]/review", $('#form-review-edit').serialize());
            }
        }
    </script>
</body>
</html>