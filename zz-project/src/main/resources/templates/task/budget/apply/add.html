<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目任务预算')" />
    <th:block th:include="include :: footer" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
    <th:block th:include="~{fragment/project.html :: freshData}"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-horizontal m" id="form-budget-add">
            <div id="project" class="col-xs-12">
                <div th:replace="~{fragment/project.html :: project(${projectTasks != null ? projectTasks.projectInfo : null})}"></div>
            </div>

            <div id="task" class="col-xs-12">
                <div th:replace="~{fragment/project.html :: task(${projectTasks != null ? projectTasks : null})}"></div>
            </div>

            <div id="taskTotal" class="col-xs-12">
                <div th:replace="~{fragment/project.html :: taskTotal(${projectTasks != null ? projectTasks.id : null})}"></div>
            </div>

            <th:block th:include="~{fragment/projectedit.html :: budget(${projectTasks != null ? projectTasks.id : null},null)}"/>
        </div>
    </div>
</body>
</html>