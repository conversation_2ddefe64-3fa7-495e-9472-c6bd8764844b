<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目任务预算')" />
    <th:block th:include="include :: footer" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
    <th:block th:include="~{fragment/project.html :: freshData}"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-horizontal m" id="form-budget-add">
            <div id="project" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: project(${projectTaskBudget.projectTasks.projectInfo})}"/>
            </div>

            <div id="task" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: task(${projectTaskBudget.projectTasks})}"/>
            </div>

            <div id="taskTotal" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: taskTotal(${projectTaskBudget.projectTasks.id})}"/>
            </div>

            <th:block th:include="~{fragment/projectedit.html :: budget(${projectTaskBudget.projectTasks.id},${projectTaskBudget})}"/>
        </div>
    </div>

</body>
</html>