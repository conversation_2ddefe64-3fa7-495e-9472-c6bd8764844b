<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务预算列表')" />
    <th:block th:include="include :: footer" />
</head>
<body class="gray-bg">
     <div class="container-div">

        <div class="row">
            <th:block th:if="${projectTasks != null}">
                <div class="col-sm-12 search-collapse form-horizontal">
                    <div th:replace="~{fragment/project.html :: project(${projectTasks.projectInfo})}"></div>
                    <div th:replace="~{fragment/project.html :: task(${projectTasks})}"></div>
                    <div th:replace="~{fragment/project.html :: taskTotal(${projectTasks.id})}"></div>
                </div>
            </th:block>

            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input name="projectTaskId" th:value="${projectTasks.id}" type="hidden">
                    <div class="select-list">
                        <ul>
                            <!--<li>
                                <label>项目任务 ID：</label>
                                <input type="text" name="projectTaskId"/>
                            </li>-->
                            <li class="select-time">
                                <label>提交时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateTime]"/>
                            </li>
                            <li>
                                <label>申请人：</label>
                                <input type="text" name="applicant"/>
                            </li>
                            <li class="select-time">
                                <label>申请时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginApplyTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endApplyTime]"/>
                            </li>
                            <li>
                                <label>审核状态：</label>
                                <select name="budgetStatusCode" th:with="type=${@dict.getType('zzpm_budget_status_format')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addFull()" shiro:hasPermission="task:budget:apply:add">
                    <i class="fa fa-plus"></i> 新增预算
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:budget:apply:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:budget:apply:remove')}]];
        var budgetStatusCodeDatas = [[${@dict.getType('zzpm_budget_status_format')}]];
        var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
        var prefix = ctx + "task/budget/apply";

        var projectTaskId = [[${projectTasks.id}]];
        $(function() {
            var options = {
                url: prefix + "?orderByColumn=projectTaskId,applyTime desc",
                createUrl: prefix + "/add?projectTaskId=" + projectTaskId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目任务预算",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectTasks.projectInfo.pNo',
                    title: '项目编号'
                },
                {
                    field: 'projectTasks.projectInfo.pName',
                    title: '项目名称'
                },
                {
                    field: 'projectTasks.projectInfo.pTypeCode',
                    title: '项目类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabels(pTypeCodeDatas, value);
                    }
                },
                {
                    field: 'projectTasks.projectInfo.pNoCus',
                    title: '客户项目编号'
                },
                {
                    field: 'projectTasks.taskName',
                    title: '任务名称'
                },
                {
                    field: 'applyTime',
                    title: '申请时间'
                },
                {
                    field: 'budgetStatusCode',
                    title: '审核状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(budgetStatusCodeDatas, value);
                    }
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        // Only show borrow management button when budgetStatusCode = 4
                        if (row.budgetStatusCode == 4) {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.modal.openTab(\'项目[' + row.projectTasks.projectInfo.pName + '] -> 任务[' + row.projectTasks.taskName + '] -> 预算[' + row.applyTime + '] -> 借款管理\',\'' + ctx + 'task/loans/apply?projectTaskBudgetId=' + row.id + '\')"><i class="fa fa-edit"></i>借款管理</a> ');
                        }
                        
                        if (row.budgetStatusCode == 0 || row.budgetStatusCode == -2) {
                            // 当budgetStatusCode为0或-2时，显示所有三个按钮
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="submitReview(\'' + row.id + '\')"><i class="fa fa-edit"></i>提交预算</a> ');
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑预算</a> ');
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除预算</a>');
                        } else if (row.budgetStatusCode == -1) {
                            // 当budgetStatusCode为-1时，仅显示删除按钮
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除预算</a>');
                        }
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function submitReview(id) {
            $.modal.confirm("确定要提交审核吗？", function() {
                $.operate.post(ctx + "task/budget/"+id+"/review?review=[[${review}]]");
            })
        }
    </script>
</body>
</html>