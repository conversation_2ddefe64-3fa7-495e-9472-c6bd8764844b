<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务预算详情列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目任务 ID：</label>
                                <input type="text" name="projectTaskId"/>
                            </li>
                            <li>
                                <label>项目任务预算 ID：</label>
                                <input type="text" name="projectTaskBudgetId"/>
                            </li>
                            <li>
                                <label>费用主类ID：</label>
                                <select name="costCategorieMainCode" th:with="type=${@dict.getType('zzpm_categorie_main')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>费用子类ID：</label>
                                <select name="costCategorieSubCode" th:with="type=${@dict.getType('zzpm_cost_categorie_sub')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>预算单价：</label>
                                <input type="text" name="priceBudget"/>
                            </li>
                            <li>
                                <label>预算数量：</label>
                                <input type="text" name="quantityBudget"/>
                            </li>
                            <li class="select-time">
                                <label>提交时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="budget:budgetdetail:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="budget:budgetdetail:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="budget:budgetdetail:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="budget:budgetdetail:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('budget:budgetdetail:edit')}]];
        var removeFlag = [[${@permission.hasPermi('budget:budgetdetail:remove')}]];
        var costCategorieMainCodeDatas = [[${@dict.getType('zzpm_categorie_main')}]];
        var costCategorieSubCodeDatas = [[${@dict.getType('zzpm_cost_categorie_sub')}]];
        var prefix = ctx + "budget/budgetdetail";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目任务预算详情",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectTaskId',
                    title: '项目任务 ID'
                },
                {
                    field: 'projectTaskBudgetId',
                    title: '项目任务预算 ID'
                },
                {
                    field: 'costCategorieMainCode',
                    title: '费用主类ID',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(costCategorieMainCodeDatas, value);
                    }
                },
                {
                    field: 'costCategorieSubCode',
                    title: '费用子类ID',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(costCategorieSubCodeDatas, value);
                    }
                },
                {
                    field: 'priceBudget',
                    title: '预算单价'
                },
                {
                    field: 'quantityBudget',
                    title: '预算数量'
                },
                {
                    field: 'createTime',
                    title: '提交时间'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>