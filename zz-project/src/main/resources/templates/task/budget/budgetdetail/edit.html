<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目任务预算详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-budgetdetail-edit" th:object="${projectTaskBudgetDetail}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目任务 ID：</label>
                    <div class="col-sm-8">
                        <input name="projectTaskId" th:field="*{projectTaskId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目任务预算 ID：</label>
                    <div class="col-sm-8">
                        <input name="projectTaskBudgetId" th:field="*{projectTaskBudgetId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">费用主类ID：</label>
                    <div class="col-sm-8">
                        <select name="costCategorieMainCode" class="form-control" th:with="type=${@dict.getType('zzpm_categorie_main')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{costCategorieMainCode}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">费用子类ID：</label>
                    <div class="col-sm-8">
                        <select name="costCategorieSubCode" class="form-control" th:with="type=${@dict.getType('zzpm_cost_categorie_sub')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{costCategorieSubCode}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预算单价：</label>
                    <div class="col-sm-8">
                        <input name="priceBudget" th:field="*{priceBudget}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">预算数量：</label>
                    <div class="col-sm-8">
                        <input name="quantityBudget" th:field="*{quantityBudget}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control">[[*{remarks}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "budget/budgetdetail";
        $("#form-budgetdetail-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-budgetdetail-edit').serialize());
            }
        }
    </script>
</body>
</html>