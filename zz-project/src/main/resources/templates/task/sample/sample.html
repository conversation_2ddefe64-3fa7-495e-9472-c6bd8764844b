<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务样本列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目任务 ID：</label>
                                <input type="text" name="projectTaskId"/>
                            </li>
                            <li>
                                <label>费用类型 code：</label>
                                <select name="sampleTypeCode" th:with="type=${@dict.getType('zzpm_sample_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>费用名称：</label>
                                <input type="text" name="sampleName"/>
                            </li>
                            <li>
                                <label>单价：</label>
                                <input type="text" name="samplePrice"/>
                            </li>
                            <li>
                                <label>数量：</label>
                                <input type="text" name="sampleQuantity"/>
                            </li>
                            <li>
                                <label>样本完成量：</label>
                                <input type="text" name="sampleQuantityFinish"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="task:sample:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="task:sample:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="task:sample:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:sample:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:sample:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:sample:remove')}]];
        var sampleTypeCodeDatas = [[${@dict.getType('zzpm_sample_type')}]];
        var prefix = ctx + "task/sample";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目任务样本",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectTaskId',
                    title: '项目任务 ID'
                },
                {
                    field: 'sampleTypeCode',
                    title: '费用类型 code',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(sampleTypeCodeDatas, value);
                    }
                },
                {
                    field: 'sampleName',
                    title: '费用名称'
                },
                {
                    field: 'samplePrice',
                    title: '单价'
                },
                {
                    field: 'sampleQuantity',
                    title: '数量'
                },
                {
                    field: 'sampleQuantityFinish',
                    title: '样本完成量'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>