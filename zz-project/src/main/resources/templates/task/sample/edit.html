<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目任务样本')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-sample-edit" th:object="${projectTaskSample}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目任务 ID：</label>
                    <div class="col-sm-8">
                        <input name="projectTaskId" th:field="*{projectTaskId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">费用类型 code：</label>
                    <div class="col-sm-8">
                        <select name="sampleTypeCode" class="form-control" th:with="type=${@dict.getType('zzpm_sample_type')}" required>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{sampleTypeCode}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">费用名称：</label>
                    <div class="col-sm-8">
                        <input name="sampleName" th:field="*{sampleName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单价：</label>
                    <div class="col-sm-8">
                        <input name="samplePrice" th:field="*{samplePrice}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数量：</label>
                    <div class="col-sm-8">
                        <input name="sampleQuantity" th:field="*{sampleQuantity}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">样本完成量：</label>
                    <div class="col-sm-8">
                        <input name="sampleQuantityFinish" th:field="*{sampleQuantityFinish}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control">[[*{remarks}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "task/sample";
        $("#form-sample-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-sample-edit').serialize());
            }
        }
    </script>
</body>
</html>