<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目阶段任务列表')" />
    <th:block th:include="include :: footer" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <th:block th:if="${projectInfo != null}">
                <div class="col-sm-12 search-collapse form-horizontal">
                    <div th:replace="~{fragment/project.html :: project(${projectInfo})}"></div>
                </div>
            </th:block>

            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input name="projectInfoId" th:value="${projectInfo.id}" type="hidden">
                    <div class="select-list">
                        <ul>
                            <!--<li>
                                <label>项目编号：</label>
                                <input type="text" name="pNo"/>
                            </li>
                            <li>
                                <label>项目：</label>
                                <select name="projectInfoId" class="form-control" th:with="pinfolist=${@dataUtils.getProjects()}">
                                    <option value="">所有</option>
                                    <option th:each="pinfo : ${pinfolist}" th:text="${pinfo.pName}" th:value="${pinfo.id}"></option>
                                </select>
                            </li>-->
                            <li>
                                <label>任务名称：</label>
                                <input type="text" name="taskName"/>
                            </li>
                            <li>
                                <label>客户联系人：</label>
                                <input type="text" name="cusContactPerson"/>
                            </li>
                            <li>
                                <label>客户联系电话：</label>
                                <input type="text" name="cusContactPhone"/>
                            </li>
                            <li>
                                <label>申请时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginApplyTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endApplyTime]"/>

                            </li>
                            <li>
                                <label>申请状态：</label>
                                <select name="params[applyStatus]" th:with="type=${@dict.getType('zzpm_apply_status_format')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addFull()" shiro:hasPermission="task:tasks:apply:add">
                    <i class="fa fa-plus"></i> 新增任务
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:tasks:apply:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:tasks:apply:remove')}]];
        var applyStatusCodeDatas = [[${@dict.getType('zzpm_apply_status_format')}]];
        var settleStatusCodeDatas = [[${@dict.getType('zzpm_settle_status_format')}]];
        var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
        var prefix = ctx + "task/apply";

        var projectInfoId = [[${projectInfo.id}]];
        $(function() {
            var options = {
                url: prefix + "?orderByColumn=projectInfoId,applyTime desc",
                createUrl: prefix + "/add?projectInfoId=" + projectInfoId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目阶段任务",
                columns: [{
                        checkbox: true

                    },
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    /*{
                        field: 'projectInfo.pNo',
                        title: '项目编号'
                    },
                    {
                        field: 'projectInfo.pName',
                        title: '项目名称'
                    },
                    {
                        field: 'projectInfo.pTypeCode',
                        title: '项目类型',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabels(pTypeCodeDatas, value);
                        }
                    },
                    {
                        field: 'projectInfo.pNoCus',
                        title: '客户项目编号'
                    },*/
                    {
                        field: 'taskName',
                        title: '任务名称'
                    },
                    {
                        field: 'cusContactPerson',
                        title: '客户联系人'
                    },
                    {
                        field: 'cusContactPhone',
                        title: '客户联系电话'
                    },
                    {
                        field: 'applyTime',
                        title: '任务申请时间'
                    },
                    {
                        field: 'applyStatusCode',
                        title: '任务申请状态',
                        formatter: function(value, row, index) {
                           return $.table.selectDictLabel(applyStatusCodeDatas, value);
                        }
                    },
                    {
                        field: 'remarks',
                        title: '任务备注'
                    },
                    {
                        field: 'contractTotal',
                        title: '合同总额'
                    },
                    {
                        field: 'totalBudget',
                        title: '预算总额'
                    },
                    {
                        field: 'budgetRatio',
                        title: '预算比例',
                        formatter: function(value, row, index) {
                            const formatter = new Intl.NumberFormat('zh-CN', {
                                style: 'decimal',
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })

                            return formatter.format(value*100)+"%";
                        }
                    },
                    {
                        field: 'costRatio',
                        title: '成本比例',
                        formatter: function(value, row, index) {
                            const formatter = new Intl.NumberFormat('zh-CN', {
                                style: 'decimal',
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })

                            return formatter.format(value*100)+"%";
                        }
                    },
                    {
                        field: 'settleStatusCode',
                        title: '决算状态',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(settleStatusCodeDatas, value);
                        }
                    },
                    {
                        field: 'settleRemarks',
                        title: '备注'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        width: '220px',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<div class="col-sm-12 search-collapse" style="padding-bottom: 6px !important;"><div class="select-list"><ul>')
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.modal.openTab(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 预算管理",row.projectInfo.pName,row.taskName)+'\',\''+$.common.sprintf("%stask/budget/apply?projectTaskId=%s",ctx,row.id)+'\')"><i class="fa fa-edit"></i>预算管理</a> </li>');
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="submitReview(\'' + row.id + '\')"><i class="fa fa-edit"></i>提交任务</a> </li>');
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑任务</a> </li>');
                            actions.push('<li><a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除任务</a> </li>');
                            actions.push('</ul></div></div>')

                            actions.push('<div class="col-sm-12 search-collapse" style="padding-bottom: 6px !important;"><div class="select-list"><ul>')
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.modal.openFull(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 决算申请",row.projectInfo.pName,row.taskName)+'\',\''+$.common.sprintf("%stask/settle/apply/edit/%s",ctx,row.id)+'\')"><i class="fa fa-edit"></i>决算申请</a> </li>');
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="submitReviewSettle(\'' + row.id + '\')"><i class="fa fa-edit"></i>提交决算</a> </li>');
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="manageLabor(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 劳务信息",row.projectInfo.pName,row.taskName)+'\',\''+row.id+'\')"><i class="fa fa-edit"></i>劳务信息</a> </li>');
                            actions.push('<li><a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="manageRespondent(\''+$.common.sprintf("项目[%s] -> 任务[%s] -> 受访信息",row.projectInfo.pName,row.taskName)+'\',\''+row.id+'\')"><i class="fa fa-edit"></i>受访信息</a> </li>');
                            actions.push('</ul></div></div>')
                            return actions.join('');
                        }
                    }]
                };
            $.table.init(options);
        });

        function manageLabor(title,id){
            $.modal.openTab(title,ctx + "task/labor?projectTaskId="+id);
        }

        function manageRespondent(title,id){
            $.modal.openTab(title,ctx + "task/respondent?projectTaskId="+id);
        }

        function submitReview(id) {
            $.modal.confirm("确定要提交审核吗？", function() {
                $.operate.post(ctx + "task/"+id+"/review?review=[[${review}]]");
            })
        }

        function submitReviewSettle(id) {
            $.modal.confirm("确定要提交审核吗？", function() {
                $.operate.post(ctx + "task/settle/"+id+"/review?review=[[${review}]]");
            })
        }
    </script>
</body>
</html>