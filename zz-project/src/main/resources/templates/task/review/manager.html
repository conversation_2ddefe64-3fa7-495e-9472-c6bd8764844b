<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('项目阶段任务列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var applyStatusCodeDatas = [[${@dict.getType('zzpm_apply_status_format')}]];
        var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
        var prefix = ctx + "task/review/manager";

        $(function() {
            var options = {
                url: prefix + "?orderByColumn=projectInfoId,applyTime desc",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目阶段任务",
                columns: [{
                    checkbox: true
                    },
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    {
                        field: 'projectInfo.pNo',
                        title: '项目编号'
                    },
                    {
                        field: 'projectInfo.pName',
                        title: '项目名称'
                    },
                    {
                        field: 'projectInfo.pTypeCode',
                        title: '项目类型',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabels(pTypeCodeDatas, value);
                        }
                    },
                    {
                        field: 'projectInfo.pNoCus',
                        title: '客户项目编号'
                    },
                    {
                        field: 'taskName',
                        title: '任务名称'
                    },
                    {
                        field: 'applicantUser.userName',
                        title: '申请人'
                    },
                    {
                        field: 'applyTime',
                        title: '申请时间'
                    },
                    {
                        field: 'applyStatusCode',
                        title: '申请状态',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(applyStatusCodeDatas, value);
                        }
                    },
                    {
                        field: 'remarks',
                        title: '备注'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="submitReview(\'' + row.id + '\')"><i class="fa fa-edit"></i>审核</a> ');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });

        function submitReview(id) {
            var options = {
                title: '审核',
                width: "100%",
                height: "100%",
                url: ctx + "task/"+id+"/review?review=[[${review}]]",
                btn: 0,
                full: true
            };
            $.modal.openOptions(options);
        }
    </script>
</body>
</html>