<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目阶段任务')" />
    <th:block th:include="include :: footer" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-horizontal m">
            <th:block th:include="~{fragment/project.html :: project(${projectTasks.projectInfo})}"/>
            <th:block th:include="~{fragment/project.html :: task(${projectTasks})}"/>
            <th:block th:include="~{fragment/project.html :: review}"/>
        </div>
    </div>
    <script th:inline="javascript">
        var successStatus = [[${review}]];
        function submitHandler(pass) {
            if ($.validate.form()) {
                if (pass) {
                    $("input[name='review']").val(successStatus);
                } else {
                    if ($.common.isEmpty($('[name="remarks"]').val())) {
                        $.modal.alertError("请填写拒绝理由");
                        return;
                    }
                    $("input[name='review']").val(-2);
                }
                $.operate.save(ctx + "task/[(${projectTasks.id})]/review", $('#form-review-edit').serialize());
            }
        }
    </script>
</body>
</html>