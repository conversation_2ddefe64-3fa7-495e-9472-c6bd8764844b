<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务受访者列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>电话：</label>
                                <input type="text" name="phone"/>
                            </li>
                            <li>
                                <label>身份证号：</label>
                                <input type="text" name="idNumber"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="task:respondent:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="task:respondent:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="task:respondent:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="task:respondent:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:respondent:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:respondent:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:respondent:remove')}]];
        var prefix = ctx + "task/respondent";

        var projectTaskId = [[${projectTaskId}]]; 
        $(function() {
            var options = {
                url: prefix + "/list?projectTaskId=" + projectTaskId,
                createUrl: prefix + "/add?projectTaskId=" + projectTaskId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "项目任务受访者",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectNo',
                    title: '项目编号'
                },
                {
                    field: 'projectTaskName',
                    title: '项目[任务]'
                },
                {
                    field: 'supervisorName',
                    title: '督导'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'phone',
                    title: '电话'
                },
                {
                    field: 'idNumber',
                    title: '身份证号'
                },
                {
                    field: 'age',
                    title: '年龄'
                },
                {
                    field: 'city',
                    title: '所在城市'
                },
                {
                    field: 'marital',
                    title: '婚姻情况'
                },
                {
                    field: 'kids',
                    title: '小孩情况'
                },
                {
                    field: 'householdIncome',
                    title: '家庭收入'
                },
                {
                    field: 'occupation',
                    title: '行业+职业'
                },
                {
                    field: 'projectType',
                    title: '项目类型'
                },
                {
                    field: 'contactPerson',
                    title: '联络员'
                },
                {
                    field: 'involvedCategories',
                    title: '项目涉及品类'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果姓名和手机号相同的记录已经存在，更新这条数据。"> 是否更新已经存在的受访者数据
            <input type="hidden" id="projectTaskId" name="projectTaskId" th:value="${projectTaskId}">
            &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>