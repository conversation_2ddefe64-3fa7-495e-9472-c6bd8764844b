<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务受访者列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>电话：</label>
                                <input type="text" name="phone"/>
                            </li>
                            <li>
                                <label>身份证号：</label>
                                <input type="text" name="idNumber"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:respondent:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:respondent:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:respondent:remove')}]];
        var prefix = ctx + "task/respondent";

        var projectTaskId = [[${projectTaskId}]];
        $(function() {
            var options = {
                url: prefix + "/list?projectTaskId=" + projectTaskId,
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目任务受访者",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'phone',
                    title: '电话'
                },
                {
                    field: 'idNumber',
                    title: '身份证号'
                },
                {
                    field: 'age',
                    title: '年龄'
                },
                {
                    field: 'city',
                    title: '所在城市'
                },
                {
                    field: 'marital',
                    title: '婚姻情况'
                },
                {
                    field: 'kids',
                    title: '小孩情况'
                },
                {
                    field: 'householdIncome',
                    title: '家庭收入'
                },
                {
                    field: 'occupation',
                    title: '行业+职业'
                },
                {
                    field: 'projectType',
                    title: '项目类型'
                },
                {
                    field: 'contactPerson',
                    title: '联络员'
                },
                {
                    field: 'involvedCategories',
                    title: '项目涉及品类'
                },
                {
                    field: 'remarks',
                    title: '备注'
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>