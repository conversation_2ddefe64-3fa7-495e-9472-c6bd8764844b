<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目任务受访者')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-respondent-edit" th:object="${projectTaskRespondent}">
            <input name="id" th:field="*{id}" type="hidden">
            <input name="projectTaskId" th:field="*{projectTaskId}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">姓名：</label>
                    <div class="col-sm-8">
                        <input name="name" th:field="*{name}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">电话：</label>
                    <div class="col-sm-8">
                        <input name="phone" th:field="*{phone}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">身份证号：</label>
                    <div class="col-sm-8">
                        <input name="idNumber" th:field="*{idNumber}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">年龄：</label>
                    <div class="col-sm-8">
                        <input name="age" th:field="*{age}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">所在城市：</label>
                    <div class="col-sm-8">
                        <input name="city" th:field="*{city}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">婚姻情况：</label>
                    <div class="col-sm-8">
                        <input name="marital" th:field="*{marital}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">小孩情况：</label>
                    <div class="col-sm-8">
                        <input name="kids" th:field="*{kids}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">家庭收入：</label>
                    <div class="col-sm-8">
                        <input name="householdIncome" th:field="*{householdIncome}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">行业+职业：</label>
                    <div class="col-sm-8">
                        <input name="occupation" th:field="*{occupation}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">联络员：</label>
                    <div class="col-sm-8">
                        <input name="contactPerson" th:field="*{contactPerson}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目涉及品类：</label>
                    <div class="col-sm-8">
                        <input name="involvedCategories" th:field="*{involvedCategories}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control">[[*{remarks}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "task/respondent";
        $("#form-respondent-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-respondent-edit').serialize());
            }
        }
    </script>
</body>
</html>