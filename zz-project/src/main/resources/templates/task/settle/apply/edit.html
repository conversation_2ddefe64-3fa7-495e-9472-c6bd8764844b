<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改项目阶段任务')" />
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-horizontal m" id="form-settle-edit">
            <div id="project" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: project(${projectTasks.projectInfo})}"/>
            </div>

            <div id="task" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: task(${projectTasks})}"/>
            </div>

            <div id="budget" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: budgetList(${projectTasks.id})}"/>
            </div>

            <div id="taskTotal" class="col-xs-12">
                <th:block th:include="~{fragment/project.html :: taskTotal(${projectTasks.id})}"/>
            </div>

            <th:block th:include="~{fragment/projectedit.html :: settle(${projectTasks})}"/>
        </div>
    </div>
</body>
</html>