<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目阶段任务列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:settle:apply:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var settleStatusCodeDatas = [[${@dict.getType('zzpm_settle_status_format')}]];
    var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
    var prefix = ctx + "task/settle/review?settleStatusCode=[[${filterStatus}]]";

    $(function() {
        var options = {
            url: prefix + "&orderByColumn=projectInfoId,settleApplyTime desc",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "项目阶段任务",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectInfo.pNo',
                    title: '项目编号'
                },
                {
                    field: 'projectInfo.pName',
                    title: '项目名称'
                },
                {
                    field: 'projectInfo.pTypeCode',
                    title: '项目类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabels(pTypeCodeDatas, value);
                    }
                },
                {
                    field: 'projectInfo.pNoCus',
                    title: '客户项目编号'
                },
                {
                    field: 'taskName',
                    title: '任务名称'
                },
                {
                    field: 'totalBudget',
                    title: '预算总额'
                },
                {
                    field: 'totalLoans',
                    title: '借款总额'
                },
                {
                    field: 'contractTotal',
                    title: '合同总额(初始)'
                },
                {
                    field: 'totalCost',
                    title: '决算总额'
                },
                {
                    field: 'settleApplicantUser.userName',
                    title: '申请人'
                },
                {
                    field: 'settleApplyTime',
                    title: '申请时间'
                },
                {
                    field: 'settleStatusCode',
                    title: '决算状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(settleStatusCodeDatas, value);
                    }
                },
                {
                    field: 'settleRemarks',
                    title: '决算备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="manageLabor(\'劳务明细【'+row.projectInfo.pName+'.'+row.taskName+'】\',\''+row.id+'\')"><i class="fa fa-edit"></i>查看劳务</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="manageRespondent(\'受访明细【'+row.projectInfo.pName+'.'+row.taskName+'】\',\''+row.id+'\')"><i class="fa fa-edit"></i>查看受访</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="submitReview(\'' + row.id + '\')"><i class="fa fa-edit"></i>审核</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function manageLabor(title,id){
        $.modal.openFull(title,ctx + "task/labor?projectTaskId="+id+"&isView=1");
    }

    function manageRespondent(title,id){
        $.modal.openFull(title,ctx + "task/respondent?projectTaskId="+id+"&isView=1");
    }

    function submitReview(id) {
        var options = {
            title: '审核',
            width: "100%",
            height: "100%",
            url: ctx + "task/settle/"+id+"/review?review=[[${review}]]",
            btn: 0,
            full: true
        };
        $.modal.openOptions(options);
    }
</script>
</body>
</html>