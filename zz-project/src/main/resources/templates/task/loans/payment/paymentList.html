<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('借款待打款列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var loansStatusCodeDatas = [[${@dict.getType('zzpm_loans_status_format')}]];
    var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
    var editFlag = [[${@permission.hasPermi('task:loans:payment:edit')}]];
    var prefix = ctx + "task/loans/payment";

    $(function() {
        var options = {
            url: prefix,
            modalName: "借款打款",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectTasks.projectInfo.pNo',
                    title: '项目编号'
                },
                {
                    field: 'projectTasks.projectInfo.pName',
                    title: '项目名称'
                },
                {
                    field: 'projectTasks.projectInfo.pTypeCode',
                    title: '项目类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabels(pTypeCodeDatas, value);
                    }
                },
                {
                    field: 'projectTasks.projectInfo.pNoCus',
                    title: '客户项目编号'
                },
                {
                    field: 'projectTasks.taskName',
                    title: '任务名称'
                },
                {
                    field: 'taskBudget.applyTime',
                    title: '预算申请日期'
                },
                {
                    field: 'taskBudget.totalBudget',
                    title: '预算金额'
                },
                {
                    field: 'amount',
                    title: '借款金额'
                },
                {
                    field: 'applicantUser.userName',
                    title: '借款申请人'
                },
                {
                    field: 'applyTime',
                    title: '借款时间'
                },
                {
                    field: 'loansStatusCode',
                    title: '审核状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(loansStatusCodeDatas, value);
                    }
                },
                {
                    field: 'remarks',
                    title: '借款描述'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="payment(\'' + row.id + '\')"><i class="fa fa-money"></i>打款</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function payment(id) {
        var options = {
            title: '借款打款',
            width: "100%",
            height: "100%",
            url: ctx + "task/loans/"+id+"/payment",
            btn: 0,
            full: true
        };
        $.modal.openOptions(options);
    }
</script>
</body>
</html> 