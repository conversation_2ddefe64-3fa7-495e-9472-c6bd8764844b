<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('借款打款')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <div class="form-horizontal m">
        <!-- 项目信息 -->
        <th:block th:include="~{fragment/project.html :: project(${projectTaskLoans.projectTasks.projectInfo})}"/>

        <!-- 任务信息 -->
        <th:block th:include="~{fragment/project.html :: task(${projectTaskLoans.projectTasks})}"/>

        <!-- 预算信息 -->
        <th:block th:include="~{fragment/project.html :: budget(${projectTaskLoans.taskBudget})}"/>

        <!-- 借款信息 -->
        <th:block th:include="~{fragment/project.html :: loans(${projectTaskLoans})}"/>

        <form id="form-payment-edit" th:object="${projectTaskLoans}">
            <input type="hidden" name="id" th:value="*{id}" />
            <!-- 打款信息 -->
            <div class="col-xs-12">
                <br><br>
                <h4 style="margin-bottom: 5px !important;border-bottom-width: 0px !important;" class="form-header h4">填写打款信息</h4>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label">打款日期：</label>
                    <div class="col-sm-8" style="width:82.5%; !important;">
                        <div class="input-group date">
                            <input name="paymentDate" class="form-control form-control-datepicker" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label">打款备注：</label>
                    <div class="col-sm-8" style="width:82.5%; !important;">
                        <textarea name="paymentRemarks" class="form-control" style="width: 100%;height: 100px;" placeholder="请输入打款备注..."></textarea>
                    </div>
                </div>
                
                <div class="layui-layer-btn" style="margin-top: 10px; text-align: right;">
                    <a class="layui-layer-btn0" onclick="submitHandler()"><i class="fa fa-check"></i> 确认打款</a>
                </div>
            </div>
        </form>
    </div>
    </div>

    <script th:inline="javascript">
        var loansId = [[${projectTaskLoans.id}]];
        var prefix = ctx + "task/loans/"+loansId+"/payment";
        
        $("#form-payment-edit").validate({
            rules: {
                paymentDate: {
                    required: true
                }
            }
        });
        
        $('.form-control-datepicker').datetimepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            minView: 2,
            todayBtn: 'linked',
            language: 'zh-CN'
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-payment-edit').serialize());
            }
        }
    </script>
</body>
</html> 