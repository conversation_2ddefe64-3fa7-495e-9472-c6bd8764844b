<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目任务借款列表')" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <th:block th:if="${taskBudget != null}">
                <div class="col-sm-12 search-collapse form-horizontal">
                    <div th:replace="~{fragment/project.html :: taskTotal(${taskBudget.projectTasks.id})}"></div>
                </div>
            </th:block>

            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input name="projectTaskBudgetId" th:value="${taskBudget.id}" type="hidden">
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="task:loans:apply:add">
                    <i class="fa fa-plus"></i> 新增借款
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:loans:apply:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:loans:apply:remove')}]];
        var loansStatusCodeDatas = [[${@dict.getType('zzpm_loans_status_format')}]];
        var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
        var prefix = ctx + "task/loans/apply";

        var projectTaskBudgetId = [[${taskBudget.id}]];
        $(function() {
            var options = {
                url: prefix + "?orderByColumn=projectTaskId,projectTaskBudgetId,applyTime desc",
                createUrl: prefix + "/add?projectTaskBudgetId=" + projectTaskBudgetId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目任务借款",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'amount',
                    title: '申请借款金额'
                },
                {
                    field: 'applyTime',
                    title: '申请时间'
                },
                {
                    field: 'loansStatusCode',
                    title: '审核状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(loansStatusCodeDatas, value);
                    }
                },
                {
                    field: 'remarks',
                    title: '借款描述'
                },
                {
                    field: 'fileUrl',
                    title: '附件',
                    formatter: function(value, row, index) {
                        if (value && row.fileName) {
                            return '<a href="' + value + '" target="_blank"><i class="fa fa-download"></i> ' + row.fileName + '</a>';
                        } else {
                            return '<span class="text-muted">无附件</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        if (row.loansStatusCode == 0 || row.loansStatusCode == -2) {
                            // 当loansStatusCode为0或-2时，显示所有三个按钮
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="submitReview(\'' + row.id + '\')"><i class="fa fa-edit"></i>提交借款</a> ');
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑借款</a> ');
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除借款</a>');
                        } else if (row.loansStatusCode == -1) {
                            // 当loansStatusCode为-1时，仅显示删除按钮
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除借款</a>');
                        }
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function submitReview(id) {
            $.modal.confirm("确定要提交审核吗？", function() {
                $.operate.post(ctx + "task/loans/"+id+"/review?review=[[${review}]]");
            })
        }
        
        // 文件上传相关函数
        function initFileInput(fileId, fileUrl) {
            var fileInput = $("#" + fileId);
            
            // 如果已经初始化过，先销毁
            if (fileInput.data("fileinput")) {
                fileInput.fileinput("destroy");
            }
            
            var initialPreview = [];
            var initialPreviewConfig = [];
            
            // 如果有已上传的文件，显示预览
            if (fileUrl) {
                initialPreview = [fileUrl];
                initialPreviewConfig = [{
                    caption: "已上传文件",
                    downloadUrl: fileUrl,
                    key: fileId
                }];
            }
            
            fileInput.fileinput({
                language: 'zh',
                uploadUrl: ctx + "pmrsch/common/file/upload",
                maxFileCount: 1,
                showCaption: true,
                showPreview: true,
                showUpload: true,
                showRemove: true,
                autoReplace: true,
                uploadAsync: true,
                initialPreview: initialPreview,
                initialPreviewAsData: true,
                initialPreviewConfig: initialPreviewConfig,
                allowedFileExtensions: ['doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png', 'xls', 'xlsx', 'zip'],
                previewFileIcon: '<i class="fa fa-file"></i>'
            }).on('fileuploaded', function(event, data, previewId, index) {
                // 获取上传成功后的返回数据
                var response = data.response;
                if (response && response.code == 0) {
                    var fileInfo = response.data;
                    // 将文件ID设置到表单中
                    $("#" + fileId + "_id").val(fileInfo.fileId);
                    $.modal.msgSuccess("文件上传成功");
                } else {
                    $.modal.msgError("文件上传失败：" + response.msg);
                }
            }).on('fileerror', function(event, data, msg) {
                $.modal.msgError("文件上传失败：" + msg);
            }).on('fileclear', function(event) {
                // 清空文件时，同时清空隐藏的文件ID
                $("#" + fileId + "_id").val('');
            });
        }
    </script>
</body>

</html>