<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('专家咨询记录列表')" />
    <style type="text/css">
        .bootstrap-table .fixed-table-container .table thead th {
            white-space: nowrap;
        }
        #bootstrap-table {
            table-layout: fixed;
        }
        .bootstrap-table .fixed-table-container {
            overflow-x: auto;
        }
    </style>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>咨询日期范围：</label>
                                <input type="text" class="time-input" placeholder="开始日期" name="params[beginConsultationDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="结束日期" name="params[endConsultationDate]"/>
                            </li>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="projectNo" placeholder="请输入项目编号"/>
                            </li>
                            <li>
                                <label>联系人：</label>
                                <input type="text" name="contactPerson"/>
                            </li>
                            <li>
                                <label>费用类型：</label>
                                <select name="sampleTypeCode" th:with="type=${@dict.getType('zzpm_sample_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>开票状态：</label>
                                <select name="invoiceStatusCode" th:with="type=${@dict.getType('zzpm_invoice_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="开始日期" name="params[beginCreatedAt]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="结束日期" name="params[endCreatedAt]"/>
                            </li>
                            <li>
                                <label>客户名称：</label>
                                <input type="text" name="params[customerName]"/>
                            </li>
                            <li>
                                <label>督导名称：</label>
                                <input type="text" name="params[supervisorName]"/>
                            </li>
                            <li>
                                <label>所属月份：</label>
                                <input type="text" name="params[ofMouth]" placeholder="请输入所属月份"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="params[pName]" placeholder="请输入项目名称"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="task:consultation:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="task:consultation:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="task:consultation:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="task:consultation:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:consultation:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('task:consultation:edit')}]];
        var removeFlag = [[${@permission.hasPermi('task:consultation:remove')}]];
        var invoiceStatusCodeDatas = [[${@dict.getType('zzpm_invoice_status')}]];
        var prefix = ctx + "task/consultation";

        $(function() {
            var options = {
                url: prefix + "/list?orderByColumn=createdAt desc",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "专家咨询记录",
                tableLayout: 'fixed',
                widthUnit: 'px',
                showFooter: true,
                footerStyle: footerStyle,
                responseHandler: function(res) {
                    // Store the footer data in a global variable for access in footerFormatter
                    window.consultationFooterData = res.otherData ? res.otherData.footer : null;
                    return res;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'customer.name',
                    title: '客户名称',
                    width: 130,
                    footerFormatter: function() {
                        return '总计';
                    }
                },
                {
                    field: 'consultationDate',
                    title: '咨询日期',
                    width: 100
                },
                {
                    field: 'consultationTime',
                    title: '咨询时间',
                    width: 80
                },
                {
                    field: 'projectInfo.pName',
                    title: '所属项目',
                    width: 130
                },
                {
                    field: 'projectInfo.pNo',
                    title: '所属项目编号',
                    width: 100
                },
                {
                    field: 'projectInfo.ofMouth',
                    title: '所属月份',
                    width: 100
                },
                {
                    field: 'expertInfo.expertName',
                    title: '专家名称',
                    width: 80
                },
                {
                    field: 'expertInfo.expertType',
                    title: '专家类型',
                    width: 80
                },
                {
                    field: 'sampleTypeCode',
                    title: '费用类型',
                    width: 80,
                    formatter: function(value, row, index) {
                        var datas = [[${@dict.getType('zzpm_sample_type')}]];
                        return $.table.selectDictLabel(datas, value);
                    }
                },
                {
                    field: 'unitPrice',
                    title: '单价',
                    width: 80,
                    footerFormatter: function(data) {
                        // Use statistics from backend if available
                        try {
                            if (window.consultationFooterData && window.consultationFooterData.unitPriceTotal !== undefined) {
                                return parseFloat(window.consultationFooterData.unitPriceTotal).toFixed(2);
                            }
                        } catch (e) {
                            console.error("Error in unitPrice footerFormatter:", e);
                        }
                        return '0.00';
                    }
                },
                {
                    field: 'expertInfo.position',
                    title: '职级',
                    width: 80
                },
                {
                    field: 'consultationDuration',
                    title: '定量数量或访谈时长(min)',
                    width: 180
                },
                {
                    field: 'unitPrice',
                    title: '单价(RMB)',
                    width: 80,
                    footerFormatter: function(data) {
                        // Use statistics from backend if available
                        try {
                            if (window.consultationFooterData && window.consultationFooterData.unitPriceTotal !== undefined) {
                                return parseFloat(window.consultationFooterData.unitPriceTotal).toFixed(2);
                            }
                        } catch (e) {
                            console.error("Error in unitPrice footerFormatter:", e);
                        }
                        return '0.00';
                    }
                },
                {
                    field: 'customerTotalCost',
                    title: '客户费用合计(RMB)',
                    width: 180,
                    formatter: function(value, row, index) {
                        return row.customerTotalCost ? row.customerTotalCost.toFixed(2) : '0.00';
                    },
                    footerFormatter: function(data) {
                        // Use statistics from backend if available
                        try {
                            if (window.consultationFooterData && window.consultationFooterData.customerTotalCostTotal !== undefined) {
                                return parseFloat(window.consultationFooterData.customerTotalCostTotal).toFixed(2);
                            }
                        } catch (e) {
                            console.error("Error in customerTotalCost footerFormatter:", e);
                        }
                        return '0.00';
                    }
                },
                {
                    field: 'projectTypeLabel',
                    title: '项目类型',
                    width: 120
                },
                {
                    field: 'expertCompanyName',
                    title: '专家所属公司',
                    width: 120
                },
                {
                    field: 'channel',
                    title: '渠道',
                    width: 80
                },
                {
                    field: 'channelPrice',
                    title: '渠道报价(RMB)',
                    width: 100,
                    footerFormatter: function(data) {
                        // Use statistics from backend if available
                        try {
                            if (window.consultationFooterData && window.consultationFooterData.channelPriceTotal !== undefined) {
                                return parseFloat(window.consultationFooterData.channelPriceTotal).toFixed(2);
                            }
                        } catch (e) {
                            console.error("Error in channelPrice footerFormatter:", e);
                        }
                        return '0.00';
                    }
                },
                {
                    field: 'channelCost',
                    title: '渠道成本(RMB)',
                    width: 150,
                    formatter: function(value, row, index) {
                        return row.channelCost ? row.channelCost.toFixed(2) : '0.00';
                    },
                    footerFormatter: function(data) {
                        // Use statistics from backend if available
                        try {
                            if (window.consultationFooterData && window.consultationFooterData.channelCostTotal !== undefined) {
                                return parseFloat(window.consultationFooterData.channelCostTotal).toFixed(2);
                            }
                        } catch (e) {
                            console.error("Error in channelCost footerFormatter:", e);
                        }
                        return '0.00';
                    }
                },
                {
                    field: 'channelCostRatio',
                    title: '渠道/费用比(%)',
                    width: 150,
                    formatter: function(value, row, index) {
                        return row.channelCostRatio ? row.channelCostRatio.toFixed(2) + '%' : '0.00%';
                    }
                },
                {
                    field: 'question',
                    title: '问题',
                    width: 120
                },
                {
                    field: 'department',
                    title: '部门',
                    width: 120
                },
                {
                    field: 'contactPerson',
                    title: '联系人',
                    width: 120
                },
                {
                    field: 'supervisorName',
                    title: '督导',
                    width: 120
                },
                {
                    field: 'contactEmail',
                    title: '联系人邮箱',
                    width: 120
                },
                {
                    field: 'remarks',
                    title: '备注',
                    width: 120
                },
                {
                    field: 'invoiceStatusCode',
                    title: '开票状态',
                    width: 120,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(invoiceStatusCodeDatas, value);
                    }
                },
                {
                    field: 'createdAt',
                    title: '创建时间',
                    width: 120
                },
                {
                    field: 'updatedAt',
                    title: '更新时间',
                    width: 120
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function footerStyle(column) {
            return {
                unitPrice: {
                    css: { color: 'red', 'font-weight': 'bold' }
                },
                customerTotalCost: {
                    css: { color: 'red', 'font-weight': 'bold' }
                },
                channelPrice: {
                    css: { color: 'red', 'font-weight': 'bold' }
                },
                channelCost: {
                    css: { color: 'red', 'font-weight': 'bold' }
                }
            }[column.field]
        }

        // Debug function to log the data structure
        $(document).on('post-body.bs.table', '#bootstrap-table', function() {
            console.log("Table data loaded, checking footer data structure");
            var tableData = $('#bootstrap-table').bootstrapTable('getData');
            var options = $('#bootstrap-table').bootstrapTable('getOptions');
            console.log("Table options:", options);
            if (options.otherData && options.otherData.footer) {
                console.log("Footer data found:", options.otherData.footer);
            } else {
                console.log("Footer data not found in options:", options);
            }
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果项目ID和专家ID相同的记录已经存在，更新这条数据。"> 是否更新已经存在的专家咨询记录
            &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！<br/>
            必须填写项目编号、专家姓名和专家手机号作为匹配条件。
        </font>
    </div>
</form>
</script>
</html>