<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('专家咨询账单统计列表')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-select-js" />
    <th:block th:include="include :: bootstrap-daterangepicker-css" />
    <th:block th:include="include :: bootstrap-daterangepicker-js" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li class="select-time">
                                <label>统计日期：</label>
                                <input type="text" class="time-input" id="beginTime" placeholder="开始日期" name="beginTime"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束日期" name="endTime"/>
                            </li>
                            <li>
                                <label>督导：</label>
                                <input type="text" name="userName" placeholder="请输入督导名称"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchBilling()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:consultation:billing">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "task/consultation";

        $(function() {
            var options = {
                url: prefix + "/billingList",
                exportUrl: prefix + "/exportBilling",
                modalName: "专家咨询账单统计",
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                firstLoad: false,
                showFooter: true,
                footerStyle: footerStyle,
                columns: [{
                    field: 'userId',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'userName',
                    title: '督导',
                    footerFormatter: function() {
                        return '总计';
                    }
                },
                {
                    field: 'revenue',
                    title: '营业额(RMB)',
                    formatter: function(value, row, index) {
                        return parseFloat(value).toFixed(2);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (const item of data) {
                            total += parseFloat(item.revenue || 0);
                        }
                        return total.toFixed(2);
                    }
                },
                {
                    field: 'channelCost',
                    title: '渠道成本(RMB)',
                    formatter: function(value, row, index) {
                        return parseFloat(value).toFixed(2);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (const item of data) {
                            total += parseFloat(item.channelCost || 0);
                        }
                        return total.toFixed(2);
                    }
                },
                {
                    field: 'channelCostRatio',
                    title: '渠道/费用比(%)',
                    formatter: function(value, row, index) {
                        return parseFloat(value).toFixed(2) + '%';
                    },
                    footerFormatter: function(data) {
                        var totalRatio = 0;
                        var count = 0;
                        for (const item of data) {
                            if (item.channelCostRatio) {
                                totalRatio += parseFloat(item.channelCostRatio);
                                count++;
                            }
                        }
                        return count > 0 ? (totalRatio / count).toFixed(2) + '%' : '0.00%';
                    }
                }]
            };
            $.table.init(options);

            // 设置初始日期
            var currentDate = new Date();
            var firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            var lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

            $("#beginTime").val(formatDate(firstDay));
            $("#endTime").val(formatDate(lastDay));
        });

        // 格式化日期为 yyyy-MM-dd
        function formatDate(date) {
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var day = date.getDate().toString().padStart(2, '0');
            return year + '-' + month + '-' + day;
        }

        function searchBilling() {
            $.table.search();
        }

        function footerStyle(column) {
            return {
                revenue: {
                    css: { color: 'red', 'font-weight': 'bold' }
                },
                channelCost: {
                    css: { color: 'red', 'font-weight': 'bold' }
                },
                channelCostRatio: {
                    css: { color: 'red', 'font-weight': 'bold' }
                }
            }[column.field]
        }
    </script>
</body>
</html>