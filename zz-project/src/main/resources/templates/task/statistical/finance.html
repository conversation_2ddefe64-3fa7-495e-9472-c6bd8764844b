<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('财务修改')" />
    <th:block th:include="include :: datetimepicker-css" />
    
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <form class="form-horizontal m" id="form-finance">
            <input name="id" th:value="${projectTasks.id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">扣款：</label>
                <div class="col-sm-8">
                    <input name="deductionAmount" th:value="${projectTasks.deductionAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">其它需要支付费用：</label>
                <div class="col-sm-8">
                    <input name="otherCost" th:value="${projectTasks.otherCost}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">己收预付费：</label>
                <div class="col-sm-8">
                    <input name="prepaidCost" th:value="${projectTasks.prepaidCost}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">项目回扣：</label>
                <div class="col-sm-8">
                    <input name="projectRebate" th:value="${projectTasks.projectRebate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">客户评议：</label>
                <div class="col-sm-8">
                    <input name="customerComment" th:value="${projectTasks.customerComment}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">费用核对日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="costCheckDate" th:value="${#dates.format(projectTasks.costCheckDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "task/statistical/finance";
        $("#form-finance").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-finance').serialize());
            }
        }
        
        $("input[name='costCheckDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html> 