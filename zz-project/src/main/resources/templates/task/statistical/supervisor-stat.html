<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('督导统计信息')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>督导姓名：</label>
                            <input type="text" name="supervisorName"/>
                        </li>
                        <li>
                            <label>申请日期：</label>
                            <input type="text" class="time-input" name="beginDate" placeholder="开始日期"/>
                            <span>-</span>
                            <input type="text" class="time-input" name="endDate" placeholder="结束日期"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: datetimepicker-js" />
<script th:inline="javascript">
    var prefix = ctx + "task/statistical/supervisor/stat";

    // 通用格式化金额函数
    function formatCurrency(value) {
        if (value == null || value === undefined || value === '') {
            return '¥0.00';
        }
        
        try {
            // 确保值是数字
            let numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return '¥0.00';
            }
            return '¥' + numValue.toFixed(2);
        } catch (e) {
            return '¥0.00';
        }
    }

    // 格式化百分比函数
    function formatPercent(value) {
        if (value == null || value === undefined || value === '') {
            return '0.00%';
        }
        
        try {
            // 确保值是数字
            let numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return '0.00%';
            }
            return (numValue * 100).toFixed(2) + '%';
        } catch (e) {
            return '0.00%';
        }
    }

    $(function() {
        // 初始化日期选择器
        $('.time-input').datetimepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            minView: 2,
            language: 'zh-CN'
        });
        
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "督导统计信息",
            showRefresh: true,
            showColumns: true,
            rememberSelected: true,
            showFooter: true,
            firstLoad: false,
            columns: [
                {
                    field: 'supervisorName',
                    title: '督导姓名',
                    footerFormatter: function() {
                        return '总计';
                    }
                },
                {
                    field: 'projectCount',
                    title: '项目数量',
                    footerFormatter: function(data) {
                        var totalCount = 0;
                        for (var i = 0; i < data.length; i++) {
                            totalCount += data[i].projectCount || 0;
                        }
                        return totalCount;
                    }
                },
                {
                    field: 'contractAmount',
                    title: '合同金额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].contractAmount || 0;
                        }
                        return formatCurrency(total);
                    }
                },
                {
                    field: 'finalContractAmount',
                    title: '最终合同额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].finalContractAmount || 0;
                        }
                        return formatCurrency(total);
                    }
                },
                {
                    field: 'businessAmount',
                    title: '营业额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].businessAmount || 0;
                        }
                        return formatCurrency(total);
                    }
                },
                {
                    field: 'budgetTotal',
                    title: '预算总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].budgetTotal || 0;
                        }
                        return formatCurrency(total);
                    }
                },
                {
                    field: 'loansTotal',
                    title: '借款总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].loansTotal || 0;
                        }
                        return formatCurrency(total);
                    }
                },
                {
                    field: 'settleTotal',
                    title: '决算总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].settleTotal || 0;
                        }
                        return formatCurrency(total);
                    }
                },
                {
                    field: 'finalProfitRatio',
                    title: '利润率',
                    formatter: function(value, row, index) {
                        return formatPercent(value);
                    },
                    footerFormatter: function(data) {
                        var total = 0;
                        var count = 0;
                        for (var i = 0; i < data.length; i++) {
                            if (data[i].finalProfitRatio) {
                                total += data[i].finalProfitRatio;
                                count++;
                            }
                        }
                        return count > 0 ? formatPercent(total / count) : '0.00%';
                    }
                }
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html> 