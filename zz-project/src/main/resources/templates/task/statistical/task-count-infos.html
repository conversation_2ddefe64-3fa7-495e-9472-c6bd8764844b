<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目统计信息')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>公司项目编号：</label>
                            <input type="text" name="companyProjectNo"/>
                        </li>
                        <li>
                            <label>项目名称：</label>
                            <input type="text" name="projectName"/>
                        </li>
                        <li>
                            <label>客户项目编号：</label>
                            <input type="text" name="customerProjectNo"/>
                        </li>
                        <li>
                            <label>客户名称：</label>
                            <input type="text" name="customerName"/>
                        </li>
                        <li>
                            <label>督导：</label>
                            <input type="text" name="taskAppler"/>
                        </li>
                        <li>
                            <label>执行城市：</label>
                            <input type="text" name="executionCity"/>
                        </li>
                        <li>
                            <label>所属月份：</label>
                            <input type="text" name="ofMouth"/>
                        </li>
                        <li>
                            <label>申请状态：</label>
                            <select name="applyStatusCode" th:with="type=${@dict.getType('zzpm_apply_status_format')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>决算状态：</label>
                            <select name="settleStatusCode" th:with="type=${@dict.getType('zzpm_settle_status_format')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>申请日期：</label>
                            <input type="text" class="time-input" name="params[applyNoDateB]" placeholder="开始日期"/>
                            <span>-</span>
                            <input type="text" class="time-input" name="params[applyNoDateE]" placeholder="结束日期"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary single disabled" id="invoiceBtn" onclick="handleInvoice()">
                <i class="fa fa-file-text-o"></i> 开票
            </a>
            <a class="btn btn-info single disabled" id="paymentBtn" onclick="handlePayment()">
                <i class="fa fa-credit-card"></i> 收款
            </a>
            <a class="btn btn-warning single disabled" id="financeBtn" onclick="handleFinance()">
                <i class="fa fa-money"></i> 财务修改
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "task/statistical";
    var applyStatusCodeDatas = [[${@dict.getType('zzpm_apply_status_format')}]];
    var settleStatusCodeDatas = [[${@dict.getType('zzpm_settle_status_format')}]];

    // 通用格式化金额函数
    function formatCurrency(value) {
        if (value == null || value === undefined || value === '') {
            return '¥0.00';
        }
        
        try {
            // 确保值是数字
            let numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return '¥0.00';
            }
            return '¥' + numValue.toFixed(2);
        } catch (e) {
            return '¥0.00';
        }
    }
    
    // 通用格式化百分比函数
    function formatPercent(value) {
        if (value == null || value === undefined || value === '') {
            return '0.00%';
        }
        
        try {
            // 确保值是数字
            let numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return '0.00%';
            }
            return numValue.toFixed(2) + '%';
        } catch (e) {
            return '0.00%';
        }
    }
    
    // 打开项目详情页面
    function openDetail(taskId) {
        var url = ctx + "task/statistical/detail?taskId=" + taskId;
        $.modal.openTab('项目详情', url);
    }

    $(function() {
        var options = {
            url: prefix + "/task-count-infos",
            exportUrl: prefix + "/task-count-infos/export",
            modalName: "项目统计信息",
            showRefresh: true,
            showColumns: true,
            clickToSelect: true,
            rememberSelected: true,
            virtualScroll: true,
            pageSize: 5,
            height: '200px',
            onCheck: function(row) {
                toggleActions();
            },
            onUncheck: function(row) {
                toggleActions();
            },
            onCheckAll: function(rows) {
                toggleActions();
            },
            onUncheckAll: function(rows) {
                toggleActions();
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'taskId',
                    title: 'TaskID',
                    visible: false
                },
                {
                    field: 'taskAppler',
                    title: '项目督导',
                    width: '700px',
                },
                {
                    field: 'companyProjectNo',
                    title: '公司项目编号',
                    formatter: function(value, row, index) {
                        if (row.taskId) {
                            return '<a href="javascript:void(0)" onclick="openDetail(\'' + row.taskId + '\')">' + value + '</a>';
                        } else {
                            return value;
                        }
                    }
                },
                {
                    field: 'customerProjectNo',
                    title: '客户项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'customerName',
                    title: '客户名称'
                },
                {
                    field: 'applyStatusCode',
                    title: '申请状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(applyStatusCodeDatas, value);
                    }
                },
                {
                    field: 'settleStatusCode',
                    title: '决算状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(settleStatusCodeDatas, value);
                    }
                },
                {
                    field: 'customerAddress',
                    title: '客户地址'
                },
                {
                    field: 'customerContact',
                    title: '客户联系人'
                },
                {
                    field: 'contactPhone',
                    title: '联系方式'
                },
                {
                    field: 'projectType',
                    title: '项目类型'
                },
                {
                    field: 'executionCity',
                    title: '执行城市'
                },
                {
                    field: 'applyNoDate',
                    title: '申请编号日期'
                },
                {
                    field: 'startTime',
                    title: '开始时间'
                },
                {
                    field: 'endTime',
                    title: '结束时间'
                },
                {
                    field: 'actualStartTime',
                    title: '实际开始时间'
                },
                {
                    field: 'actualEndTime',
                    title: '实际结束时间'
                },
                {
                    field: 'sampleQuantity',
                    title: '样本量'
                },
                {
                    field: 'sampleAmount',
                    title: '样本额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'otherQuantity',
                    title: '其它费用量'
                },
                {
                    field: 'otherAmount',
                    title: '其它费用额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'sampleQuantityFinish',
                    title: '样本结算量'
                },
                {
                    field: 'sampleAmountFinish',
                    title: '样本结算额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'sampleQuantityFinishAdd',
                    title: '样本补充结算量'
                },
                {
                    field: 'sampleAmountFinishAdd',
                    title: '样本补充结算额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'otherQuantityFinish',
                    title: '其它费用结算量'
                },
                {
                    field: 'otherAmountFinish',
                    title: '其它费用结算额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'otherQuantityFinishAdd',
                    title: '其它费用补充结算量'
                },
                {
                    field: 'otherAmountFinishAdd',
                    title: '其它费用补充结算额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'contractAmount',
                    title: '合同金额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'finalContractAmount',
                    title: '最终合同额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'actualContractDifference',
                    title: '实际合同差额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'budgtApplyTime',
                    title: '预算申请时间'
                },
                {
                    field: 'budgetTotal',
                    title: '预算总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'loansApplyTime',
                    title: '借款申请时间'
                },
                {
                    field: 'loansTotal',
                    title: '借款总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'settleApplyTime',
                    title: '决算申请时间'
                },
                {
                    field: 'settleTotal',
                    title: '决算总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'invoiceTime',
                    title: '开票时间'
                },
                {
                    field: 'invoiceTotal',
                    title: '开票总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'collectTime',
                    title: '收款时间'
                },
                {
                    field: 'collectionTotal',
                    title: '收款总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'deductionAmount',
                    title: '扣款',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'deductionRatio',
                    title: '扣款比例',
                    formatter: function(value, row, index) {
                        return formatPercent(value);
                    }
                },
                {
                    field: 'costCheckDate',
                    title: '费用核对日期'
                },
                {
                    field: 'finalProfitRatio',
                    title: '决算比例（利润）',
                    formatter: function(value, row, index) {
                        return formatPercent(value);
                    }
                },
                {
                    field: 'settleDifference',
                    title: '预决算差额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'otherCost',
                    title: '其它需要支付费用',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'prepaidCost',
                    title: '己收预付费',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'businessAmount',
                    title: '营业额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'receivables',
                    title: '应收款',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'received',
                    title: '已收',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'unpaidTotal',
                    title: '未开票总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'unpaidCollectionTotal',
                    title: '未收款总额',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'projectRebate',
                    title: '项目回扣',
                    formatter: function(value, row, index) {
                        return formatCurrency(value);
                    }
                },
                {
                    field: 'customerComment',
                    title: '客户评议'
                },
                {
                    field: 'ofMouth',
                    title: '所属月份'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="openInvoice(\'' + row.taskId + '\')"><i class="fa fa-file-text-o"></i> 开票</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="openPayment(\'' + row.taskId + '\')"><i class="fa fa-credit-card"></i> 收款</a>');
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(options);
    });
    
    // 更新工具栏按钮状态
    function updateToolbarButtons(rows) {
        var $invoiceBtn = $('#invoiceBtn');
        var $paymentBtn = $('#paymentBtn');
        
        if (rows && rows.length === 1) {
            $invoiceBtn.prop('disabled', false);
            $paymentBtn.prop('disabled', false);
        } else {
            $invoiceBtn.prop('disabled', true);
            $paymentBtn.prop('disabled', true);
        }
    }
    
    // 处理开票操作
    function handleInvoice() {
        var rows = $.table.selectColumns("taskId");
        if (rows && rows.length === 1) {
            openInvoice(rows[0]);
        }
    }
    
    // 处理收款操作
    function handlePayment() {
        var rows = $.table.selectColumns("taskId");
        if (rows && rows.length === 1) {
            openPayment(rows[0]);
        }
    }
    
    // 打开开票操作弹窗
    function openInvoice(taskId) {
        $.modal.openTab("项目开票",ctx + "task/invoicelogs?orderByColumn=created_at desc&projectTaskId="+taskId);
    }
    
    // 打开收款操作弹窗
    function openPayment(taskId) {
        $.modal.openTab("项目收款",ctx + "task/paycollectlogs?orderByColumn=created_at desc&projectTaskId="+taskId);
    }

    // 处理财务修改操作
    function handleFinance() {
        var rows = $.table.selectColumns("taskId");
        if (rows && rows.length === 1) {
            openFinance(rows[0]);
        }
    }
    
    // 打开财务修改操作弹窗
    function openFinance(taskId) {
        $.modal.open("财务修改",ctx + "task/statistical/finance?taskId=" + taskId);
    }

    // 添加新函数控制按钮状态
    function toggleActions() {
        var rows = $.table.selectColumns("taskId");
        var num = rows.length;
        $('.single').toggleClass('disabled', num!=1);
    }
</script>
</body>
</html> 