<div th:fragment="project(projectInfo)">
    <div class="col-xs-12">
        <h4 class="form-header h4" th:text="'项目信息【'+${projectInfo != null ? projectInfo.pName : ''}+'】'"></h4>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <label class="col-sm-4 control-label">项目编号：</label>
            <div class="col-sm-8">
                <input th:value="${projectInfo != null ? projectInfo.pNo : ''}" class="form-control" type="text" readonly>
            </div>
        </div>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <label class="col-sm-4 control-label">客户项目编号：</label>
            <div class="col-sm-8">
                <input th:value="${projectInfo != null ? projectInfo.pNoCus : ''}" class="form-control" type="text" readonly>
            </div>
        </div>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <label class="col-sm-4 control-label">客户名称：</label>
            <div class="col-sm-8">
                <input th:value="${projectInfo != null ? projectInfo.pName : ''}" class="form-control" type="text" readonly>
            </div>
        </div>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <label class="col-sm-4 control-label">项目类型：</label>
            <div class="col-sm-8" th:with="type=${@dict.getType('zzpm_p_type')}">
                <label th:each="dict : ${type}" class="check-box">
                    <input type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}"
                           th:attr="checked=${(projectInfo != null ? projectInfo.pTypeCode.contains(dict.dictValue) : false) ? true:false}" readonly
                           disabled>
                </label>
            </div>
        </div>
    </div>
    <div class="col-xs-12">
        <div class="form-group">
            <label class="col-sm-2 control-label">项目简介：</label>
            <div class="col-sm-8" style="width:82.5%; !important;">
                <textarea class="form-control" style="width: 100%;height: 100px;" th:text = "${projectInfo != null ? projectInfo.remarks : ''}" readonly></textarea>
            </div>
        </div>
    </div>
</div>

<div th:fragment="task(projectTasks)">
    <div class="col-xs-12">
        <h4 class="form-header h4" th:text="'项目任务【'+${projectTasks != null ? projectTasks.taskName : ''}+'】'"></h4>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">客户联系人：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? projectTasks.cusContactPerson : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">客户联系电话：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ?  projectTasks.cusContactPhone : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>

        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">申请人：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? projectTasks.applicantUser.userName : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">申请日期：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? #dates.format(projectTasks.applyTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">开始时间：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? #dates.format(projectTasks.startTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">结束时间：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? #dates.format(projectTasks.endTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">执行城市：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? projectTasks.executionCity : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xs-12">
        <div class="form-group">
            <label class="col-sm-2 control-label">任务描述：</label>
            <div class="col-sm-8" style="width:82.5%; !important;">
                <textarea th:text="${projectTasks != null ? projectTasks.remarks : ''}" class="form-control" style="width: 100%;height: 100px;" type="text" readonly></textarea>
            </div>
        </div>
    </div>
    <div class="col-xs-12">
        <label class="col-sm-2 control-label">合同费用：</label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
            <table id="bootstrap-table-sample"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var sampleTypeCodeDatas = [[${@dict.getType('zzpm_sample_type')}]];
        $(function () {
            var data = /*[[${projectTasks != null ? projectTasks.sampleList : {}}]]*/ [];
            var options = {
                id: "bootstrap-table-sample",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'id',
                        title: 'ID',
                        visible: false
                    },
                    {
                        field: 'sampleTypeCode',
                        align: 'center',
                        width: '150px',
                        title: "费用类型",
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(sampleTypeCodeDatas, value);
                        }
                    },
                    {
                        field: 'sampleName',
                        align: 'center',
                        width: '300px',
                        title: '费用名称'
                    },
                    {
                        field: 'samplePrice',
                        align: 'center',
                        width: '100px',
                        title: '单价'
                    },
                    {
                        field: 'sampleQuantity',
                        align: 'center',
                        width: '150px',
                        title: '数量'
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        width: '150px',
                        title: '小计',
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                total += parseFloat(value[i].subtotal);
                            }
                            return "合计：" + total;
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注'
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

<div th:fragment="budget(projectTaskBudget)">
    <div class="col-xs-12">
        <br><br>
        <h4 class="form-header h4" th:text="${'任务预算'}"></h4>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">申请人：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTaskBudget != null ? projectTaskBudget.applicantUser.userName : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">申请日期：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTaskBudget != null ? #dates.format(projectTaskBudget.applyTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xs-12">
        <div class="form-group">
            <label class="col-sm-2 control-label">预算描述：</label>
            <div class="col-sm-8" style="width:82.5%; !important;">
                <textarea th:text = "${projectTaskBudget != null ? projectTaskBudget.remarks : ''}" class="form-control" style="width: 100%;height: 100px;" type="text" readonly></textarea>
            </div>
        </div>
    </div>

    <div class="col-xs-12">
        <label class="col-sm-2 control-label">预算明细：</label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
            <table id="bootstrap-table-budget"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var costCategorieMainCodeDatas = [[${@dict.getType('zzpm_cost_categorie_main')}]];
        var costCategorieSubCodeDatas = [[${@dict.getType('zzpm_cost_categorie_sub')}]];

        $(function () {
            var data = /*[[${projectTaskBudget != null ? projectTaskBudget.budgetDetails : {}}]]*/ [];
            var options = {
                id: "bootstrap-table-budget",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'costCategorieMainCode',
                        align: 'center',
                        width: '100px',
                        title: "费用类别",
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(costCategorieMainCodeDatas, value);
                        }
                    },
                    {
                        field: 'costCategorieSubCode',
                        align: 'center',
                        width: '100px',
                        title: '费用名称',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(costCategorieSubCodeDatas, value);
                        }
                    },
                    {
                        field: 'priceBudget',
                        align: 'center',
                        width: '100px',
                        title: '单价'
                    },
                    {
                        field: 'quantityBudget',
                        align: 'center',
                        title: '数量'
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        width: '150px',
                        title: '小计',
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                total += parseFloat(value[i].subtotal);
                            }
                            return "合计：" + total;
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注'
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

<div th:fragment="taskTotal(taskId)">
    <div class="col-xs-12">
        <label class="col-sm-2 control-label"></label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;" th:with="taskTotal=${@dataUtils.getTaskTotal(taskId)}">
            <label style="margin-right:10px;float: right;" th:text="${'初步成本比例：'+taskTotal.costRatio+'%'}"></label>
            <label style="margin-right:10px;float: right;" th:text="${'初步预算比例：'+taskTotal.budgetRatio+'%'}"></label>
            <label style="margin-right:10px;float: right;" th:text="${'借款总额：'+taskTotal.totalLoans}"></label>
            <label style="margin-right:10px;float: right;" th:text="${'借款比例：'+taskTotal.loanRatio+'%'}"></label>
            <label style="margin-right:10px;float: right;" th:text="${'预算总额：'+taskTotal.budgetTotal}"></label>
            <label style="margin-right:10px;float: right;" th:text="${'合同总额：'+taskTotal.contractTotal}"></label>
        </div>
    </div>
</div>

<div th:fragment="budgetList(taskId)">
    <div class="col-xs-12">
        <h4 class="form-header h4">项目预算</h4>
        <label class="col-sm-2 control-label"></label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
            <table id="bootstrap-table-budget-list"></table>
        </div>
    </div>
    <script th:inline="javascript">
        $(function () {
            var data = /*[[${@dataUtils.getBudgets(taskId)}]]*/ [];
            var options = {
                id: "bootstrap-table-budget-list",
                detailUrl: ctx + "pmrsch/common/budget/view/{id}",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        totalBudget: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        },
                        totalLoans: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'applicantUser.userName',
                        title: '申请人'
                    },
                    {
                        field: 'applyTime',
                        title: '申请时间'
                    },
                    {
                        field: 'totalBudget',
                        align: 'center',
                        title: '预算总额',
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                total += parseFloat(value[i].totalBudget);
                            }
                            return "预算合计：" + total;
                        }
                    },
                    {
                        field: 'totalLoans',
                        align: 'center',
                        title: '借款总额',
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                total += parseFloat(value[i].totalLoans);
                            }
                            return "借款合计：" + total;
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '预算描述'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.id + '\')"><i class="fa"></i>查看详情</a> ');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

<div th:fragment="loansList(taskBudgetId)">
    <div class="col-xs-12">
        <h4 class="form-header h4">预算借款</h4>
        <label class="col-sm-2 control-label"></label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
            <table id="bootstrap-table-loans-list"></table>
        </div>
    </div>

    <script th:inline="javascript">
        $(function () {
            var data = /*[[${@dataUtils.getTaskLoans(taskBudgetId)}]]*/ [];
            var options = {
                id: "bootstrap-table-loans-list",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        amount: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'amount',
                        title: '借款金额',
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                total += parseFloat(value[i].amount);
                            }
                            return "借款合计：" + total;
                        }
                    },
                    {
                        field: 'applicantUser.userName',
                        title: '借款申请人'
                    },
                    {
                        field: 'applyTime',
                        title: '借款时间'
                    },
                    {
                        field: 'remarks',
                        title: '借款描述'
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
</div>

<div th:fragment="loans(projectTaskLoans)">
    <div class="col-xs-12">
        <h4 class="form-header h4" th:text="${'借款'}"></h4>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">申请人：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTaskLoans != null ? projectTaskLoans.applicantUser.userName : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">申请日期：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTaskLoans != null ? #dates.format(projectTaskLoans.applyTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">借款金额：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTaskLoans != null ? projectTaskLoans.amount : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">借款说明：</label>
                <div class="col-sm-8">
                    <textarea class="form-control" readonly th:text="${projectTaskLoans != null ? projectTaskLoans.remarks : ''}"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">附件：</label>
                <div class="col-sm-8">
                    <a th:if="${projectTaskLoans != null && projectTaskLoans.fileUrl != null}" th:href="${projectTaskLoans.fileUrl}" th:text="${projectTaskLoans.fileName != null ? projectTaskLoans.fileName : '下载附件'}" target="_blank">
                        <i class="fa fa-download"></i>
                    </a>
                    <span th:if="${projectTaskLoans == null || projectTaskLoans.fileUrl == null}" class="text-muted">无附件</span>
                </div>
            </div>
        </div>
        <div class="col-xs-6" th:if="${projectTaskLoans != null && projectTaskLoans.isPayment != null && projectTaskLoans.isPayment}">
            <div class="form-group">
                <label class="col-sm-4 control-label">打款状态：</label>
                <div class="col-sm-8">
                    <input value="已打款" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6" th:if="${projectTaskLoans != null && projectTaskLoans.isPayment != null && projectTaskLoans.isPayment}">
            <div class="form-group">
                <label class="col-sm-4 control-label">打款日期：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTaskLoans != null ? #dates.format(projectTaskLoans.paymentDate, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6" th:if="${projectTaskLoans != null && projectTaskLoans.isPayment != null && projectTaskLoans.isPayment}">
            <div class="form-group">
                <label class="col-sm-4 control-label">打款备注：</label>
                <div class="col-sm-8">
                    <textarea class="form-control" readonly th:text="${projectTaskLoans != null ? projectTaskLoans.paymentRemarks : ''}"></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<div th:fragment="settle(projectTasks)">
    <div class="col-xs-12">
        <h4 class="form-header h4">决算信息</h4>
        <label class="col-sm-2 control-label">合同费用(执行情况)：</label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
            <table id="bootstrap-table-finish-sample"></table>
        </div>
    </div>

    <div class="col-xs-12">
        <label class="col-sm-2 control-label">决算明细：</label>
        <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
            <table id="bootstrap-table-settle"></table>
        </div>
    </div>

    <div class="col-xs-12">
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">提交客户确认样本量截图：</label>
                <div class="col-sm-8">
                    <a th:if="${projectTasks != null && projectTasks.sampleCostLink != null}" 
                       th:href="${projectTasks.sampleCostLink}" 
                       th:text="${projectTasks.sampleCostFileName != null ? projectTasks.sampleCostFileName : '查看文件'}" 
                       target="_blank">
                        <i class="fa fa-download"></i>
                    </a>
                    <span th:if="${projectTasks == null || projectTasks.sampleCostLink == null}" class="text-muted">无附件</span>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">归档文件：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="text" class="form-control" th:value="${projectTasks != null ? projectTasks.archiveLink : ''}" id="archiveLink" readonly>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default btn-flat" onclick="copyArchiveLink()">
                                <i class="fa fa-copy"></i> 复制
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xs-12">
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">实际开始时间：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? #dates.format(projectTasks.actualStartTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <label class="col-sm-4 control-label">实际结束时间：</label>
                <div class="col-sm-8">
                    <input th:value="${projectTasks != null ? #dates.format(projectTasks.actualEndTime, 'yyyy-MM-dd') : ''}" class="form-control" type="text" readonly>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xs-12">
        <div class="form-group">
            <label class="col-sm-2 control-label">决算描述：</label>
            <div class="col-sm-8" style="width:82.5%; !important;">
                <textarea th:text = "${projectTasks != null ? projectTasks.settleRemarks : ''}" class="form-control" style="width: 100%;height: 100px;" type="text" readonly></textarea>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var costCategorieMainCodeDatas = [[${@dict.getType('zzpm_cost_categorie_main')}]];
        var costCategorieSubCodeDatas = [[${@dict.getType('zzpm_cost_categorie_sub')}]];
        
        // 加载样本数据
        $(function() {
            var sampleTypeDatas = [[${@dict.getType('zzpm_sample_type')}]];
            var data = /*[[${projectTasks != null ? projectTasks.sampleAllList : {}}]]*/ [];
            var options = {
                id: "bootstrap-table-finish-sample",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        },
                        finishSubtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        },
                        diffSubtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'sampleTypeCode',
                        align: 'center',
                        width: '150px',
                        title: "费用类型",
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(sampleTypeDatas, value);
                        }
                    },
                    {
                        field: 'sampleName',
                        align: 'center',
                        width: '200px',
                        title: '费用名称'
                    },
                    {
                        field: 'samplePrice',
                        align: 'center',
                        width: '100px',
                        title: '单价'
                    },
                    {
                        field: 'sampleQuantity',
                        align: 'center',
                        title: '数量'
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        title: '小计',
                        formatter: function(value, row, index) {
                            return ((row.sampleQuantity || 0) * (row.samplePrice || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantity || 0;
                                var price = value[i].samplePrice || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'sampleQuantityFinish',
                        align: 'center',
                        title: '完成量'
                    },
                    {
                        field: 'finishSubtotal',
                        align: 'center',
                        title: '小计(完成量)',
                        formatter: function(value, row, index) {
                            return ((row.sampleQuantityFinish || 0) * (row.samplePrice || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantityFinish || 0;
                                var price = value[i].samplePrice || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计(完成量)：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'diffSubtotal',
                        align: 'center',
                        title: '差额(合同-完成)',
                        formatter: function (value, row, index) {
                            var quantity = row.sampleQuantity || 0;
                            var quantityFinish = row.sampleQuantityFinish || 0;
                            var price = row.samplePrice || 0;

                            var total = quantity * price;
                            var totalFinish = quantityFinish * price;

                            return (total - totalFinish).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantity || 0;
                                var quantityFinish = value[i].sampleQuantityFinish || 0;
                                var price = value[i].samplePrice || 0;

                                var subtotal = quantity * price;
                                var finishSubtotal = quantityFinish * price;

                                total += parseFloat(subtotal - finishSubtotal);
                            }
                            return "差额合计(合同-完成)：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注'
                    }
                ]
            };
            $.table.init(options);
        });

        $(function() {
            var data = /*[[${projectTasks.taskSettles}]]*/ [];
            var options = {
                id: "bootstrap-table-settle",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'costCategorieMainCode',
                        width: '150px',
                        title: '费用类型',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(costCategorieMainCodeDatas, value);
                        }
                    },
                    {
                        field: 'costCategorieSubCode',
                        width: '200px',
                        title: '费用名称',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(costCategorieSubCodeDatas, value);
                        }
                    },
                    {
                        field: 'priceBudget',
                        align: 'center',
                        width: '100px',
                        title: '预算单价'
                    },
                    {
                        field: 'quantityBudget',
                        align: 'center',
                        width: '100px',
                        title: '预算数量'
                    },
                    {
                        field: 'priceSettle',
                        align: 'center',
                        width: '100px',
                        title: '决算单价'
                    },
                    {
                        field: 'quantitySettle',
                        align: 'center',
                        title: '决算数量'
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        width: '150px',
                        title: '决算小计',
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                total += parseFloat(value[i].subtotal);
                            }
                            return "决算合计：" + total;
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注'
                    }]
            };
            $.table.init(options);
        });

        function copyArchiveLink() {
            var archiveLink = document.getElementById("archiveLink");
            archiveLink.select();
            document.execCommand("copy");
            $.modal.msgSuccess("复制成功");
        }
    </script>
</div>

<div th:fragment="review">
    <form id = "form-review-edit" onsubmit="submitHandler">
        <input name="review" type="hidden">
        <div class="col-xs-12">
            <br><br>
            <h4 style="margin-bottom: 5px !important;border-bottom-width: 0px !important;"  class="form-header h4">审核意见</h4>
            <textarea name="remarks" class="form-control" style="width: 100%;height: 100px;" placeholder="请输入审批意见..."></textarea>

            <div class="layui-layer-btn" style="margin-top: 10px">
                <a class="layui-layer-btn0" onclick="submitHandler(true)"><i class="fa fa-check"></i> 审核通过</a>
                <a class="layui-layer-btn2" onclick="submitHandler(false)"><i class="fa fa-close"></i> 审核拒绝</a>
            </div>
        </div>
    </form>
</div>

<div th:fragment="freshData">
    <script th:inline="javascript">
        function freshData(ids) {
            if(!ids || ids.length == 0){
                return;
            }

            $.modal.loading("正在加载数据，请稍后...");
            var isFinish = -1;

            var projectId = ids.length > 0 ? ids[0] : undefined;
            var taskId = ids.length > 1 ? ids[1] : undefined;
            var budgetId = ids.length > 2 ? ids[2] : undefined;

            $.ajax({
                type: "get",
                url: ctx + "pmdata/projectinfo/edit/"+projectId,
                data: {
                    "fragment":'project'
                },
                success: function(data) {
                    $("#project").html(data);
                    isFinish ++;
                    if(isFinish == (ids.length-1)){
                        $.modal.closeLoading();
                    }
                }
            });

            if($("#taskTotal")){
                $.ajax({
                    type: "get",
                    url: ctx + "task/"+projectId+"/total",
                    data: {
                        "fragment":'project'
                    },
                    success: function(data) {
                        $("#project").html(data);
                    }
                });
            }

            if(!taskId){
                return;
            }

            $.ajax({
                type: "get",
                url: ctx + "task/apply/edit/"+taskId,
                data: {
                    "fragment":'task'
                },
                success: function(data) {
                    $("#task").html(data);
                    isFinish ++;
                    if(isFinish == (ids.length-1)){
                        $.modal.closeLoading();
                    }
                }
            });

            if(!budgetId){
                return;
            }
            $.ajax({
                type: "get",
                url: ctx + "task/budget/apply/edit/"+budgetId,
                data: {
                    "fragment":'budget'
                },
                success: function(data) {
                    $("#budget").html(data);
                    isFinish ++;
                    if(isFinish == (ids.length-1)){
                        $.modal.closeLoading();
                    }
                }
            });
        }
    </script>
</div>
</div>
</div>