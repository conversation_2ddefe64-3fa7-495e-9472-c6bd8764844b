<div th:fragment="task(projectInfoId,projectTasks)">
    <form id="form-edit" onsubmit="submitHandler()">
        <input name="id" th:value="${projectTasks != null ? projectTasks.id : ''}" type="hidden">
        <div class="col-xs-12">
            <h4 class="form-header h4">***请填写任务信息***</h4>

            <th:block th:if="${projectInfoId != null && projectTasks == null}">
                <input name="projectInfoId" th:value="${projectInfoId}" type="hidden">
            </th:block>

            <th:block th:if="${projectInfoId == null && projectTasks != null}">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">请选择项目：</label>
                        <div class="col-sm-8" style="display: flex;flex-wrap: wrap;justify-content: space-between">
                            <select id="projectInfoId" name="projectInfoId" class="form-control" style="width: 90%" th:with="pinfolist=${@dataUtils.getProjects()}" required>
                                <option th:each="pinfo : ${pinfolist}" th:text="${pinfo.pName}" th:value="${pinfo.id}" th:data-id="${pinfo.id}" th:selected="${projectTasks == null ? false : pinfo.id == projectTasks.projectInfoId}"></option>
                            </select>
                            <a class="control-label" href="javascript:void(0)" onclick="$.modal.open('项目信息',ctx + 'pmdata/projectinfo')" shiro:hasPermission="pmdata:projectinfo:add">新增项目</a>
                            <!-- <a class="control-label" href="javascript:void(0)" onclick="$.modal.open('项目信息',ctx + 'pmdata/projectinfo')" shiro:hasPermission="pmdata:projectinfo:add">新增项目</a> -->
                        </div>
                    </div>
                </div>
            </th:block>

        </div>

        <div class="col-xs-12">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">任务名称：</label>
                    <div class="col-sm-8">
                        <input name="taskName" th:value="${projectTasks != null ? projectTasks.taskName : ''}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">客户联系人：</label>
                    <div class="col-sm-8">
                        <input name="cusContactPerson" th:value="${projectTasks != null ? projectTasks.cusContactPerson : ''}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">客户联系电话：</label>
                    <div class="col-sm-8">
                        <input name="cusContactPhone" th:value="${projectTasks != null ? projectTasks.cusContactPhone : ''}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">开始时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="startTime" th:value="${projectTasks != null ? #dates.format(projectTasks.startTime, 'yyyy-MM-dd') : ''}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">结束时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="endTime" th:value="${projectTasks != null ? #dates.format(projectTasks.endTime, 'yyyy-MM-dd') : ''}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">执行城市：</label>
                    <div class="col-sm-8">
                        <input name="executionCity" th:value="${projectTasks != null ? projectTasks.executionCity : ''}" class="form-control" type="text" required placeholder="请输入执行城市">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">任务描述：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" th:text="${projectTasks != null ? projectTasks.remarks : ''}" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <label class="col-sm-2 control-label">合同费用：</label>
            <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
                <button type="button" class="btn btn-white btn-sm" onclick="sample_addRow()"><i class="fa fa-plus"> 增加</i></button>
                <table id="bootstrap-table-sample"></table>
            </div>
        </div>
    </form>

    <script th:inline="javascript">
        $(function() {
            var data = /*[[${projectTasks != null ? projectTasks.sampleList : {}}]]*/ [];
            var options = {
                id: "bootstrap-table-sample",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        width: '70px',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='sampleList[%s].id' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'sampleTypeCode',
                        align: 'center',
                        width: '150px',
                        title: "费用类型",
                        formatter: function (value, row, index) {
                            var data = [{ index: index, type: value }];
                            return $("#sampleTypeTpl").tmpl(data).html();
                        }
                    },
                    {
                        field: 'sampleName',
                        align: 'center',
                        width: '300px',
                        title: '费用名称',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control sampleName' type='text' name='sampleList[%s].sampleName' value='%s' required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'samplePrice',
                        align: 'center',
                        width: '100px',
                        title: '单价',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' type='text' name='sampleList[%s].samplePrice' value='%s' oninput='freshTotal()' required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'sampleQuantity',
                        align: 'center',
                        width: '100px',
                        title: '数量',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control sampleQuantity' type='text' name='sampleList[%s].sampleQuantity' value='%s' oninput='freshTotal()'  required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        width: '150px',
                        title: '小计',
                        formatter: function(value, row, index) {
                            return ((row.sampleQuantity || 0) * (row.samplePrice || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantity || 0;
                                var price = value[i].samplePrice || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control remarks' type='text' name='sampleList[%s].remarks' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                            return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\',\'bootstrap-table-sample\')"><i class="fa fa-remove"></i>删除</a>';
                        }
                    }]
            };
            $.table.init(options);
        });

        var timeoutId;
        function freshTotal(){
            if(timeoutId){
                clearTimeout(timeoutId); // 清除之前的定时器
            }

            timeoutId = setTimeout(() => {
                sub.editRow();
            }, 1000); // 延迟500毫秒后触发
        }

        function sample_addRow() {
            var count = $("#bootstrap-table-sample").bootstrapTable('getData').length;
            var row = {
                sampleTypeCode: "",
                sampleName: "",
                samplePrice: "",
                sampleQuantity: "",
                remarks: "",
            }
            sub.addRow(row,"bootstrap-table-sample");
        }

        var prefix = ctx + "task/apply";
        $("#form-tasks-edit").validate({
            focusCleanup: true
        });

        var isNew = [[${projectTasks == null ? true : false}]];
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + (isNew ? "/add":"/edit"), $('#form-edit').serialize());
            }
        }

        $(document).ready(function() {
            $('#projectInfoId').change(function() {
                var ids = $('#projectInfoId option:selected').data('id').split('.');
                freshData(ids);
            });

            if(!$('#projectInfoId option:selected') || $('#projectInfoId option:selected').length == 0){
                return;
            }
            var ids = $('#projectInfoId option:selected').data('id').split('.');
            if(ids){
                freshData(ids);
            }
        });
        // 初始化日期选择器
        $("input[name='startTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
            
        $("input[name='endTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
    <!-- 费用类型 zzpm_sample_type-->
    <script id="sampleTypeTpl" th:inline="javascript" type="text/x-jquery-tmpl">
        <div>
            <select class='form-control' name='sampleList[${index}].sampleTypeCode' th:utext="${@dataUtils.getSelectForTableHtml('zzpm_sample_type')}" required></select>
        </div>
    </script>
</div>

<div th:fragment="budget(projectTaskId,projectTaskBudget)">
    <form id="form-edit" onsubmit="submitHandler()">
        <input name="id" th:value="${projectTaskBudget != null ? projectTaskBudget.id : ''}" type="hidden">

        <div class="col-xs-12">
            <h4 class="form-header h4">***填写预算信息***</h4>
            <th:block th:if="${projectTaskId != null && projectTaskBudget == null}">
                <input name="projectTaskId" th:value="${projectTaskId}" type="hidden">
            </th:block>
            <th:block th:if="${projectTaskId == null && projectTaskBudget != null}">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">选择项目任务：</label>
                        <div class="col-sm-8">
                            <select id="projectTaskId" name="projectTaskId" class="form-control" th:with="ptasklist=${@dataUtils.getTasks()}" required>
                                <option th:each="ptask : ${ptasklist}" th:text="${'['+ptask.projectInfo.pName+']' + ptask.taskName}" th:value="${ptask.id}" th:data-id="${ptask.projectInfo.id + '.' + ptask.id}" th:selected="${projectTasks == null ? false : ptask.id == projectTaskBudget.projectTaskId}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </th:block>
        </div>

        <div class="col-xs-12">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">预算描述：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control" th:value="${projectTaskBudget != null ? projectTaskBudget.remarks : ''}"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xs-12">
            <label class="col-sm-2 control-label">预算明细：</label>
            <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
                <button type="button" class="btn btn-white btn-sm" onclick="budget_addRow()"><i class="fa fa-plus"> 增加</i></button>
                <table id="bootstrap-table-budget"></table>
            </div>
        </div>
    </form>

    <script th:inline="javascript">
        $(function() {
            var data = /*[[${projectTaskBudget != null ? projectTaskBudget.budgetDetails : {}}]]*/ [];
            var options = {
                id: "bootstrap-table-budget",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        width: '70px',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='budgetDetails[%s].id' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'costCategorieMainCode',
                        align: 'center',
                        width: '150px',
                        title: "费用类型",
                        formatter: function (value, row, index) {
                            var data = [{ index: index, type: value }];
                            return $("#costCategorieMainTpl").tmpl(data).html();
                        }
                    },
                    {
                        field: 'costCategorieSubCode',
                        align: 'center',
                        width: '200px',
                        title: '费用名称',
                        formatter: function(value, row, index) {
                            var data = [{ index: index, type: value }];
                            return $("#costCategorieSubTpl").tmpl(data).html();
                        }
                    },
                    {
                        field: 'priceBudget',
                        align: 'center',
                        width: '100px',
                        title: '单价',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' type='text' name='budgetDetails[%s].priceBudget' oninput='freshTotal()' value='%s' required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'quantityBudget',
                        align: 'center',
                        width: '100px',
                        title: '数量',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control quantityBudget' type='text' name='budgetDetails[%s].quantityBudget' oninput='freshTotal()' value='%s' required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        width: '150px',
                        title: '小计',
                        formatter: function(value, row, index) {
                            return ((row.quantityBudget || 0) * (row.priceBudget || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].quantityBudget || 0;
                                var price = value[i].priceBudget || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control remarks' type='text' name='budgetDetails[%s].remarks' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                            return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\',\'bootstrap-table-budget\')"><i class="fa fa-remove"></i>删除</a>';
                        }
                    }]
            };
            $.table.init(options);
        });

        var timeoutId;
        function freshTotal(){
            if(timeoutId){
                clearTimeout(timeoutId); // 清除之前的定时器
            }

            timeoutId = setTimeout(() => {
                sub.editRow();
            }, 1000); // 延迟500毫秒后触发
        }

        var prefix = ctx + "task/budget/apply";
        $("#form-edit").validate({
            focusCleanup: true
        });

        var isNew = [[${projectTaskBudget == null ? true : false}]];
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + (isNew ? "/add":"/edit"), $('#form-edit').serialize());
            }
        }

        function budget_addRow() {
            var row = {
                costCategorieMainCode: "",
                costCategorieSubCode: "",
                priceBudget: "",
                quantityBudget: "",
                remarks: "",
            }
            sub.addRow(row,"bootstrap-table-budget");
        }

        $(document).ready(function() {
            $('#projectTaskId').change(function() {
                var ids = $('#projectTaskId option:selected').data('id').split('.');
                freshData(ids);
            });

            if(!$('#projectTaskId option:selected') || $('#projectTaskId option:selected').length == 0){
                return;
            }
            var ids = $('#projectTaskId option:selected').data('id').split('.');
            if(ids){
                freshData(ids);
            }
        });
    </script>

    <!-- 费用主类 zzpm_cost_categorie_main-->
    <script id="costCategorieMainTpl" th:inline="javascript" type="text/x-jquery-tmpl">
        <div>
            <select class='form-control' name='budgetDetails[${index}].costCategorieMainCode' th:utext="${@dataUtils.getSelectForTableHtml('zzpm_cost_categorie_main')}" required></select>
        </div>
    </script>

    <!-- 费用自类 zzpm_cost_categorie_sub-->
    <script id="costCategorieSubTpl" th:inline="javascript" type="text/x-jquery-tmpl">
        <div>
            <select class='form-control' name='budgetDetails[${index}].costCategorieSubCode' th:utext="${@dataUtils.getSelectForTableHtml('zzpm_cost_categorie_sub')}" required></select>
        </div>
    </script>
</div>

<div th:fragment="settle(projectTasks)">
    <form id="form-edit" onsubmit="submitHandler()">
        <input name="id" th:field="${projectTasks.id}" type="hidden">
        <div class="col-xs-12">
            <h4 class="form-header h4">***填写决算信息***</h4>
            <label class="col-sm-2 control-label">合同费用(填写执行情况)：</label>
            <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
                <button type="button" class="btn btn-white btn-sm" onclick="sample_addRow()"><i class="fa fa-plus"> 增加</i></button>
                <table id="bootstrap-table-finish-sample"></table>
            </div>
        </div>

        <div class="col-xs-12">
            <label class="col-sm-2 control-label">决算明细：</label>
            <div class="col-sm-8 select-table table-striped" style="width:82.5%; !important;">
                <button type="button" class="btn btn-white btn-sm" onclick="settle_addRow()"><i class="fa fa-plus"> 增加</i></button>
                <table id="bootstrap-table-settle"></table>
            </div>
        </div>

        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">提交客户确认样本量截图：</label>
                <div class="col-sm-8" style="width:82.5%; !important;">
                    <div class="input-group">
                        <input type="text" class="form-control" id="sampleCostFileName" readonly>
                        <input type="hidden" name="sampleCostFileId" id="sampleCostFileId" th:field="${projectTasks.sampleCostFileId}" required>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-primary" onclick="$('#sampleCostFile').click()">
                                <i class="fa fa-upload"></i> 上传
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteSampleCostFile()" id="deleteSampleCostFileBtn" style="display:none">
                                <i class="fa fa-trash"></i> 删除
                            </button>
                        </span>
                    </div>
                    <input type="file" id="sampleCostFile" style="display:none" onchange="uploadSampleCostFile(this)">
                    <div id="sampleCostFileLink" style="margin-top:5px"></div>
                </div>
            </div>
        </div>

        <div class="col-xs-12">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">实际开始时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="actualStartTime" th:value="${projectTasks != null ? #dates.format(projectTasks.actualStartTime, 'yyyy-MM-dd') : ''}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">实际结束时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="actualEndTime" th:value="${projectTasks != null ? #dates.format(projectTasks.actualEndTime, 'yyyy-MM-dd') : ''}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-2 control-label">资料归档链接：</label>
                <div class="col-sm-8" style="width:82.5%; !important;">
                    <input class="form-control" type="text" name="archiveLink" th:field="${projectTasks.archiveLink}" placeholder="请输入资料归档链接">
                </div>
            </div>
        </div>

        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-2 control-label">决算备注：</label>
                <div class="col-sm-8" style="width:82.5%; !important;">
                    <textarea class="form-control" style="width: 100%;height: 100px;" name="settleRemarks" th:field="${projectTasks.settleRemarks}"></textarea>
                </div>
            </div>
        </div>
    </form>

    <script th:inline="javascript">
        var costCategorieMainCodeDatas = [[${@dict.getType('zzpm_cost_categorie_main')}]];
        var costCategorieSubCodeDatas = [[${@dict.getType('zzpm_cost_categorie_sub')}]];
        $(function() {
            var data = /*[[${projectTasks.taskSettles}]]*/ [];
            var options = {
                id: "bootstrap-table-settle",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        width: '70px',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='taskSettles[%s].id' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'costCategorieMainCode',
                        width: '150px',
                        title: '费用类型',
                        formatter: function(value, row, index) {
                            if(row.isFromBudget == 1){
                                return $.table.selectDictLabel(costCategorieMainCodeDatas, value);
                            }else{
                                var data = [{ index: index, type: value }];
                                return $("#costCategorieMainTpl").tmpl(data).html();
                            }
                        }
                    },
                    {
                        field: 'costCategorieSubCode',
                        width: '200px',
                        title: '费用名称',
                        formatter: function(value, row, index) {
                            if(row.isFromBudget == 1){
                                return $.table.selectDictLabel(costCategorieSubCodeDatas, value);
                            }else{
                                var data = [{ index: index, type: value }];
                                return $("#costCategorieSubTpl").tmpl(data).html();
                            }
                        }
                    },
                    {
                        field: 'priceBudget',
                        align: 'center',
                        width: '100px',
                        title: '预算单价'
                    },
                    {
                        field: 'quantityBudget',
                        align: 'center',
                        width: '100px',
                        title: '预算数量'
                    },
                    {
                        field: 'priceSettle',
                        align: 'center',
                        width: '100px',
                        title: '决算单价',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control price' type='text' name='taskSettles[%s].priceSettle' value='%s' oninput='freshTotal()' required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'quantitySettle',
                        align: 'center',
                        width: '100px',
                        title: '决算数量',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' type='text' name='taskSettles[%s].quantitySettle' value='%s' oninput='freshTotal()' required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        width: '150px',
                        title: '小计',
                        formatter: function(value, row, index) {
                            return ((row.quantitySettle || 0) * (row.priceSettle || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].quantitySettle || 0;
                                var price = value[i].priceSettle || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control remarks' type='text' name='taskSettles[%s].remarks' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                            if(row.isFromBudget == 1){
                                return '';
                            }else{
                                return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\',\'bootstrap-table-settle\')"><i class="fa fa-remove"></i>删除</a>';
                            }
                        }
                    }
                    ]
            };
            $.table.init(options);
        });

        $(function() {
            var sampleTypeDatas = [[${@dict.getType('zzpm_sample_type')}]];
            var data = /*[[${projectTasks != null ? projectTasks.sampleAllList : {}}]]*/ [];
            var options = {
                id: "bootstrap-table-finish-sample",
                data: data,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                showFooter: true,
                footerStyle: function (column){
                    return {
                        subtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        },
                        finishSubtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        },
                        diffSubtotal: {
                            css: { color: 'red', 'font-weight': 'normal' }
                        }
                    }[column.field]
                },
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        width: '70px',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='sampleAllList[%s].id' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'sampleTypeCode',
                        align: 'center',
                        width: '150px',
                        title: "费用类型",
                        formatter: function (value, row, index) {
                            var data = [{ index: index, type: value }];
                            if(row.isSupplement == 0){
                                return $.table.selectDictLabel(sampleTypeDatas, value);
                            }else{
                                return $("#sampleTypeTpl").tmpl(data).html();
                            }
                        }
                    },
                    {
                        field: 'sampleName',
                        align: 'center',
                        width: '200',
                        title: '费用名称',
                        formatter: function(value, row, index) {
                            if(row.isSupplement == 0){
                                return value;
                            }else{
                                var html = $.common.sprintf("<input class='form-control sampleName' type='text' name='sampleAllList[%s].sampleName' value='%s'>", index, value);
                                return html;
                            }
                        }
                    },
                    {
                        field: 'samplePrice',
                        align: 'center',
                        width: '100px',
                        title: '单价',
                        formatter: function(value, row, index) {
                            if(row.isSupplement == 0){
                                return value;
                            }else{
                                var html = $.common.sprintf("<input class='form-control' type='text' name='sampleAllList[%s].samplePrice' value='%s' oninput='freshTotal()'>", index, value);
                                return html;
                            }
                        }
                    },
                    {
                        field: 'sampleQuantity',
                        align: 'center',
                        title: '数量',
                        formatter: function(value, row, index) {
                            return value;
                        }
                    },
                    {
                        field: 'subtotal',
                        align: 'center',
                        title: '小计',
                        formatter: function(value, row, index) {
                            return ((row.sampleQuantity || 0) * (row.samplePrice || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantity || 0;
                                var price = value[i].samplePrice || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'sampleQuantityFinish',
                        align: 'center',
                        title: '完成量',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control sampleQuantityFinish' type='text' name='sampleAllList[%s].sampleQuantityFinish' value='%s' oninput='freshTotal()'  required>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'finishSubtotal',
                        align: 'center',
                        title: '小计(完成量)',
                        formatter: function(value, row, index) {
                            return ((row.sampleQuantityFinish || 0) * (row.samplePrice || 0)).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantityFinish || 0;
                                var price = value[i].samplePrice || 0;
                                total += parseFloat(quantity * price);
                            }
                            return "合计(完成量)：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'diffSubtotal',
                        align: 'center',
                        title: '差额(合同-完成)',
                        formatter: function (value, row, index) {
                            var quantity = row.sampleQuantity || 0;
                            var quantityFinish = row.sampleQuantityFinish || 0;
                            var price = row.samplePrice || 0;

                            var total = quantity * price;
                            var totalFinish = quantityFinish * price;

                            return (total - totalFinish).toFixed(2);
                        },
                        footerFormatter:function (value) {
                            var total = 0;
                            for (var i in value) {
                                var quantity = value[i].sampleQuantity || 0;
                                var quantityFinish = value[i].sampleQuantityFinish || 0;
                                var price = value[i].samplePrice || 0;

                                var total = quantity * price;
                                var totalFinish = quantityFinish * price;

                                total += parseFloat(total - totalFinish);
                            }
                            return "差额合计(合同-完成)：" + total.toFixed(2);
                        }
                    },
                    {
                        field: 'remarks',
                        align: 'center',
                        title: '备注',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control remarks' type='text' name='sampleAllList[%s].remarks' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                            if(row.isSupplement == 0){
                                return '';
                            }else{
                                return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\',\'bootstrap-table-finish-sample\')"><i class="fa fa-remove"></i>删除</a>';
                            }
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        var timeoutId;
        function freshTotal(){
            if(timeoutId){
                clearTimeout(timeoutId); // 清除之前的定时器
            }

            timeoutId = setTimeout(() => {
                sub.editRow();
            }, 1000); // 延迟500毫秒后触发
        }

        function settle_addRow() {
            var row = {
                costCategorieMainCode: "",
                costCategorieSubCode: "",
                priceSettle: "",
                quantitySettle: "",
                remarks: "",
            }
            sub.addRow(row,"bootstrap-table-settle");
        }

        function sample_addRow() {
            var count = $("#bootstrap-table-finish-sample").bootstrapTable('getData').length;
            var row = {
                sampleTypeCode: "",
                sampleName: "",
                samplePrice: "",
                sampleQuantity: "",
                sampleQuantityFinish: "",
                remarks: "",
            }
            sub.addRow(row,"bootstrap-table-finish-sample");
        }

        var prefix = ctx + "task/settle/apply";
        $("#form-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-edit').serialize());
            }
        }

        // 文件上传相关函数
        function uploadSampleCostFile(input) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('fileBType', ''); // 1表示样本费用附件类型

                $.ajax({
                    url: ctx + 'pmrsch/common/file/upload',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.code === 0) {
                            var fileData = response.data;
                            $('#sampleCostFileName').val(fileData.fileName);
                            $('#sampleCostFileId').val(fileData.fileId);
                            $('#sampleCostFileLink').html('<a href="' + fileData.fileUrl + '" target="_blank"><i class="fa fa-download"></i> ' + fileData.fileName + '</a>');
                            $('#deleteSampleCostFileBtn').show();
                            $.modal.msgSuccess("上传成功");
                        } else {
                            $.modal.msgError(response.msg);
                        }
                    },
                    error: function() {
                        $.modal.msgError("上传失败");
                    }
                });
            }
        }

        function deleteSampleCostFile() {
            $.modal.confirm('确认要删除该附件吗？', function() {
                $('#sampleCostFileName').val('');
                $('#sampleCostFileId').val('');
                $('#sampleCostFileLink').html('');
                $('#deleteSampleCostFileBtn').hide();
                $('#sampleCostFile').val('');
                $.modal.msgSuccess("删除成功");
            });
        }

        // 页面加载时检查是否有已上传的附件
        $(function() {
            var sampleCostFileId = /*[[${projectTasks.sampleCostFileId}]]*/ '';
            if (sampleCostFileId) {
                $.ajax({
                    url: ctx + 'pmrsch/common/file/' + sampleCostFileId,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            var fileData = response.data;
                            $('#sampleCostFileName').val(fileData.fileName);
                            $('#sampleCostFileId').val(sampleCostFileId);
                            $('#sampleCostFileLink').html('<a href="' + fileData.url + '" target="_blank"><i class="fa fa-download"></i> ' + fileData.fileName + '</a>');
                            $('#deleteSampleCostFileBtn').show();
                        }
                    }
                });
            }
        });

        $("input[name='actualStartTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='actualEndTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>

    <!-- 费用类型 zzpm_sample_type-->
    <script id="sampleTypeTpl" th:inline="javascript" type="text/x-jquery-tmpl">
        <div>
            <select class='form-control' name='sampleAllList[${index}].sampleTypeCode' th:utext="${@dataUtils.getSelectForTableHtml('zzpm_sample_type')}"></select>
        </div>
    </script>

    <script id="costCategorieMainTpl" th:inline="javascript" type="text/x-jquery-tmpl">
        <div>
            <select class='form-control' name='taskSettles[${index}].costCategorieMainCode' th:utext="${@dataUtils.getSelectForTableHtml('zzpm_cost_categorie_main')}" required></select>
        </div>
    </script>

    <script id="costCategorieSubTpl" th:inline="javascript" type="text/x-jquery-tmpl">
        <div>
            <select class='form-control' name='taskSettles[${index}].costCategorieSubCode' th:utext="${@dataUtils.getSelectForTableHtml('zzpm_cost_categorie_sub')}" required></select>
        </div>
    </script>

</div>

<div th:fragment="loans(projectTaskBudgetId,projectTaskLoans)">
    <form id="form-edit" onsubmit="submitHandler()">
        <input name="id" th:value="${projectTaskLoans != null ? projectTaskLoans.id : ''}" type="hidden">
        <input name="projectTaskBudgetId" th:value="${projectTaskBudgetId}" type="hidden">
        
        <div class="col-xs-12">
            <h4 class="form-header h4">***填写借款信息***</h4>
        </div>
        <div class="col-xs-12">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">借款金额：</label>
                    <div class="col-sm-8">
                        <input name="amount" th:value="${projectTaskLoans != null ? projectTaskLoans.amount : ''}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>

            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">借款说明：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control" th:text = "${projectTaskLoans != null ? projectTaskLoans.remarks : ''}" ></textarea>
                    </div>
                </div>
            </div>
            
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">附件：</label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="loansFileName" readonly>
                            <input type="hidden" name="fileId" id="loansFileId" th:value="${projectTaskLoans != null ? projectTaskLoans.fileId : ''}">
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-primary" onclick="$('#loansFile').click()">
                                    <i class="fa fa-upload"></i> 上传
                                </button>
                                <button type="button" class="btn btn-danger" onclick="deleteLoansFile()" id="deleteLoansFileBtn" style="display:none">
                                    <i class="fa fa-trash"></i> 删除
                                </button>
                            </span>
                        </div>
                        <input type="file" id="loansFile" style="display:none" onchange="uploadLoansFile(this)">
                        <div id="loansFileLink" style="margin-top:5px"></div>
                    </div>
                </div>
            </div>

        </div>
    </form>

    <script th:inline="javascript">
        $(document).ready(function() {
            // 初始化文件上传控件
            initFileUpload();
        });

        var prefix = ctx + "task/loans/apply";
        $("#form-loans-edit").validate({
            focusCleanup: true
        });

        var isNew = [[${projectTaskLoans == null ? true : false}]];
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + (isNew ? "/add":"/edit"), $('#form-edit').serialize());
            }
        }

        $("input[name='applyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
        
        // 文件上传相关函数
        function uploadLoansFile(input) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('fileBType', ''); // 借款附件类型

                $.ajax({
                    url: ctx + 'pmrsch/common/file/upload',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.code === 0) {
                            var fileData = response.data;
                            $('#loansFileName').val(fileData.fileName);
                            $('#loansFileId').val(fileData.fileId);
                            $('#loansFileLink').html('<a href="' + fileData.fileUrl + '" target="_blank"><i class="fa fa-download"></i> ' + fileData.fileName + '</a>');
                            $('#deleteLoansFileBtn').show();
                            $.modal.msgSuccess("上传成功");
                        } else {
                            $.modal.msgError(response.msg);
                        }
                    },
                    error: function() {
                        $.modal.msgError("上传失败");
                    }
                });
            }
        }

        function deleteLoansFile() {
            $.modal.confirm('确认要删除该附件吗？', function() {
                $('#loansFileName').val('');
                $('#loansFileId').val('');
                $('#loansFileLink').html('');
                $('#deleteLoansFileBtn').hide();
                $('#loansFile').val('');
                $.modal.msgSuccess("删除成功");
            });
        }

        // 初始化文件上传控件
        function initFileUpload() {
            var fileId = [[${projectTaskLoans != null ? projectTaskLoans.fileId : ''}]];
            
            // 如果有已上传的文件，获取下载地址并显示链接
            if (fileId) {
                $.ajax({
                    url: ctx + 'pmrsch/common/file/' + fileId,
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            var fileData = response.data;
                            $('#loansFileName').val(fileData.fileName);
                            $('#loansFileId').val(fileId);
                            $('#loansFileLink').html('<a href="' + fileData.url + '" target="_blank"><i class="fa fa-download"></i> ' + fileData.fileName + '</a>');
                            $('#deleteLoansFileBtn').show();
                        }
                    }
                });
            }
        }
    </script>
</div>