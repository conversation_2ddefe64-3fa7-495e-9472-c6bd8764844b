<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目阶段任务列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>项目：</label>
                            <select name="projectInfoId" class="form-control" th:with="pinfolist=${@dataUtils.getProjects()}">
                                <option value="">所有</option>
                                <option th:each="pinfo : ${pinfolist}" th:text="${pinfo.pName}" th:value="${pinfo.id}"></option>
                            </select>
                        </li>
                        <li>
                            <label>任务名称：</label>
                            <input type="text" name="taskName"/>
                        </li>
                        <li>
                            <label>申请时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginApplyTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endApplyTime]"/>

                        </li>
                        <li>
                            <label>决算状态：</label>
                            <select name="settleStatusCode" th:with="type=${@dict.getType('zzpm_settle_status_format')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="task:settle:apply:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('task:settle:apply:edit')}]];
    var removeFlag = [[${@permission.hasPermi('task:settle:apply:remove')}]];
    var settleStatusCodeDatas = [[${@dict.getType('zzpm_settle_status_format')}]];
    var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
    var prefix = ctx + "task/settle/apply";

    $(function() {
        var options = {
            url: prefix + "?orderByColumn=projectInfoId,settleApplyTime desc",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "项目阶段任务",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'projectInfo.pNo',
                    title: '项目编号'
                },
                {
                    field: 'projectInfo.pName',
                    title: '项目名称'
                },
                {
                    field: 'projectInfo.pTypeCode',
                    title: '项目类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabels(pTypeCodeDatas, value);
                    }
                },
                {
                    field: 'projectInfo.pNoCus',
                    title: '客户项目编号'
                },
                {
                    field: 'taskName',
                    title: '任务名称'
                },

                {
                    field: 'totalBudget',
                    title: '预算总额'
                },
                {
                    field: 'totalLoans',
                    title: '借款总额'
                },
                {
                    field: 'contractTotal',
                    title: '合同总额(初始)'
                },
                {
                    field: 'totalCost',
                    title: '决算总额'
                },
                {
                    field: 'settleApplyTime',
                    title: '决算申请时间'
                },
                {
                    field: 'settleStatusCode',
                    title: '决算状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(settleStatusCodeDatas, value);
                    }
                },
                {
                    field: 'settleRemarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="submitReview(\'' + row.id + '\')"><i class="fa fa-edit"></i>提交</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="manageLabor(\'劳务明细【'+row.projectInfo.pName+'.'+row.taskName+'】\',\''+row.id+'\')"><i class="fa fa-edit"></i>劳务明细</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="manageRespondent(\'受访明细【'+row.projectInfo.pName+'.'+row.taskName+'】\',\''+row.id+'\')"><i class="fa fa-edit"></i>受访明细</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function manageLabor(title,id){
        $.modal.openTab(title,ctx + "task/labor?projectTaskId="+id);
    }

    function manageRespondent(title,id){
        $.modal.openTab(title,ctx + "task/respondent?projectTaskId="+id);
    }

    function submitReview(id) {
        $.modal.confirm("确定要提交审核吗？", function() {
            $.operate.post(ctx + "task/settle/"+id+"/review?review=[[${review}]]");
        })
    }
</script>
</body>
</html>