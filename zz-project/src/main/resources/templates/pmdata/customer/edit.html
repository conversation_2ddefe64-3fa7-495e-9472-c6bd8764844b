<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改客户信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-customer-edit" th:object="${customer}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">客户名称：</label>
                    <div class="col-sm-8">
                        <input name="name" th:field="*{name}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">客户地址：</label>
                    <div class="col-sm-8">
                        <input name="address" th:field="*{address}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control">[[*{remarks}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "pmdata/customer";
        $("#form-customer-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-customer-edit').serialize());
            }
        }
    </script>
</body>
</html>