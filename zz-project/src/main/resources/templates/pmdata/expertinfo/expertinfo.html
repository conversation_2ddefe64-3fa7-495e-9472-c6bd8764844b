<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('专家信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>来源：</label>
                                <input type="text" name="source"/>
                            </li>
                            <li>
                                <label>专家类型：</label>
                                <select name="expertTypeCode" th:with="type=${@dict.getType('zzpm_expert_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>所属行业：</label>
                                <input type="text" name="industry"/>
                            </li>
                            <li>
                                <label>公司/医院名称：</label>
                                <input type="text" name="companyName"/>
                            </li>
                            <li>
                                <label>部门/科室：</label>
                                <input type="text" name="department"/>
                            </li>
                            <li>
                                <label>专家姓名：</label>
                                <input type="text" name="expertName"/>
                            </li>
                            <li>
                                <label>电话：</label>
                                <input type="text" name="phone"/>
                            </li>
                            <li>
                                <label>微信：</label>
                                <input type="text" name="wechat"/>
                            </li>
                            <li>
                                <label>邮箱：</label>
                                <input type="text" name="email"/>
                            </li>
                            <li>
                                <label>职位/职级：</label>
                                <input type="text" name="position"/>
                            </li>
                            <li>
                                <label>上任公司：</label>
                                <input type="text" name="previousCompany"/>
                            </li>
                            <li>
                                <label>擅长领域：</label>
                                <input type="text" name="expertiseArea"/>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="createdAt"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="pmdata:expertinfo:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="pmdata:expertinfo:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="pmdata:expertinfo:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="pmdata:expertinfo:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="pmdata:expertinfo:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('pmdata:expertinfo:edit')}]];
        var removeFlag = [[${@permission.hasPermi('pmdata:expertinfo:remove')}]];
        var expertTypeCodeDatas = [[${@dict.getType('zzpm_expert_type')}]];
        var prefix = ctx + "pmdata/expertinfo";

        $(function() {
            var options = {
                url: prefix + "/list?orderByColumn=createdAt desc",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "专家信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'source',
                    title: '来源'
                },
                {
                    field: 'expertTypeCode',
                    title: '专家类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(expertTypeCodeDatas, value);
                    }
                },
                {
                    field: 'industry',
                    title: '所属行业'
                },
                {
                    field: 'companyName',
                    title: '公司/医院名称'
                },
                {
                    field: 'department',
                    title: '部门/科室'
                },
                {
                    field: 'expertName',
                    title: '专家姓名'
                },
                {
                    field: 'phone',
                    title: '电话'
                },
                {
                    field: 'wechat',
                    title: '微信'
                },
                {
                    field: 'email',
                    title: '邮箱'
                },
                {
                    field: 'position',
                    title: '职位/职级'
                },
                {
                    field: 'background',
                    title: '专家背景',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value, 10, "open");
                    }
                },
                {
                    field: 'price',
                    title: '建议推荐价'
                },
                {
                    field: 'evaluation',
                    title: '评价',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value, 10, "open");
                    }
                },
                {
                    field: 'intro',
                    title: '简介',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value, 10, "open");
                    }
                },
                {
                    field: 'businessCard',
                    title: '名片/工作证',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if(value) {
                            return '<a href="'+value+'" target="_blank">查看</a>';
                        }
                        return '-';
                    }
                },
                {
                    field: 'previousCompany',
                    title: '上任公司'
                },
                {
                    field: 'expertiseArea',
                    title: '擅长领域',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value, 10, "open");
                    }
                },
                {
                    field: 'createdAt',
                    title: '创建时间'
                },
                {
                    field: 'updatedAt',
                    title: '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果姓名和电话相同的记录已经存在，更新这条数据。"> 是否更新已经存在的专家数据
            &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>