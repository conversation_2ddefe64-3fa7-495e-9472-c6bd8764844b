<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改专家信息')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-expertinfo-edit" th:object="${expertInfo}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">来源：</label>
                    <div class="col-sm-8">
                        <input name="source" th:field="*{source}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">专家类型：</label>
                    <div class="col-sm-8">
                        <select name="expertTypeCode" class="form-control" th:with="type=${@dict.getType('zzpm_expert_type')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{expertTypeCode}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">所属行业：</label>
                    <div class="col-sm-8">
                        <input name="industry" th:field="*{industry}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">公司/医院名称：</label>
                    <div class="col-sm-8">
                        <input name="companyName" th:field="*{companyName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">部门/科室：</label>
                    <div class="col-sm-8">
                        <input name="department" th:field="*{department}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">专家姓名：</label>
                    <div class="col-sm-8">
                        <input name="expertName" th:field="*{expertName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">电话：</label>
                    <div class="col-sm-8">
                        <input name="phone" th:field="*{phone}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">微信：</label>
                    <div class="col-sm-8">
                        <input name="wechat" th:field="*{wechat}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">邮箱：</label>
                    <div class="col-sm-8">
                        <input name="email" th:field="*{email}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">职位/职级：</label>
                    <div class="col-sm-8">
                        <input name="position" th:field="*{position}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">建议推荐价：</label>
                    <div class="col-sm-8">
                        <input name="price" th:field="*{price}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">专家背景：</label>
                    <div class="col-sm-8">
                        <textarea name="background" class="form-control">[[*{background}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">评价：</label>
                    <div class="col-sm-8">
                        <textarea name="evaluation" class="form-control">[[*{evaluation}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">简介：</label>
                    <div class="col-sm-8">
                        <textarea name="intro" class="form-control">[[*{intro}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">名片/工作证：</label>
                    <div class="col-sm-8">
                        <input type="file" name="file" id="file" class="file-loading">
                        <input type="hidden" name="businessCard" th:field="*{businessCard}" id="businessCard">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">上任公司：</label>
                    <div class="col-sm-8">
                        <input name="previousCompany" th:field="*{previousCompany}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">擅长领域：</label>
                    <div class="col-sm-8">
                        <textarea name="expertiseArea" class="form-control">[[*{expertiseArea}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script th:inline="javascript">
        var prefix = ctx + "pmdata/expertinfo";
        $("#form-expertinfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-expertinfo-edit').serialize());
            }
        }

        $("input[name='createdAt']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='updatedAt']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        // 已有文件的初始化
        var businessCard = [[${expertInfo.businessCard}]];
        var businessCardName = businessCard ? businessCard.substring(businessCard.lastIndexOf("/") + 1) : "";
        var initialPreview = [];
        var initialPreviewConfig = [];
        
        if (businessCard) {
            initialPreview.push(businessCard);
            initialPreviewConfig.push({
                caption: businessCardName,
                key: 1,
                showRemove: true
            });
        }
        
        $("#file").fileinput({
            uploadUrl: ctx + "common/upload",
            initialPreview: initialPreview,
            initialPreviewAsData: true,
            initialPreviewConfig: initialPreviewConfig,
            maxFileCount: 1,
            autoReplace: true
        }).on('fileuploaded', function (event, data) {
            $("#businessCard").val(data.response.url);
        }).on('fileremoved', function (event, id) {
            $("#businessCard").val('');
        });
    </script>
</body>
</html>