<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-projectinfo-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">项目编号：</label>
                    <div class="col-sm-8">
                        <input name="pNo" class="form-control" type="text" readonly>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目名称：</label>
                    <div class="col-sm-8">
                        <input name="pName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">项目类型：</label>
                    <div class="col-sm-8" th:with="type=${@dict.getType('zzpm_p_type')}">
                        <label th:each="dict : ${type}" class="check-box">
                            <input name="pTypeCode" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}" required>
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">客户项目编号：</label>
                    <div class="col-sm-8">
                        <input name="pNoCus" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">客户：</label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input id="customerId" name="customerId" class="form-control" type="text" required>
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" style="height: 31px" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remarks" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script th:inline="javascript">
        var customerIdSuggest = $("#customerId").bsSuggest({
            url: ctx + "pmrsch/common/all",
            showBtn: true,
            //showHeader: true,
            idField: "id",
            keyField: "name"
        }).on('onDataRequestSuccess', function (e, result) {
            console.log('onDataRequestSuccess: ', result);
        }).on('onSetSelectValue', function (e, keyword) {
            console.log('onSetSelectValue: ', keyword);
        }).on('onUnsetSelectValue', function (e) {
            console.log("onUnsetSelectValue");
        });

        var prefix = ctx + "pmdata/projectinfo"
        $("#form-projectinfo-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $("#customerId").val($("#customerId").data('id'))
                $.operate.save(prefix + "/add", $('#form-projectinfo-add').serialize());
            }
        }
    </script>
</body>
</html>