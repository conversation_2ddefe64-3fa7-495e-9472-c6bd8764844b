<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="pNo"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="pName"/>
                            </li>
                            <li>
                                <label>客户项目编号：</label>
                                <input type="text" name="pNoCus"/>
                            </li>
                            <li>
                                <label>客户ID：</label>
                                <input type="text" name="customerId"/>
                            </li>
                            <li>
                                <label>备注：</label>
                                <input type="text" name="remarks"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="pmdata:projectinfo:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="pmdata:projectinfo:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="pmdata:projectinfo:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="pmdata:projectinfo:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('pmdata:projectinfo:edit')}]];
        var removeFlag = [[${@permission.hasPermi('pmdata:projectinfo:remove')}]];
        var pTypeCodeDatas = [[${@dict.getType('zzpm_p_type')}]];
        var prefix = ctx + "pmdata/projectinfo";

        $(function() {
            var options = {
                url: prefix + "/list?orderByColumn=createdAt desc",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'pNo',
                    title: '项目编号'
                },
                {
                    field: 'pName',
                    title: '项目名称'
                },
                {
                    field: 'pTypeLabel',
                    title: '项目类型'
                },
                {
                    field: 'pNoCus',
                    title: '客户项目编号'
                },
                {
                    field: 'customerId',
                    title: '客户ID'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>