<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectTasksMapper">

    <resultMap type="ProjectTasks" id="ProjectTasksResult">
        <result property="id"    column="id"    />
        <result property="projectInfoId"    column="project_info_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="cusContactPerson"    column="cus_contact_person"    />
        <result property="cusContactPhone"    column="cus_contact_phone"    />
        <result property="remarks"    column="remarks"    />
        <result property="settleTime"    column="settle_time"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="applyStatusCode"    column="apply_status_code"    />
        <result property="settleStatusCode"    column="settle_status_code"    />
        <result property="settleApplicant"    column="settle_applicant"    />
        <result property="settleApplyTime"    column="settle_apply_time"    />
        <result property="settleRemarks"    column="settle_remarks"    />
        <result property="archiveLink"    column="archive_link"    />
        <result property="sampleCostFileId"    column="sample_cost_file_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deductionAmount"    column="deduction_amount"    />
        <result property="otherCost"    column="other_cost"    />
        <result property="prepaidCost"    column="prepaid_cost"    />
        <result property="projectRebate"    column="project_rebate"    />
        <result property="customerComment"    column="customer_comment"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="actualStartTime"    column="actual_start_time"    />
        <result property="actualEndTime"    column="actual_end_time"    />
        <result property="executionCity"    column="execution_city"    />
        <result property="costCheckDate"    column="cost_check_date"    />
    </resultMap>


    <sql id="selectProjectTasksVo">
        select t.id, t.project_info_id, t.task_name, t.cus_contact_person, t.cus_contact_phone, t.remarks, t.settle_time, t.apply_time, t.apply_status_code, t.settle_status_code, t.settle_applicant, t.settle_apply_time, t.settle_remarks, t.archive_link, t.sample_cost_file_id, t.user_id, t.dept_id, t.deduction_amount, t.other_cost, t.prepaid_cost, t.project_rebate, t.customer_comment, t.start_time, t.end_time, t.actual_start_time, t.actual_end_time, t.execution_city, t.cost_check_date 
        from zz_project_tasks t
    </sql>

    <select id="selectProjectTasksList" parameterType="ProjectTasks" resultMap="ProjectTasksResult">
        <include refid="selectProjectTasksVo"/>
        <where>
            <if test="projectInfoId != null  and projectInfoId != ''"> and t.project_info_id = #{projectInfoId}</if>
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="cusContactPerson != null  and cusContactPerson != ''"> and t.cus_contact_person like concat('%', #{cusContactPerson}, '%')</if>
            <if test="cusContactPhone != null  and cusContactPhone != ''"> and t.cus_contact_phone like concat('%', #{cusContactPhone}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and t.remarks = #{remarks}</if>
            <if test="settleTime != null "> and t.settle_time = #{settleTime}</if>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="deptId != null"> and t.dept_id = #{deptId}</if>
            <if test="applyTime != null "> and t.apply_time = #{applyTime}</if>
            <if test="applyStatusCode != null "> and t.apply_status_code = #{applyStatusCode}</if>
            <if test="settleStatusCode != null "> and t.settle_status_code = #{settleStatusCode}</if>
            <if test="settleApplicant != null  and settleApplicant != ''"> and t.settle_applicant = #{settleApplicant}</if>
            <if test="settleApplyTime != null "> and t.settle_apply_time = #{settleApplyTime}</if>
            <if test="settleRemarks != null  and settleRemarks != ''"> and t.settle_remarks = #{settleRemarks}</if>
            <if test="archiveLink != null  and archiveLink != ''"> and t.archive_link = #{archiveLink}</if>
            <if test="sampleCostFileId != null  and sampleCostFileId != ''"> and t.sample_cost_file_id = #{sampleCostFileId}</if>
            <if test="deductionAmount != null"> and t.deduction_amount = #{deductionAmount}</if>
            <if test="otherCost != null"> and t.other_cost = #{otherCost}</if>
            <if test="prepaidCost != null"> and t.prepaid_cost = #{prepaidCost}</if>
            <if test="projectRebate != null"> and t.project_rebate = #{projectRebate}</if>
            <if test="customerComment != null and customerComment != ''"> and t.customer_comment like concat('%', #{customerComment}, '%')</if>
            <if test="startTime != null"> and t.start_time = #{startTime}</if>
            <if test="endTime != null"> and t.end_time = #{endTime}</if>
            <if test="actualStartTime != null"> and t.actual_start_time = #{actualStartTime}</if>
            <if test="actualEndTime != null"> and t.actual_end_time = #{actualEndTime}</if>
            <if test="executionCity != null and executionCity != ''"> and t.execution_city like concat('%', #{executionCity}, '%')</if>
            <if test="costCheckDate != null"> and t.cost_check_date = #{costCheckDate}</if>
            <if test="params.applyStatus != null and params.applyStatus != ''"> and t.apply_status_code in (#{params.applyStatus})</if>
            <!--<if test="applyStatus != null and applyStatus.size() > 0">
             and t.settle_status_code in
                <foreach collection="applyStatus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>-->
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectProjectTasksById" parameterType="String" resultMap="ProjectTasksResult">
        <include refid="selectProjectTasksVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertProjectTasks" parameterType="ProjectTasks">
        insert into zz_project_tasks
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectInfoId != null and projectInfoId != ''">project_info_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="cusContactPerson != null and cusContactPerson != ''">cus_contact_person,</if>
            <if test="cusContactPhone != null and cusContactPhone != ''">cus_contact_phone,</if>
            <if test="remarks != null">remarks,</if>
            <if test="settleTime != null">settle_time,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="applyStatusCode != null">apply_status_code,</if>
            <if test="settleStatusCode != null">settle_status_code,</if>
            <if test="settleApplicant != null">settle_applicant,</if>
            <if test="settleApplyTime != null">settle_apply_time,</if>
            <if test="settleRemarks != null">settle_remarks,</if>
            <if test="archiveLink != null">archive_link,</if>
            <if test="sampleCostFileId != null">sample_cost_file_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deductionAmount != null">deduction_amount,</if>
            <if test="otherCost != null">other_cost,</if>
            <if test="prepaidCost != null">prepaid_cost,</if>
            <if test="projectRebate != null">project_rebate,</if>
            <if test="customerComment != null">customer_comment,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="executionCity != null">execution_city,</if>
            <if test="costCheckDate != null">cost_check_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectInfoId != null and projectInfoId != ''">#{projectInfoId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="cusContactPerson != null and cusContactPerson != ''">#{cusContactPerson},</if>
            <if test="cusContactPhone != null and cusContactPhone != ''">#{cusContactPhone},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="settleTime != null">#{settleTime},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="applyStatusCode != null">#{applyStatusCode},</if>
            <if test="settleStatusCode != null">#{settleStatusCode},</if>
            <if test="settleApplicant != null">#{settleApplicant},</if>
            <if test="settleApplyTime != null">#{settleApplyTime},</if>
            <if test="settleRemarks != null">#{settleRemarks},</if>
            <if test="archiveLink != null">#{archiveLink},</if>
            <if test="sampleCostFileId != null">#{sampleCostFileId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deductionAmount != null">#{deductionAmount},</if>
            <if test="otherCost != null">#{otherCost},</if>
            <if test="prepaidCost != null">#{prepaidCost},</if>
            <if test="projectRebate != null">#{projectRebate},</if>
            <if test="customerComment != null">#{customerComment},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="executionCity != null">#{executionCity},</if>
            <if test="costCheckDate != null">#{costCheckDate},</if>
        </trim>
    </insert>

    <update id="updateProjectTasks" parameterType="ProjectTasks">
        update zz_project_tasks
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectInfoId != null and projectInfoId != ''">project_info_id = #{projectInfoId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="cusContactPerson != null and cusContactPerson != ''">cus_contact_person = #{cusContactPerson},</if>
            <if test="cusContactPhone != null and cusContactPhone != ''">cus_contact_phone = #{cusContactPhone},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="settleTime != null">settle_time = #{settleTime},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="applyStatusCode != null">apply_status_code = #{applyStatusCode},</if>
            <if test="settleStatusCode != null">settle_status_code = #{settleStatusCode},</if>
            <if test="settleApplicant != null">settle_applicant = #{settleApplicant},</if>
            <if test="settleApplyTime != null">settle_apply_time = #{settleApplyTime},</if>
            <if test="settleRemarks != null">settle_remarks = #{settleRemarks},</if>
            <if test="archiveLink != null">archive_link = #{archiveLink},</if>
            <if test="sampleCostFileId != null">sample_cost_file_id = #{sampleCostFileId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deductionAmount != null">deduction_amount = #{deductionAmount},</if>
            <if test="otherCost != null">other_cost = #{otherCost},</if>
            <if test="prepaidCost != null">prepaid_cost = #{prepaidCost},</if>
            <if test="projectRebate != null">project_rebate = #{projectRebate},</if>
            <if test="customerComment != null">customer_comment = #{customerComment},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="executionCity != null">execution_city = #{executionCity},</if>
            <if test="costCheckDate != null">cost_check_date = #{costCheckDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTasksById" parameterType="String">
        delete from zz_project_tasks where id = #{id}
    </delete>

    <delete id="deleteProjectTasksByIds" parameterType="String">
        delete from zz_project_tasks where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="suggestList" resultType="java.util.Map">
        select t.id, t.task_name as taskName, t.project_info_id as projectInfoId, p.p_no as projectNo,p.p_name as projectName
        from zz_project_tasks t
        left join zz_project_info p on t.project_info_id = p.id
        <where>
            <if test="keyword != null and keyword != ''">
                and concat(t.task_name, p.p_no) like concat('%', #{keyword}, '%')
            </if>
        </where>
        order by t.id desc
        limit 20
    </select>

</mapper>