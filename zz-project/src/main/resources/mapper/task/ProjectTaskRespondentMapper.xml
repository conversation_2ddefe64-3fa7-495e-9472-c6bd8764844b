<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectTaskRespondentMapper">
    
    <resultMap type="ProjectTaskRespondent" id="ProjectTaskRespondentResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="idNumber"    column="id_number"    />
        <result property="age"    column="age"    />
        <result property="city"    column="city"    />
        <result property="marital"    column="marital"    />
        <result property="kids"    column="kids"    />
        <result property="householdIncome"    column="household_income"    />
        <result property="occupation"    column="occupation"    />
        <result property="projectType"    column="project_type"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="involvedCategories"    column="involved_categories"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectProjectTaskRespondentVo">
        select t.id, t.project_task_id, t.name, t.phone, t.id_number, t.age, t.city, t.marital, t.kids, t.household_income, 
        t.occupation, t.project_type, t.contact_person, t.involved_categories, t.remarks,
        p.p_no as projectNo, task.task_name as projectTaskName, u.user_name as supervisorName
        from zz_project_task_respondent t
        left join zz_project_tasks task on task.id = t.project_task_id
        inner join zz_project_info p on p.id = task.project_info_id
        left join sys_user u on u.user_id = task.user_id
    </sql>

    <select id="selectProjectTaskRespondentList" parameterType="ProjectTaskRespondent" resultMap="ProjectTaskRespondentResult">
        <include refid="selectProjectTaskRespondentVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and t.project_task_id = #{projectTaskId}</if>
            <if test="supervisorName != null  and supervisorName != ''"> and u.user_name like concat('%', #{supervisorName}, '%')</if>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and t.phone like concat('%', #{phone}, '%')</if>
            <if test="idNumber != null  and idNumber != ''"> and t.id_number like concat('%', #{idNumber}, '%')</if>
            <if test="city != null  and city != ''"> and t.city like concat('%', #{city}, '%')</if>
            <if test="occupation != null  and occupation != ''"> and t.occupation like concat('%', #{occupation}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and t.contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="involvedCategories != null  and involvedCategories != ''"> and t.involved_categories like concat('%', #{involvedCategories}, '%')</if>
            <if test="settleStatusCode != null "> and task.settle_status_code = #{settleStatusCode}</if>
            <if test="projectNo != null and projectNo != ''">AND p.p_no LIKE CONCAT('%', #{projectNo}, '%')</if>
        </where>
    </select>
    
    <select id="selectProjectTaskRespondentById" parameterType="String" resultMap="ProjectTaskRespondentResult">
        <include refid="selectProjectTaskRespondentVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertProjectTaskRespondent" parameterType="ProjectTaskRespondent">
        insert into zz_project_task_respondent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="phone != null">phone,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="age != null">age,</if>
            <if test="city != null">city,</if>
            <if test="marital != null">marital,</if>
            <if test="kids != null">kids,</if>
            <if test="householdIncome != null">household_income,</if>
            <if test="occupation != null">occupation,</if>
            <if test="projectType != null">project_type,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="involvedCategories != null">involved_categories,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="age != null">#{age},</if>
            <if test="city != null">#{city},</if>
            <if test="marital != null">#{marital},</if>
            <if test="kids != null">#{kids},</if>
            <if test="householdIncome != null">#{householdIncome},</if>
            <if test="occupation != null">#{occupation},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="involvedCategories != null">#{involvedCategories},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateProjectTaskRespondent" parameterType="ProjectTaskRespondent">
        update zz_project_task_respondent
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="age != null">age = #{age},</if>
            <if test="city != null">city = #{city},</if>
            <if test="marital != null">marital = #{marital},</if>
            <if test="kids != null">kids = #{kids},</if>
            <if test="householdIncome != null">household_income = #{householdIncome},</if>
            <if test="occupation != null">occupation = #{occupation},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="involvedCategories != null">involved_categories = #{involvedCategories},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskRespondentById" parameterType="String">
        delete from zz_project_task_respondent where id = #{id}
    </delete>

    <delete id="deleteProjectTaskRespondentByIds" parameterType="String">
        delete from zz_project_task_respondent where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>