<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectTaskBudgetMapper">
    
    <resultMap type="ProjectTaskBudget" id="ProjectTaskBudgetResult">
        <result property="id"    column="id"    />
        <result property="budgetName"    column="budget_name"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="applicant"    column="applicant"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="budgetStatusCode"    column="budget_status_code"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectProjectTaskBudgetVo">
        select t.id, t.budget_name,t.project_task_id, t.create_time, t.applicant, t.apply_time, t.budget_status_code, t.remarks 
        from zz_project_task_budget t
        inner join zz_project_tasks task on task.id = t.project_task_id
    </sql>

    <select id="selectProjectTaskBudgetList" parameterType="ProjectTaskBudget" resultMap="ProjectTaskBudgetResult">
        <include refid="selectProjectTaskBudgetVo"/>
        <where>
            <if test="budgetName != null  and budgetName != ''"> and t.budget_name = #{budgetName}</if>
            <if test="projectTaskId != null  and projectTaskId != ''"> and t.project_task_id = #{projectTaskId}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and t.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="applicant != null  and applicant != ''"> and t.applicant = #{applicant}</if>
            <if test="params.beginApplyTime != null and params.beginApplyTime != '' and params.endApplyTime != null and params.endApplyTime != ''"> and t.apply_time between #{params.beginApplyTime} and #{params.endApplyTime}</if>
            <if test="budgetStatusCode != null "> and t.budget_status_code = #{budgetStatusCode}</if>
            <if test="remarks != null  and remarks != ''"> and t.remarks = #{remarks}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectProjectTaskBudgetById" parameterType="String" resultMap="ProjectTaskBudgetResult">
        <include refid="selectProjectTaskBudgetVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertProjectTaskBudget" parameterType="ProjectTaskBudget">
        insert into zz_project_task_budget
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="budgetName != null and budgetName != ''">budget_name,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="budgetStatusCode != null">budget_status_code,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="budgetName != null and budgetName != ''">#{budgetName},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="budgetStatusCode != null">#{budgetStatusCode},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateProjectTaskBudget" parameterType="ProjectTaskBudget">
        update zz_project_task_budget
        <trim prefix="SET" suffixOverrides=",">
            <if test="budgetName != null and budgetName != ''">budget_name = #{budgetName},</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="budgetStatusCode != null">budget_status_code = #{budgetStatusCode},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskBudgetById" parameterType="String">
        delete from zz_project_task_budget where id = #{id}
    </delete>

    <delete id="deleteProjectTaskBudgetByIds" parameterType="String">
        delete from zz_project_task_budget where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>