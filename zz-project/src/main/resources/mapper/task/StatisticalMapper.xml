<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.StatisticalMapper">

    <select id="getTaskCountInfos" resultType="com.ruoyi.pmrsch.task.entity.TaskCountInfo">
        select
            t.id taskId,u.user_name taskAppler,t.apply_time applyNoDate,
            c.name customerName,c.address customerAddress,t.cus_contact_person customerContact,t.cus_contact_phone contactPhone,
            p.p_no companyProjectNo,p.p_no_cus customerProjectNo,p.p_name projectName,t.execution_city executionCity,
            (select group_concat(dc.dict_label) from sys_dict_data dc where dc.dict_type = 'zzpm_p_type' and dc.dict_value = p.p_type_code) projectType,
            t.start_time startTime,t.end_time endTime,t.actual_start_time actualStartTime,t.actual_end_time actualEndTime,
            t.apply_status_code applyStatusCode,
            t.settle_status_code settleStatusCode,
            -- 样本量
            IFNULL(sample.sampleQuantity, 0) sampleQuantity,
            -- 样本额
            IFNULL(sample.sampleAmount, 0) sampleAmount,

            -- 其它费用量
            IFNULL(sample.otherQuantity, 0) otherQuantity,
            -- 其它费用额
            IFNULL(sample.otherAmount, 0) otherAmount,

            -- 样本结算量
            IFNULL(sample.sampleQuantityFinish, 0) sampleQuantityFinish,
            -- 样本结算额
            IFNULL(sample.sampleAmountFinish, 0) sampleAmountFinish,
            -- 样本补充结算量
            IFNULL(sample.sampleQuantityFinishAdd, 0) sampleQuantityFinishAdd,
            -- 样本补充结算额
            IFNULL(sample.sampleAmountFinishAdd, 0) sampleAmountFinishAdd,

            -- 其它费用结算量
            IFNULL(sample.otherQuantityFinish, 0) otherQuantityFinish,
            -- 其它费用结算额
            IFNULL(sample.otherAmountFinish, 0) otherAmountFinish,
            -- 其它费用补充结算量
            IFNULL(sample.otherQuantityFinishAdd, 0) otherQuantityFinishAdd,
            -- 其它费用补充结算额
            IFNULL(sample.otherAmountFinishAdd, 0) otherAmountFinishAdd,

            -- 合同金额 = 样本额 + 其它费用额
            IFNULL(sample.sampleAmount, 0) + IFNULL(sample.otherAmount, 0) contractAmount,
            -- 最终合同额 = 样本结算额 + 样本补充结算额 + 其它费用结算额 + 其它费用补充结算额
            IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0) finalContractAmount,
            -- 实际合同差额 = 最终合同额 - 合同金额
            IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0) - (IFNULL(sample.sampleAmount, 0) + IFNULL(sample.otherAmount, 0)) actualContractDifference,

            -- 预算日期
            budgt.budgtApplyTime,
            -- 预算总额
            IFNULL(budgt.budgetTotal, 0) budgetTotal,
            -- 借款日期
            loans.loansApplyTime,
            -- 借款总额
            IFNULL(loans.loansTotal, 0) loansTotal,
            -- 决算日期
            settle.settleApplyTime,
            -- 决算总额
            IFNULL(settle.settleTotal, 0) settleTotal,
            -- 开票日期
            invoice.invoiceTime,
            -- 开票总额
            IFNULL(invoice.invoiceTotal, 0) invoiceTotal,
            -- 收款日期
            collect.collectTime,
            -- 收款总额
            IFNULL(collect.collectionTotal, 0) collectionTotal,
            -- 扣款
            IFNULL(t.deduction_amount, 0) deductionAmount,
            -- 扣款比例
            IFNULL(t.deduction_ratio, 0) deductionRatio,
            -- 费用核对日期
            t.cost_check_date costCheckDate,
            -- 决算比例（利润） = 决算总额/最终合同额
            IFNULL(settle.settleTotal, 0) / (IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) finalProfitRatio,
            -- 预决算差额 = 决算总额 - 预算总额
            IFNULL(settle.settleTotal, 0) - IFNULL(budgt.budgetTotal, 0) settleDifference,
            -- 扣款
            IFNULL(t.deduction_amount, 0) deductionAmount,
            -- 扣款比例 = 扣款/最终合同额
            IFNULL(t.deduction_amount, 0) / (IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) deductionRatio,
            -- 其它需要支付费用
            IFNULL(t.other_cost, 0) otherCost,
            -- 己收预付费
            IFNULL(t.prepaid_cost, 0) prepaidCost,
            -- 营业额 = 最终合同额 - 扣款
            (IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) - IFNULL(t.deduction_amount, 0) businessAmount,
            -- 应收款 = 最终合同额 - 其它需要支付费用 - 已收预付费
            (IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) - IFNULL(t.other_cost, 0) - IFNULL(t.prepaid_cost, 0) receivables,
            -- 已收
            IFNULL(collect.collectionTotal, 0) received,
            -- 未开票总额 = 应收款 - 开票总额
            (IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) - IFNULL(t.other_cost, 0) - IFNULL(t.prepaid_cost, 0) - IFNULL(invoice.invoiceTotal, 0) unpaidTotal,
            -- 未收款总额 = 应收款 - 收款总额
            (IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) - IFNULL(t.other_cost, 0) - IFNULL(t.prepaid_cost, 0) - IFNULL(collect.collectionTotal, 0) unpaidCollectionTotal,
            -- 项目回扣
            IFNULL(t.project_rebate, 0) projectRebate,
            -- 客户评议
            t.customer_comment customerComment,
            -- 所属月份
            p.of_mouth ofMouth
        from zz_project_info p
        left join zz_customer c on c.id = p.customer_id
        left join zz_project_tasks t on t.project_info_id = p.id
        left join sys_user u on u.user_id = t.user_id

        left join (
            select sp.project_task_id,
                   -- 样本
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity,0)) sampleQuantity,
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity * sp.sample_price,0)) sampleAmount,

                   -- 样本完成
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish,0)) sampleQuantityFinish,
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish * sp.sample_price,0)) sampleAmountFinish,

                   -- 样本补充
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity,0)) sampleQuantityAdd,
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity * sp.sample_price,0)) sampleAmountAdd,

                   -- 样本补充完成
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish,0)) sampleQuantityFinishAdd,
                   sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish * sp.sample_price,0)) sampleAmountFinishAdd,
                   
                   -- 其它费用
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity,0)) otherQuantity ,
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity * sp.sample_price,0)) otherAmount,

                   -- 其它费用完成
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish,0)) otherQuantityFinish,
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish * sp.sample_price,0)) otherAmountFinish,
                   
                   -- 其它费用补充
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity,0)) otherQuantityAdd,
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity * sp.sample_price,0)) otherAmountAdd,

                   -- 其它费用补充完成
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish,0)) otherQuantityFinishAdd,
                   sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish * sp.sample_price,0)) otherAmountFinishAdd
            from zz_project_task_sample sp
            group by sp.project_task_id
        ) sample on sample.project_task_id = t.id

        left join (
            select _budgt.project_task_id,max(b.apply_time) budgtApplyTime,sum(_budgt.quantity_budget*_budgt.price_budget) budgetTotal
            from zz_project_task_budget_detail _budgt
            inner join zz_project_task_budget b on b.id = _budgt.project_task_budget_id
            where b.budget_status_code = 4
            group by _budgt.project_task_id
        ) budgt on budgt.project_task_id = t.id

        left join (
            select _loans.project_task_id,max(_loans.apply_time) loansApplyTime,sum(_loans.amount) loansTotal
            from zz_project_task_loans _loans
            where _loans.loans_status_code = 4
            group by _loans.project_task_id
        ) loans on loans.project_task_id = t.id

        left join (
            select _settle.project_task_id,max(t.apply_time) settleApplyTime,sum(_settle.quantity_settle * _settle.price_settle) settleTotal
            from zz_project_task_settle _settle
            inner join zz_project_tasks t on t.id = _settle.project_task_id
            where t.settle_status_code = 4
            group by _settle.project_task_id
        ) settle on settle.project_task_id = t.id

        left join(
            select _invoice.project_task_id,max(_invoice.created_at) invoiceTime,sum(_invoice.amount) invoiceTotal
            from zz_project_invoicing_logs _invoice
            group by _invoice.project_task_id
        ) invoice on invoice.project_task_id = t.id

        left join(
            select _collect.project_task_id,max(_collect.payment_time) collectTime,sum(_collect.amount) collectionTotal
            from zz_project_pay_collection_logs _collect
            group by _collect.project_task_id
        ) collect on collect.project_task_id = t.id

        <where>
            <if test="applyStatusCode != null">
                and t.apply_status_code = #{applyStatusCode}
            </if>
            <if test="settleStatusCode != null">
                and t.settle_status_code = #{settleStatusCode}
            </if>
            <if test="taskAppler != null and taskAppler != ''">
                and u.user_name like concat('%', #{taskAppler}, '%')
            </if>
            <if test="companyProjectNo != null and companyProjectNo != ''">
                and p.p_no = #{companyProjectNo}
            </if>
            <if test="customerProjectNo != null and customerProjectNo != ''">
                and p.p_no_cus = #{customerProjectNo}
            </if>
            <if test="projectName != null and projectName != ''">
                and p.p_name like concat('%', #{projectName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                and c.name like concat('%', #{customerName}, '%')
            </if>
            <if test="executionCity != null and executionCity != ''">
                and t.execution_city like concat('%', #{executionCity}, '%')
            </if>
            <if test="projectType != null and projectType != ''">
                and p.p_type_code = #{projectType}
            </if>
            <if test="ofMouth != null and ofMouth != ''">
                and p.of_mouth = #{ofMouth}
            </if>
            <if test="params != null and params.applyNoDateB != null and params.applyNoDateB != ''">
                and t.apply_time >= #{params.applyNoDateB}
            </if>
            <if test="params != null and params.applyNoDateE != null and params.applyNoDateE != ''">
                and t.apply_time &lt;= #{params.applyNoDateE}
            </if>
            <if test="params != null and params.startTimeB != null and params.startTimeB != ''">
                and t.start_time >= #{params.startTimeB}
            </if>
            <if test="params != null and params.startTimeE != null and params.startTimeE != ''">
                and t.start_time &lt;= #{params.startTimeE}
            </if>
            <if test="params != null and params.endTimeB != null and params.endTimeB != ''">
                and t.end_time >= #{params.endTimeB}
            </if>
            <if test="params != null and params.endTimeE != null and params.endTimeE != ''">
                and t.end_time &lt;= #{params.endTimeE}
            </if>
            <if test="params != null and params.actualStartTimeB != null and params.actualStartTimeB != ''">
                and t.actual_start_time >= #{params.actualStartTimeB}
            </if>
            <if test="params != null and params.actualStartTimeE != null and params.actualStartTimeE != ''">
                and t.actual_start_time &lt;= #{params.actualStartTimeE}
            </if>
            <if test="params != null and params.actualEndTimeB != null and params.actualEndTimeB != ''">
                and t.actual_end_time >= #{params.actualEndTimeB}
            </if>
            <if test="params != null and params.actualEndTimeE != null and params.actualEndTimeE != ''">
                and t.actual_end_time &lt;= #{params.actualEndTimeE}
            </if>
        </where>
        order by t.apply_time desc
    </select>

    <!-- 督导统计查询 -->
    <select id="getSupervisorStatInfos" resultType="com.ruoyi.pmrsch.task.entity.SupervisorStatInfo">
        SELECT 
            u.user_id AS userId,
            -- 督导
            u.user_name AS supervisorName,
            -- 项目数量
            COUNT(DISTINCT t.id) AS projectCount,
            -- 合同金额 = 样本额 + 其它费用额
            SUM(IFNULL(sample.sampleAmount, 0) + IFNULL(sample.otherAmount, 0)) AS contractAmount,
            -- 最终合同额 = 样本结算额 + 样本补充结算额 + 其它费用结算额 + 其它费用补充结算额
            SUM(IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) AS finalContractAmount,
            -- 营业额 = 最终合同额 - 扣款
            SUM((IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) - IFNULL(t.deduction_amount, 0)) AS businessAmount,
            -- 预算总额
            SUM(IFNULL(budgt.budgetTotal, 0)) AS budgetTotal,
            -- 借款总额
            SUM(IFNULL(loans.loansTotal, 0)) AS loansTotal,
            -- 决算总额
            SUM(IFNULL(settle.settleTotal, 0)) AS settleTotal,
            -- 利润率 = 决算总额/最终合同额
            SUM(IFNULL(settle.settleTotal, 0)) / SUM(IFNULL(sample.sampleAmountFinish, 0) + IFNULL(sample.sampleAmountFinishAdd, 0) + IFNULL(sample.otherAmountFinish, 0) + IFNULL(sample.otherAmountFinishAdd, 0)) AS finalProfitRatio
        FROM 
            zz_project_tasks t
        INNER JOIN 
            sys_user u ON t.user_id = u.user_id
        left join (
                select sp.project_task_id,
                       -- 样本
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity,0)) sampleQuantity,
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity * sp.sample_price,0)) sampleAmount,

                       -- 样本完成
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish,0)) sampleQuantityFinish,
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish * sp.sample_price,0)) sampleAmountFinish,

                       -- 样本补充
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity,0)) sampleQuantityAdd,
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity * sp.sample_price,0)) sampleAmountAdd,

                       -- 样本补充完成
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish,0)) sampleQuantityFinishAdd,
                       sum(if(sp.sample_type_code in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish * sp.sample_price,0)) sampleAmountFinishAdd,

                       -- 其它费用
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity,0)) otherQuantity ,
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity * sp.sample_price,0)) otherAmount,

                       -- 其它费用完成
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish,0)) otherQuantityFinish,
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 0,sp.sample_quantity_finish * sp.sample_price,0)) otherAmountFinish,

                       -- 其它费用补充
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity,0)) otherQuantityAdd,
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity * sp.sample_price,0)) otherAmountAdd,

                       -- 其它费用补充完成
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish,0)) otherQuantityFinishAdd,
                       sum(if(sp.sample_type_code not in (0,1) and sp.is_supplement = 1,sp.sample_quantity_finish * sp.sample_price,0)) otherAmountFinishAdd
                from zz_project_task_sample sp
                group by sp.project_task_id
            ) sample on sample.project_task_id = t.id

        left join (
                select _budgt.project_task_id,max(b.apply_time) budgtApplyTime,sum(_budgt.quantity_budget*_budgt.price_budget) budgetTotal
                from zz_project_task_budget_detail _budgt
                         inner join zz_project_task_budget b on b.id = _budgt.project_task_budget_id
                where b.budget_status_code = 4
                group by _budgt.project_task_id
            ) budgt on budgt.project_task_id = t.id

        left join (
                select _loans.project_task_id,max(_loans.apply_time) loansApplyTime,sum(_loans.amount) loansTotal
                from zz_project_task_loans _loans
                where _loans.loans_status_code = 4
                group by _loans.project_task_id
            ) loans on loans.project_task_id = t.id

        left join (
                select _settle.project_task_id,max(t.apply_time) settleApplyTime,sum(_settle.quantity_settle * _settle.price_settle) settleTotal
                from zz_project_task_settle _settle
                         inner join zz_project_tasks t on t.id = _settle.project_task_id
                where t.settle_status_code = 4
                group by _settle.project_task_id
            ) settle on settle.project_task_id = t.id
        <where>
            <if test="supervisorName != null and supervisorName != ''">
                AND u.user_name LIKE CONCAT('%', #{supervisorName}, '%')
            </if>
            <if test="beginDate != null and beginDate != ''">
                AND t.apply_time >= #{beginDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND t.apply_time &lt;= #{endDate}
            </if>
        </where>
        GROUP BY 
            u.user_id, u.user_name
    </select>

</mapper>