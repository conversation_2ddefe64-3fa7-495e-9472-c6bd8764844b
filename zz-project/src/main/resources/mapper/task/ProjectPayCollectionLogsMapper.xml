<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectPayCollectionLogsMapper">
    
    <resultMap type="ProjectPayCollectionLogs" id="ProjectPayCollectionLogsResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="amount"    column="amount"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="projectNo"    column="project_no"    />
        <result property="projectName"    column="project_name"    />
        <result property="taskName"    column="task_name"    />
    </resultMap>

    <sql id="selectProjectPayCollectionLogsVo">
        select 
            l.id, l.project_task_id, l.amount, l.payment_time, l.remarks, l.created_at,
            t.task_name,
            p.p_no as project_no, p.p_name as project_name
        from zz_project_pay_collection_logs l
        left join zz_project_tasks t on l.project_task_id = t.id
        left join zz_project_info p on t.project_info_id = p.id
    </sql>

    <select id="selectProjectPayCollectionLogsList" parameterType="ProjectPayCollectionLogs" resultMap="ProjectPayCollectionLogsResult">
        <include refid="selectProjectPayCollectionLogsVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and l.project_task_id = #{projectTaskId}</if>
            <if test="amount != null "> and l.amount = #{amount}</if>
            <if test="params.beginPaymentTime != null and params.beginPaymentTime != '' and params.endPaymentTime != null and params.endPaymentTime != ''"> and l.payment_time between #{params.beginPaymentTime} and #{params.endPaymentTime}</if>
            <if test="remarks != null  and remarks != ''"> and l.remarks = #{remarks}</if>
        </where>
        order by l.created_at desc
    </select>
    
    <select id="selectProjectPayCollectionLogsById" parameterType="String" resultMap="ProjectPayCollectionLogsResult">
        <include refid="selectProjectPayCollectionLogsVo"/>
        where l.id = #{id}
    </select>

    <insert id="insertProjectPayCollectionLogs" parameterType="ProjectPayCollectionLogs">
        insert into zz_project_pay_collection_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="amount != null">amount,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="remarks != null">remarks,</if>
            created_at
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="amount != null">#{amount},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="remarks != null">#{remarks},</if>
            now()
         </trim>
    </insert>

    <update id="updateProjectPayCollectionLogs" parameterType="ProjectPayCollectionLogs">
        update zz_project_pay_collection_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectPayCollectionLogsById" parameterType="String">
        delete from zz_project_pay_collection_logs where id = #{id}
    </delete>

    <delete id="deleteProjectPayCollectionLogsByIds" parameterType="String">
        delete from zz_project_pay_collection_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>