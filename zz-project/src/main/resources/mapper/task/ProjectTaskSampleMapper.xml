<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectTaskSampleMapper">
    
    <resultMap type="ProjectTaskSample" id="ProjectTaskSampleResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="sampleTypeCode"    column="sample_type_code"    />
        <result property="sampleName"    column="sample_name"    />
        <result property="samplePrice"    column="sample_price"    />
        <result property="sampleQuantity"    column="sample_quantity"    />
        <result property="sampleQuantityFinish"    column="sample_quantity_finish"    />
        <result property="remarks"    column="remarks"    />
        <result property="isSupplement"    column="is_supplement"    />
    </resultMap>

    <sql id="selectProjectTaskSampleVo">
        select id, project_task_id, sample_type_code, sample_name, sample_price, sample_quantity, sample_quantity_finish, remarks, is_supplement from zz_project_task_sample
    </sql>

    <select id="selectProjectTaskSampleList" parameterType="ProjectTaskSample" resultMap="ProjectTaskSampleResult">
        <include refid="selectProjectTaskSampleVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and project_task_id = #{projectTaskId}</if>
            <if test="sampleTypeCode != null  and sampleTypeCode != ''"> and sample_type_code = #{sampleTypeCode}</if>
            <if test="sampleName != null  and sampleName != ''"> and sample_name like concat('%', #{sampleName}, '%')</if>
            <if test="samplePrice != null "> and sample_price = #{samplePrice}</if>
            <if test="sampleQuantity != null "> and sample_quantity = #{sampleQuantity}</if>
            <if test="sampleQuantityFinish != null "> and sample_quantity_finish = #{sampleQuantityFinish}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="isSupplement != null  and isSupplement != ''"> and is_supplement = #{isSupplement}</if>
        </where>
    </select>
    
    <select id="selectProjectTaskSampleById" parameterType="String" resultMap="ProjectTaskSampleResult">
        <include refid="selectProjectTaskSampleVo"/>
        where id = #{id}
    </select>

    <insert id="insertProjectTaskSample" parameterType="ProjectTaskSample">
        insert into zz_project_task_sample
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="sampleTypeCode != null and sampleTypeCode != ''">sample_type_code,</if>
            <if test="sampleName != null">sample_name,</if>
            <if test="samplePrice != null">sample_price,</if>
            <if test="sampleQuantity != null">sample_quantity,</if>
            <if test="sampleQuantityFinish != null">sample_quantity_finish,</if>
            <if test="remarks != null">remarks,</if>
            <if test="isSupplement != null">is_supplement,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="sampleTypeCode != null and sampleTypeCode != ''">#{sampleTypeCode},</if>
            <if test="sampleName != null">#{sampleName},</if>
            <if test="samplePrice != null">#{samplePrice},</if>
            <if test="sampleQuantity != null">#{sampleQuantity},</if>
            <if test="sampleQuantityFinish != null">#{sampleQuantityFinish},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="isSupplement != null">#{isSupplement},</if>
         </trim>
    </insert>

    <update id="updateProjectTaskSample" parameterType="ProjectTaskSample">
        update zz_project_task_sample
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="sampleTypeCode != null and sampleTypeCode != ''">sample_type_code = #{sampleTypeCode},</if>
            <if test="sampleName != null">sample_name = #{sampleName},</if>
            <if test="samplePrice != null">sample_price = #{samplePrice},</if>
            <if test="sampleQuantity != null">sample_quantity = #{sampleQuantity},</if>
            <if test="sampleQuantityFinish != null">sample_quantity_finish = #{sampleQuantityFinish},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="isSupplement != null">is_supplement = #{isSupplement},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskSampleById" parameterType="String">
        delete from zz_project_task_sample where id = #{id}
    </delete>

    <delete id="deleteProjectTaskSampleByIds" parameterType="String">
        delete from zz_project_task_sample where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>