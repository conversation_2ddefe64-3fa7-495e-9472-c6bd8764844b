<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectInvoicingLogsMapper">
    
    <resultMap type="com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs" id="ProjectInvoicingLogsResult">
        <result property="id"             column="id"              />
        <result property="projectTaskId"  column="project_task_id" />
        <result property="involveTitle"   column="involve_title"   />
        <result property="amount"         column="amount"          />
        <result property="invoicesTime"   column="invoices_time"   />
        <result property="remarks"        column="remarks"         />
        <result property="createdAt"      column="created_at"      />
        <result property="projectNo"      column="project_no"      />
        <result property="projectName"    column="project_name"    />
        <result property="taskName"       column="task_name"       />
    </resultMap>

    <sql id="selectProjectInvoicingLogsVo">
        select 
            l.id, l.project_task_id, l.involve_title, l.amount, l.invoices_time, l.remarks, l.created_at,
            t.task_name,
            p.p_no as project_no, p.p_name as project_name
        from zz_project_invoicing_logs l
        left join zz_project_tasks t on l.project_task_id = t.id
        left join zz_project_info p on t.project_info_id = p.id
    </sql>

    <select id="selectProjectInvoicingLogsList" parameterType="com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs" resultMap="ProjectInvoicingLogsResult">
        <include refid="selectProjectInvoicingLogsVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and l.project_task_id = #{projectTaskId}</if>
            <if test="involveTitle != null  and involveTitle != ''"> and l.involve_title like concat('%', #{involveTitle}, '%')</if>
            <if test="amount != null "> and l.amount = #{amount}</if>
            <if test="invoicesTime != null "> and l.invoices_time = #{invoicesTime}</if>
        </where>
        order by l.created_at desc
    </select>
    
    <select id="selectProjectInvoicingLogsById" parameterType="String" resultMap="ProjectInvoicingLogsResult">
        <include refid="selectProjectInvoicingLogsVo"/>
        where l.id = #{id}
    </select>
        
    <insert id="insertProjectInvoicingLogs" parameterType="com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs">
        insert into zz_project_invoicing_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="involveTitle != null and involveTitle != ''">involve_title,</if>
            <if test="amount != null">amount,</if>
            <if test="invoicesTime != null">invoices_time,</if>
            <if test="remarks != null and remarks != ''">remarks,</if>
            created_at
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="involveTitle != null and involveTitle != ''">#{involveTitle},</if>
            <if test="amount != null">#{amount},</if>
            <if test="invoicesTime != null">#{invoicesTime},</if>
            <if test="remarks != null and remarks != ''">#{remarks},</if>
            now()
         </trim>
    </insert>

    <update id="updateProjectInvoicingLogs" parameterType="com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs">
        update zz_project_invoicing_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="involveTitle != null and involveTitle != ''">involve_title = #{involveTitle},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="invoicesTime != null">invoices_time = #{invoicesTime},</if>
            <if test="remarks != null and remarks != ''">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectInvoicingLogsById" parameterType="String">
        delete from zz_project_invoicing_logs where id = #{id}
    </delete>

    <delete id="deleteProjectInvoicingLogsByIds" parameterType="String">
        delete from zz_project_invoicing_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>