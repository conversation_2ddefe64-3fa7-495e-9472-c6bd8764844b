<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ApprovalLogsMapper">
    
    <resultMap type="ApprovalLogs" id="ApprovalLogsResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="ifApproved"    column="if_approved"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectApprovalLogsVo">
        select id, business_type, business_id, reviewer, review_time, if_approved, remarks from zz_approval_logs
    </sql>

    <select id="selectApprovalLogsList" parameterType="ApprovalLogs" resultMap="ApprovalLogsResult">
        <include refid="selectApprovalLogsVo"/>
        <where>  
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
            <if test="reviewer != null  and reviewer != ''"> and reviewer = #{reviewer}</if>
            <if test="reviewTime != null "> and review_time = #{reviewTime}</if>
            <if test="ifApproved != null "> and if_approved = #{ifApproved}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectApprovalLogsById" parameterType="String" resultMap="ApprovalLogsResult">
        <include refid="selectApprovalLogsVo"/>
        where id = #{id}
    </select>

    <insert id="insertApprovalLogs" parameterType="ApprovalLogs">
        insert into zz_approval_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null and businessId != ''">business_id,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="ifApproved != null">if_approved,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null and businessId != ''">#{businessId},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="ifApproved != null">#{ifApproved},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateApprovalLogs" parameterType="ApprovalLogs">
        update zz_approval_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null and businessId != ''">business_id = #{businessId},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="ifApproved != null">if_approved = #{ifApproved},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApprovalLogsById" parameterType="String">
        delete from zz_approval_logs where id = #{id}
    </delete>

    <delete id="deleteApprovalLogsByIds" parameterType="String">
        delete from zz_approval_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteApprovalLogsByBusinessTypeAndId">
        delete from zz_approval_logs where business_type = #{businessType} and business_id = #{businessId}
    </delete>

</mapper>