<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectTaskSettleMapper">

    <resultMap type="ProjectTaskSettle" id="ProjectTaskSettleResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="costCategorieMainCode"    column="cost_categorie_main_code"    />
        <result property="costCategorieSubCode"    column="cost_categorie_sub_code"    />
        <result property="priceSettle"    column="price_settle"    />
        <result property="quantitySettle"    column="quantity_settle"    />
        <result property="createTime"    column="create_time"    />
        <result property="remarks"    column="remarks"    />
        <result property="priceBudget"    column="price_budget"    />
        <result property="quantityBudget"    column="quantity_budget"    />
        <result property="isFromBudget"    column="isFromBudget"    />
    </resultMap>

    <sql id="selectProjectTaskSettleVo">
        select id, project_task_id, cost_categorie_main_code, cost_categorie_sub_code, price_settle, quantity_settle, create_time, remarks, price_budget, quantity_budget, isFromBudget from zz_project_task_settle
    </sql>

    <select id="selectProjectTaskSettleList" parameterType="ProjectTaskSettle" resultMap="ProjectTaskSettleResult">
        <include refid="selectProjectTaskSettleVo"/>
        <where>
            <if test="projectTaskId != null  and projectTaskId != ''"> and project_task_id = #{projectTaskId}</if>
            <if test="costCategorieMainCode != null  and costCategorieMainCode != ''"> and cost_categorie_main_code = #{costCategorieMainCode}</if>
            <if test="costCategorieSubCode != null  and costCategorieSubCode != ''"> and cost_categorie_sub_code = #{costCategorieSubCode}</if>
            <if test="priceSettle != null "> and price_settle = #{priceSettle}</if>
            <if test="quantitySettle != null "> and quantity_settle = #{quantitySettle}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="priceBudget != null "> and price_budget = #{priceBudget}</if>
            <if test="quantityBudget != null "> and quantity_budget = #{quantityBudget}</if>
            <if test="isFromBudget != null "> and isFromBudget = #{isFromBudget}</if>
        </where>
    </select>

    <select id="selectProjectTaskSettleById" parameterType="String" resultMap="ProjectTaskSettleResult">
        <include refid="selectProjectTaskSettleVo"/>
        where id = #{id}
    </select>

    <insert id="insertProjectTaskSettle" parameterType="ProjectTaskSettle">
        insert into zz_project_task_settle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="costCategorieMainCode != null">cost_categorie_main_code,</if>
            <if test="costCategorieSubCode != null">cost_categorie_sub_code,</if>
            <if test="priceSettle != null">price_settle,</if>
            <if test="quantitySettle != null">quantity_settle,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remarks != null">remarks,</if>
            <if test="priceBudget != null">price_budget,</if>
            <if test="quantityBudget != null">quantity_budget,</if>
            <if test="isFromBudget != null">isFromBudget,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="costCategorieMainCode != null">#{costCategorieMainCode},</if>
            <if test="costCategorieSubCode != null">#{costCategorieSubCode},</if>
            <if test="priceSettle != null">#{priceSettle},</if>
            <if test="quantitySettle != null">#{quantitySettle},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="priceBudget != null">#{priceBudget},</if>
            <if test="quantityBudget != null">#{quantityBudget},</if>
            <if test="isFromBudget != null">#{isFromBudget},</if>
        </trim>
    </insert>

    <update id="updateProjectTaskSettle" parameterType="ProjectTaskSettle">
        update zz_project_task_settle
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="costCategorieMainCode != null">cost_categorie_main_code = #{costCategorieMainCode},</if>
            <if test="costCategorieSubCode != null">cost_categorie_sub_code = #{costCategorieSubCode},</if>
            <if test="priceSettle != null">price_settle = #{priceSettle},</if>
            <if test="quantitySettle != null">quantity_settle = #{quantitySettle},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="priceBudget != null">price_budget = #{priceBudget},</if>
            <if test="quantityBudget != null">quantity_budget = #{quantityBudget},</if>
            <if test="isFromBudget != null">isFromBudget = #{isFromBudget},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskSettleById" parameterType="String">
        delete from zz_project_task_settle where id = #{id}
    </delete>

    <delete id="deleteProjectTaskSettleByIds" parameterType="String">
        delete from zz_project_task_settle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>