<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper">

    <resultMap type="ProjectInfo" id="ProjectInfoResult">
        <result property="id"    column="id"    />
        <result property="pNo"    column="p_no"    />
        <result property="pName"    column="p_name"    />
        <result property="pTypeCode"    column="p_type_code"    />
        <result property="pTypeLabel"    column="p_type_label"    />
        <result property="pNoCus"    column="p_no_cus"    />
        <result property="customerId"    column="customer_id"    />
        <result property="remarks"    column="remarks"    />
        <result property="statusCode"    column="status_code"    />
        <result property="createdAt"    column="created_at"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="ofMouth"    column="of_mouth"    />
    </resultMap>

    <sql id="selectProjectInfoVo">
        select t.id, t.p_no, t.p_name, t.p_type_code, t.p_no_cus, t.customer_id, t.remarks, t.status_code, t.created_at, t.finish_time,
        t.user_id, t.dept_id, t.of_mouth from zz_project_info t
    </sql>

    <sql id="Base_Column_List">
        t.id, t.p_no, t.p_name, t.p_type_code, t.p_no_cus, t.customer_id, t.remarks, t.status_code, t.created_at, t.finish_time,
        t.user_id, t.dept_id, t.of_mouth
    </sql>

    <select id="selectProjectInfoList" parameterType="ProjectInfo" resultMap="ProjectInfoResult">
        SELECT
            t.id, 
            t.p_no, 
            t.p_name, 
            t.p_type_code,
            (
                SELECT
                    GROUP_CONCAT(s.dict_label)
                FROM
                    sys_dict_data s
                WHERE
                    s.dict_type = 'zzpm_p_type'
                    AND FIND_IN_SET(s.dict_value, t.p_type_code)
            ) AS p_type_label,
            t.p_no_cus, 
            t.customer_id, 
            t.remarks, 
            t.status_code, 
            t.created_at, 
            t.finish_time,
            t.user_id, 
            t.dept_id,
            t.of_mouth
        FROM 
            zz_project_info t
        <where>
            <if test="pNo != null  and pNo != ''"> and t.p_no = #{pNo}</if>
            <if test="pName != null  and pName != ''"> and t.p_name like concat('%', #{pName}, '%')</if>
            <if test="pTypeCode != null  and pTypeCode != ''"> and t.p_type_code = #{pTypeCode}</if>
            <if test="pNoCus != null  and pNoCus != ''"> and t.p_no_cus = #{pNoCus}</if>
            <if test="customerId != null  and customerId != ''"> and t.customer_id = #{customerId}</if>
            <if test="remarks != null  and remarks != ''"> and t.remarks = #{remarks}</if>
            <if test="statusCode != null "> and t.status_code = #{statusCode}</if>
            <if test="createdAt != null "> and t.created_at = #{createdAt}</if>
            <if test="finishTime != null "> and t.finish_time = #{finishTime}</if>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="deptId != null"> and t.dept_id = #{deptId}</if>
            <if test="ofMouth != null and ofMouth != ''"> and t.of_mouth = #{ofMouth}</if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectProjectInfoById" parameterType="String" resultMap="ProjectInfoResult">
        SELECT
            t.id, 
            t.p_no, 
            t.p_name, 
            t.p_type_code,
            (
                SELECT
                    GROUP_CONCAT(s.dict_label)
                FROM
                    sys_dict_data s
                WHERE
                    s.dict_type = 'zzpm_p_type'
                    AND FIND_IN_SET(s.dict_value, t.p_type_code)
            ) AS p_type_label,
            t.p_no_cus, 
            t.customer_id, 
            t.remarks, 
            t.status_code, 
            t.created_at, 
            t.finish_time,
            t.user_id, 
            t.dept_id,
            t.of_mouth
        FROM 
            zz_project_info t
        WHERE 
            t.id = #{id}
    </select>

    <insert id="insertProjectInfo" parameterType="ProjectInfo">
        insert into zz_project_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pNo != null">p_no,</if>
            <if test="pName != null and pName != ''">p_name,</if>
            <if test="pTypeCode != null and pTypeCode != ''">p_type_code,</if>
            <if test="pNoCus != null">p_no_cus,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="remarks != null">remarks,</if>
            <if test="statusCode != null">status_code,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="ofMouth != null">of_mouth,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pNo != null">#{pNo},</if>
            <if test="pName != null and pName != ''">#{pName},</if>
            <if test="pTypeCode != null and pTypeCode != ''">#{pTypeCode},</if>
            <if test="pNoCus != null">#{pNoCus},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="statusCode != null">#{statusCode},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="ofMouth != null">#{ofMouth},</if>
        </trim>
    </insert>

    <update id="updateProjectInfo" parameterType="ProjectInfo">
        update zz_project_info
        <set>
            <if test="pNo != null">p_no = #{pNo},</if>
            <if test="pName != null">p_name = #{pName},</if>
            <if test="pTypeCode != null">p_type_code = #{pTypeCode},</if>
            <if test="pNoCus != null">p_no_cus = #{pNoCus},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="statusCode != null">status_code = #{statusCode},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="ofMouth != null">of_mouth = #{ofMouth}</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteProjectInfoById" parameterType="String">
        delete from zz_project_info where id = #{id}
    </delete>

    <delete id="deleteProjectInfoByIds" parameterType="String">
        delete from zz_project_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>