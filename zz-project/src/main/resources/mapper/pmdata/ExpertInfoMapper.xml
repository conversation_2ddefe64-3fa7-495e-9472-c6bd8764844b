<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.pmdata.mapper.ExpertInfoMapper">
    
    <resultMap type="ExpertInfo" id="ExpertInfoResult">
        <result property="id"    column="id"    />
        <result property="source"    column="source"    />
        <result property="expertTypeCode"    column="expert_type_code"    />
        <result property="expertType"    column="expert_type"    />
        <result property="industry"    column="industry"    />
        <result property="companyName"    column="company_name"    />
        <result property="department"    column="department"    />
        <result property="expertName"    column="expert_name"    />
        <result property="phone"    column="phone"    />
        <result property="wechat"    column="wechat"    />
        <result property="email"    column="email"    />
        <result property="position"    column="position"    />
        <result property="background"    column="background"    />
        <result property="price"    column="price"    />
        <result property="evaluation"    column="evaluation"    />
        <result property="intro"    column="intro"    />
        <result property="businessCard"    column="business_card"    />
        <result property="previousCompany"    column="previous_company"    />
        <result property="expertiseArea"    column="expertise_area"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectExpertInfoVo">
        select 
            t.id, t.source, t.expert_type_code,expertType.dict_label expert_type, 
            t.industry, t.company_name, t.department, t.expert_name, 
            t.phone, t.wechat, t.email,
            t.position, t.background, t.price, t.evaluation, t.intro, 
            t.business_card, t.previous_company, t.expertise_area,
            t.created_at, t.updated_at, t.user_id, t.dept_id
        from zz_expert_info t
        left join sys_dict_data expertType on expertType.dict_type = 'zzpm_expert_type' and expertType.dict_value = t.expert_type_code
    </sql>

    <select id="selectExpertInfoList" parameterType="ExpertInfo" resultMap="ExpertInfoResult">
        <include refid="selectExpertInfoVo"/>
        <where>  
            <if test="source != null  and source != ''"> and t.source = #{source}</if>
            <if test="expertTypeCode != null "> and t.expert_type_code = #{expertTypeCode}</if>
            <if test="industry != null  and industry != ''"> and t.industry = #{industry}</if>
            <if test="companyName != null  and companyName != ''"> and t.company_name like concat('%', #{companyName}, '%')</if>
            <if test="department != null  and department != ''"> and t.department = #{department}</if>
            <if test="expertName != null  and expertName != ''"> and t.expert_name like concat('%', #{expertName}, '%')</if>
            <if test="phone != null  and phone != ''"> and t.phone = #{phone}</if>
            <if test="wechat != null  and wechat != ''"> and t.wechat = #{wechat}</if>
            <if test="email != null  and email != ''"> and t.email = #{email}</if>
            <if test="position != null  and position != ''"> and t.position = #{position}</if>
            <if test="background != null  and background != ''"> and t.background = #{background}</if>
            <if test="price != null "> and t.price = #{price}</if>
            <if test="evaluation != null  and evaluation != ''"> and t.evaluation = #{evaluation}</if>
            <if test="intro != null  and intro != ''"> and t.intro = #{intro}</if>
            <if test="businessCard != null  and businessCard != ''"> and t.business_card = #{businessCard}</if>
            <if test="previousCompany != null  and previousCompany != ''"> and t.previous_company like concat('%', #{previousCompany}, '%')</if>
            <if test="expertiseArea != null  and expertiseArea != ''"> and t.expertise_area like concat('%', #{expertiseArea}, '%')</if>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="deptId != null"> and t.dept_id = #{deptId}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectExpertInfoById" parameterType="String" resultMap="ExpertInfoResult">
        <include refid="selectExpertInfoVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertExpertInfo" parameterType="ExpertInfo">
        insert into zz_expert_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="source != null">source,</if>
            <if test="expertTypeCode != null">expert_type_code,</if>
            <if test="industry != null">industry,</if>
            <if test="companyName != null">company_name,</if>
            <if test="department != null">department,</if>
            <if test="expertName != null">expert_name,</if>
            <if test="phone != null">phone,</if>
            <if test="wechat != null">wechat,</if>
            <if test="email != null">email,</if>
            <if test="position != null">position,</if>
            <if test="background != null">background,</if>
            <if test="price != null">price,</if>
            <if test="evaluation != null">evaluation,</if>
            <if test="intro != null">intro,</if>
            <if test="businessCard != null">business_card,</if>
            <if test="previousCompany != null">previous_company,</if>
            <if test="expertiseArea != null">expertise_area,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="source != null">#{source},</if>
            <if test="expertTypeCode != null">#{expertTypeCode},</if>
            <if test="industry != null">#{industry},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="department != null">#{department},</if>
            <if test="expertName != null">#{expertName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="wechat != null">#{wechat},</if>
            <if test="email != null">#{email},</if>
            <if test="position != null">#{position},</if>
            <if test="background != null">#{background},</if>
            <if test="price != null">#{price},</if>
            <if test="evaluation != null">#{evaluation},</if>
            <if test="intro != null">#{intro},</if>
            <if test="businessCard != null">#{businessCard},</if>
            <if test="previousCompany != null">#{previousCompany},</if>
            <if test="expertiseArea != null">#{expertiseArea},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateExpertInfo" parameterType="ExpertInfo">
        update zz_expert_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="source != null">source = #{source},</if>
            <if test="expertTypeCode != null">expert_type_code = #{expertTypeCode},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="department != null">department = #{department},</if>
            <if test="expertName != null">expert_name = #{expertName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="wechat != null">wechat = #{wechat},</if>
            <if test="email != null">email = #{email},</if>
            <if test="position != null">position = #{position},</if>
            <if test="background != null">background = #{background},</if>
            <if test="price != null">price = #{price},</if>
            <if test="evaluation != null">evaluation = #{evaluation},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="businessCard != null">business_card = #{businessCard},</if>
            <if test="previousCompany != null">previous_company = #{previousCompany},</if>
            <if test="expertiseArea != null">expertise_area = #{expertiseArea},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExpertInfoById" parameterType="String">
        delete from zz_expert_info where id = #{id}
    </delete>

    <delete id="deleteExpertInfoByIds" parameterType="String">
        delete from zz_expert_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>