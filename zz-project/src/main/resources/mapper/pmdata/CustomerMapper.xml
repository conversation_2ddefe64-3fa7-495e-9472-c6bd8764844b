<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.pmdata.mapper.CustomerMapper">
    
    <resultMap type="Customer" id="CustomerResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="address"    column="address"    />
        <result property="remarks"    column="remarks"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectCustomerVo">
        select t.id, t.name, t.address, t.remarks, t.user_id, t.dept_id from zz_customer t
    </sql>

    <select id="selectCustomerList" parameterType="Customer" resultMap="CustomerResult">
        <include refid="selectCustomerVo"/>
        <where>  
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="address != null  and address != ''"> and t.address like concat('%', #{address}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and t.remarks = #{remarks}</if>
            <if test="userId != null"> and t.user_id = #{userId}</if>
            <if test="deptId != null"> and t.dept_id = #{deptId}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectCustomerById" parameterType="String" resultMap="CustomerResult">
        <include refid="selectCustomerVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertCustomer" parameterType="Customer">
        insert into zz_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="address != null">address,</if>
            <if test="remarks != null">remarks,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="address != null">#{address},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateCustomer" parameterType="Customer">
        update zz_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="address != null">address = #{address},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerById" parameterType="String">
        delete from zz_customer where id = #{id}
    </delete>

    <delete id="deleteCustomerByIds" parameterType="String">
        delete from zz_customer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>