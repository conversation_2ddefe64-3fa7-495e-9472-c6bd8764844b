<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.budget.mapper.ProjectTaskLoansMapper">
    
    <resultMap type="ProjectTaskLoans" id="ProjectTaskLoansResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="projectTaskBudgetId"    column="project_task_budget_id"    />
        <result property="amount"    column="amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="applicant"    column="applicant"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="loansStatusCode"    column="loans_status_code"    />
        <result property="remarks"    column="remarks"    />
        <result property="fileId"    column="file_id"    />
        <result property="isPayment"    column="is_payment"    />
        <result property="paymentDate"    column="payment_date"    />
        <result property="paymentRemarks"    column="payment_remarks"    />
    </resultMap>

    <sql id="selectProjectTaskLoansVo">
        select t.id, t.project_task_id, t.project_task_budget_id, t.amount, t.create_time, t.applicant, t.apply_time, t.loans_status_code, t.remarks, t.file_id, t.is_payment, t.payment_date, t.payment_remarks
        from zz_project_task_loans t
        inner join zz_project_tasks task on task.id = t.project_task_id
    </sql>

    <select id="selectProjectTaskLoansList" parameterType="ProjectTaskLoans" resultMap="ProjectTaskLoansResult">
        <include refid="selectProjectTaskLoansVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and t.project_task_id = #{projectTaskId}</if>
            <if test="projectTaskBudgetId != null  and projectTaskBudgetId != ''"> and t.project_task_budget_id = #{projectTaskBudgetId}</if>
            <if test="amount != null "> and t.amount = #{amount}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and t.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="applicant != null  and applicant != ''"> and t.applicant = #{applicant}</if>
            <if test="params.beginApplyTime != null and params.beginApplyTime != '' and params.endApplyTime != null and params.endApplyTime != ''"> and t.apply_time between #{params.beginApplyTime} and #{params.endApplyTime}</if>
            <if test="loansStatusCode != null "> and t.loans_status_code = #{loansStatusCode}</if>
            <if test="remarks != null  and remarks != ''"> and t.remarks = #{remarks}</if>
            <if test="fileId != null  and fileId != ''"> and t.file_id = #{fileId}</if>
            <if test="isPayment != null"> and t.is_payment = #{isPayment}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectProjectTaskLoansById" parameterType="String" resultMap="ProjectTaskLoansResult">
        <include refid="selectProjectTaskLoansVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertProjectTaskLoans" parameterType="ProjectTaskLoans">
        insert into zz_project_task_loans
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="projectTaskBudgetId != null and projectTaskBudgetId != ''">project_task_budget_id,</if>
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="applicant != null">applicant,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="loansStatusCode != null">loans_status_code,</if>
            <if test="remarks != null">remarks,</if>
            <if test="fileId != null">file_id,</if>
            <if test="isPayment != null">is_payment,</if>
            <if test="paymentDate != null">payment_date,</if>
            <if test="paymentRemarks != null">payment_remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="projectTaskBudgetId != null and projectTaskBudgetId != ''">#{projectTaskBudgetId},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="loansStatusCode != null">#{loansStatusCode},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="isPayment != null">#{isPayment},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="paymentRemarks != null">#{paymentRemarks},</if>
         </trim>
    </insert>

    <update id="updateProjectTaskLoans" parameterType="ProjectTaskLoans">
        update zz_project_task_loans
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="projectTaskBudgetId != null and projectTaskBudgetId != ''">project_task_budget_id = #{projectTaskBudgetId},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="loansStatusCode != null">loans_status_code = #{loansStatusCode},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="isPayment != null">is_payment = #{isPayment},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="paymentRemarks != null">payment_remarks = #{paymentRemarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskLoansById" parameterType="String">
        delete from zz_project_task_loans where id = #{id}
    </delete>

    <delete id="deleteProjectTaskLoansByIds" parameterType="String">
        delete from zz_project_task_loans where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>