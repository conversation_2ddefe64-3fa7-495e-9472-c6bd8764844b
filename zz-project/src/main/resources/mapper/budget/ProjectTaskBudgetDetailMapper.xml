<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.budget.mapper.ProjectTaskBudgetDetailMapper">
    
    <resultMap type="ProjectTaskBudgetDetail" id="ProjectTaskBudgetDetailResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="projectTaskBudgetId"    column="project_task_budget_id"    />
        <result property="costCategorieMainCode"    column="cost_categorie_main_code"    />
        <result property="costCategorieSubCode"    column="cost_categorie_sub_code"    />
        <result property="priceBudget"    column="price_budget"    />
        <result property="quantityBudget"    column="quantity_budget"    />
        <result property="createTime"    column="create_time"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectProjectTaskBudgetDetailVo">
        select id, project_task_id, project_task_budget_id, cost_categorie_main_code, cost_categorie_sub_code, price_budget, quantity_budget, create_time, remarks from zz_project_task_budget_detail
    </sql>

    <select id="selectProjectTaskBudgetDetailList" parameterType="ProjectTaskBudgetDetail" resultMap="ProjectTaskBudgetDetailResult">
        <include refid="selectProjectTaskBudgetDetailVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and project_task_id = #{projectTaskId}</if>
            <if test="projectTaskBudgetId != null  and projectTaskBudgetId != ''"> and project_task_budget_id = #{projectTaskBudgetId}</if>
            <if test="costCategorieMainCode != null  and costCategorieMainCode != ''"> and cost_categorie_main_code = #{costCategorieMainCode}</if>
            <if test="costCategorieSubCode != null  and costCategorieSubCode != ''"> and cost_categorie_sub_code = #{costCategorieSubCode}</if>
            <if test="priceBudget != null "> and price_budget = #{priceBudget}</if>
            <if test="quantityBudget != null "> and quantity_budget = #{quantityBudget}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectProjectTaskBudgetDetailById" parameterType="String" resultMap="ProjectTaskBudgetDetailResult">
        <include refid="selectProjectTaskBudgetDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertProjectTaskBudgetDetail" parameterType="ProjectTaskBudgetDetail">
        insert into zz_project_task_budget_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="projectTaskBudgetId != null and projectTaskBudgetId != ''">project_task_budget_id,</if>
            <if test="costCategorieMainCode != null">cost_categorie_main_code,</if>
            <if test="costCategorieSubCode != null">cost_categorie_sub_code,</if>
            <if test="priceBudget != null">price_budget,</if>
            <if test="quantityBudget != null">quantity_budget,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="projectTaskBudgetId != null and projectTaskBudgetId != ''">#{projectTaskBudgetId},</if>
            <if test="costCategorieMainCode != null">#{costCategorieMainCode},</if>
            <if test="costCategorieSubCode != null">#{costCategorieSubCode},</if>
            <if test="priceBudget != null">#{priceBudget},</if>
            <if test="quantityBudget != null">#{quantityBudget},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateProjectTaskBudgetDetail" parameterType="ProjectTaskBudgetDetail">
        update zz_project_task_budget_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="projectTaskBudgetId != null and projectTaskBudgetId != ''">project_task_budget_id = #{projectTaskBudgetId},</if>
            <if test="costCategorieMainCode != null">cost_categorie_main_code = #{costCategorieMainCode},</if>
            <if test="costCategorieSubCode != null">cost_categorie_sub_code = #{costCategorieSubCode},</if>
            <if test="priceBudget != null">price_budget = #{priceBudget},</if>
            <if test="quantityBudget != null">quantity_budget = #{quantityBudget},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskBudgetDetailById" parameterType="String">
        delete from zz_project_task_budget_detail where id = #{id}
    </delete>

    <delete id="deleteProjectTaskBudgetDetailByIds" parameterType="String">
        delete from zz_project_task_budget_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>