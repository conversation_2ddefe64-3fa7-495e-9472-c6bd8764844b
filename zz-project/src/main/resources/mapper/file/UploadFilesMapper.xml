<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.file.dao.mapper.UploadFilesMapper">

    <resultMap type="com.ruoyi.pmrsch.file.dao.model.UploadFiles" id="UploadFilesResult">
        <result property="fileId"    column="file_id"    />
        <result property="fileBType"    column="file_B_type"    />
        <result property="userId"    column="user_id"    />
        <result property="description"    column="description"    />
        <result property="filename"    column="filename"    />
        <result property="extension"    column="extension"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="fileSize"    column="file_size"    />
        <result property="md5"    column="md5"    />
        <result property="fileSavePlace"    column="file_save_place"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
    </resultMap>

    <sql id="selectUploadFilesVo">
        select file_id, file_B_type, user_id, description, filename, extension, created_time, updated_time, file_size, md5, file_save_place, file_url, file_type from tx_upload_files
    </sql>

    <select id="selectUploadFilesList" parameterType="com.ruoyi.pmrsch.file.dao.model.UploadFiles" resultMap="UploadFilesResult">
        <include refid="selectUploadFilesVo"/>
        <where>
            <if test="fileBType != null "> and file_B_type = #{fileBType}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="filename != null  and filename != ''"> and filename like concat('%', #{filename}, '%')</if>
            <if test="extension != null  and extension != ''"> and extension = #{extension}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="md5 != null  and md5 != ''"> and md5 = #{md5}</if>
            <if test="fileSavePlace != null  and fileSavePlace != ''"> and file_save_place = #{fileSavePlace}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
        </where>
    </select>

    <select id="selectUploadFilesByFileId" parameterType="String" resultMap="UploadFilesResult">
        <include refid="selectUploadFilesVo"/>
        where file_id = #{fileId}
    </select>

    <insert id="insertUploadFiles" parameterType="com.ruoyi.pmrsch.file.dao.model.UploadFiles">
        insert into tx_upload_files
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="fileBType != null">file_B_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="description != null">description,</if>
            <if test="filename != null">filename,</if>
            <if test="extension != null">extension,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="md5 != null">md5,</if>
            <if test="fileSavePlace != null">file_save_place,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileType != null">file_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="fileBType != null">#{fileBType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="description != null">#{description},</if>
            <if test="filename != null">#{filename},</if>
            <if test="extension != null">#{extension},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="md5 != null">#{md5},</if>
            <if test="fileSavePlace != null">#{fileSavePlace},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileType != null">#{fileType},</if>
        </trim>
    </insert>

    <update id="updateUploadFiles" parameterType="com.ruoyi.pmrsch.file.dao.model.UploadFiles">
        update tx_upload_files
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileBType != null">file_B_type = #{fileBType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="filename != null">filename = #{filename},</if>
            <if test="extension != null">extension = #{extension},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="md5 != null">md5 = #{md5},</if>
            <if test="fileSavePlace != null">file_save_place = #{fileSavePlace},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteUploadFilesByFileId" parameterType="String">
        delete from tx_upload_files where file_id = #{fileId}
    </delete>

    <delete id="deleteUploadFilesByFileIds" parameterType="String">
        delete from tx_upload_files where file_id in
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>

</mapper>