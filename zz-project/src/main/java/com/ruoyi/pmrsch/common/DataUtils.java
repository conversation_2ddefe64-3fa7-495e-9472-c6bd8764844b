package com.ruoyi.pmrsch.common;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.service.IProjectInfoService;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;
import com.ruoyi.pmrsch.task.budget.service.IProjectTaskLoansService;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ApplyStatus;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.service.IProjectTaskBudgetService;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 对用户扫码记录进行数据操作的dao
 * @Author: hhq
 * @Date: 2021/5/24 22:02
 */
@Service
@Slf4j
public class DataUtils {
    @Autowired
    private IProjectInfoService projectInfoService;
    @Autowired
    private IProjectTasksService tasksService;
    @Autowired
    private IProjectTaskBudgetService budgetService;
    @Autowired
    private IProjectTaskLoansService taskLoansService;
    @Autowired
    private DictService dictService;

    public List<ProjectInfo> getProjects(){
        return projectInfoService.selectProjectInfoList(new ProjectInfo());
    }

    public List<ProjectTasks> getTasks(){
        ProjectTasks q = new ProjectTasks();
        q.setApplyStatusCode(ApplyStatus.审核.getValue());
        return tasksService.selectProjectTasksList(q);
    }

    public List<ProjectTaskBudget> getBudgets(){
        return getBudgets(null);
    }
    public List<ProjectTaskBudget> getBudgets(String taskId){
        ProjectTaskBudget q = new ProjectTaskBudget();
        q.setBudgetStatusCode(ReviewStatus.老板审核.getValue());
        q.setProjectTaskId(taskId);
        return budgetService.selectProjectTaskBudgetList(q);
    }

    public Map getTaskTotal(String taskId){
        ProjectTasks tasks = tasksService.selectProjectTasksById(taskId);
        if(tasks == null){
            Map rslt = new HashMap();
            rslt.put("contractCostInitial","");
            rslt.put("budgetTotal","");
            rslt.put("totalLoans","");
            rslt.put("contractTotal","");
            rslt.put("budgetRatio","");
            rslt.put("costRatio","");
            rslt.put("loanRatio", "");
            return rslt;
        }

        //合同费用总额(初始)
        Double contractCostInitial = tasks.getContractTotal();

        //预算总额
        Double budgetTotal = tasks.getTotalBudget();
        //费用总额(与预算对应，实际成本支出额)

        //合同总额(样本总额(合同) + 费用总额(初始))
        Double contractTotal = tasks.getContractTotal();
        //预算比例(预算总额/合同总额)
        Double budgetRatio = tasks.getBudgetRatio();
        //成本比例(费用总额/(样本总额(已完成)+合同费用总额(初始)+合同费用总额(补充)))
        Double costRatio = tasks.getCostRatio();

        Double totalLoans = tasks.getTotalLoans();

        Map rslt = new HashMap();
        rslt.put("contractCostInitial",MoneyUtil.format(contractCostInitial,2));
        rslt.put("budgetTotal",MoneyUtil.format(budgetTotal,2));
        rslt.put("totalLoans",MoneyUtil.format(totalLoans,2));
        rslt.put("contractTotal",MoneyUtil.format(contractTotal,2));
        rslt.put("budgetRatio",MoneyUtil.format(budgetRatio * 100,2));
        rslt.put("costRatio",MoneyUtil.format(costRatio * 100,2));
        
        // Calculate loan ratio (totalLoans/budgetTotal)*100
        Double loanRatio = 0.0;
        if (budgetTotal != null && budgetTotal > 0) {
            loanRatio = (totalLoans / budgetTotal) * 100;
        }
        rslt.put("loanRatio", MoneyUtil.format(loanRatio, 2));

        return rslt;
    }

    public List<ProjectTaskLoans> getTaskLoans(String taskBudgetId){
        ParamUtil.checkStrParam(taskBudgetId,"taskBudgetId");
        ProjectTaskLoans q = new ProjectTaskLoans();
        q.setLoansStatusCode(ReviewStatus.老板审核.getValue());
        q.setProjectTaskBudgetId(taskBudgetId);
        return taskLoansService.selectProjectTaskLoansList(q);
    }

    public String getSelectForTableHtml(String dictType){
        List<SysDictData> dictData = dictService.getType(dictType);
        StringBuilder sb = new StringBuilder();
        for (SysDictData dictDatum : dictData) {
            sb.append(String.format("\n<option value=\"%s\" {{if type===\"%s\"}}selected{{/if}}>%s</option>",dictDatum.getDictValue(),dictDatum.getDictValue(),dictDatum.getDictLabel()));
        }

        return sb.toString();
    }
}
