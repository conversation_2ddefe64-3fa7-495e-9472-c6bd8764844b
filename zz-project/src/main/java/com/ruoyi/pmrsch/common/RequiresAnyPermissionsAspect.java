package com.ruoyi.pmrsch.common;/**
 * <AUTHOR>
 * @Date 2025/2/23
 */


import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.UnauthorizedException;
import org.apache.shiro.subject.Subject;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述
 * @className RequiresAnyPermissionsAspect
 * <AUTHOR>
 * @date 2025年02月23日 14:59
 */
@Aspect
@Component
public class RequiresAnyPermissionsAspect {

    @Before("@annotation(requiresAnyPermissions)")
    public void checkPermissions(JoinPoint joinPoint, RequiresAnyPermissions requiresAnyPermissions) throws Throwable {
        Subject subject = SecurityUtils.getSubject();
        boolean hasPermission = false;
        for (String permission : requiresAnyPermissions.value()) {
            if (subject.isPermitted(permission)) {
                hasPermission = true;
                break;
            }
        }

        if (!hasPermission) {
            throw new UnauthorizedException("You do not have the required permissions");
        }
    }
}
