package com.ruoyi.pmrsch.common;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/22
 */
public enum MoneyUnit implements Serializable {

    元(1),
    角(2),
    分(3),
    厘(4),
    毫(5);

    private int value;

    MoneyUnit(int value) {
        this.value = value;
    }

    public static MoneyUnit getByValue(int value) {
        for (MoneyUnit i : MoneyUnit.values()) {
            if (value == i.getValue()) {
                return i;
            }
        }
        return null;
    }

    public int getValue() {
        return this.value;
    }
}
