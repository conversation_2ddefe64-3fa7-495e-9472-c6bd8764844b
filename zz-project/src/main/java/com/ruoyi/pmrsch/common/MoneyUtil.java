package com.ruoyi.pmrsch.common;/**
 * <AUTHOR>
 * @Date 1/20/21
 */

import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 金额转换处理
 * @ClassName TimeUtil
 * <AUTHOR>
 * @Date
 **/
public class MoneyUtil {
    private static DecimalFormat twoDigitsFormatter = new DecimalFormat("0.00");

    /**
     * 分转元
     * @param cents
     * @return
     */
    public static Double toYuan(Object cents) {
        return toYuan(MoneyUnit.分,cents,new Double(0));
    }

    public static Double toYuan(Object cents, Double defaultValue){
        return toYuan(MoneyUnit.分,cents,defaultValue);
    }

    public static Double toYuan(MoneyUnit moneyUnit, Object value){
        return toYuan(moneyUnit,value,new Double(0));
    }

    /**
     * 金额转元
     * @param value
     * @param defaultValue value 为空时默认值
     * @return
     */
    public static Double toYuan(MoneyUnit srcUnit, Object value, Double defaultValue) {
        ParamUtil.checkObjParam(srcUnit,"srcUnit");

        if (value == null) {
            return defaultValue;
        }

        BigDecimal decimal;
        if (value.getClass().equals(String.class)) {
            decimal = new BigDecimal((String) value);
        } else {
            try {
                Method intValue = value.getClass().getDeclaredMethod("intValue");
                decimal = new BigDecimal((int) intValue.invoke(value));
            } catch (Exception e) {
                throw new ApiException("不支持的数据类型", ApiException.SYSTEM);
            }
        }

        switch (srcUnit){
            case 元:
                return decimal.doubleValue();
            case 角:
                return decimal.movePointLeft(1).doubleValue();
            case 分:
                return decimal.movePointLeft(2).doubleValue();
            case 厘:
                return decimal.movePointLeft(3).doubleValue();
            case 毫:
                return decimal.movePointLeft(4).doubleValue();
        }

        throw new RuntimeException("不支持的金额格式:"+srcUnit);
    }

    /**
     * 元转分
     * @param yuan
     * @return
     */
    public static Integer toCents(Object yuan) {
        return toCents(MoneyUnit.元,yuan,null);
    }

    public static Integer toCents(MoneyUnit srcUnit,Object value){
        return toCents(srcUnit,value,null);
    }

    /**
     * 金额转分
     * @param value
     * @param defaultValue
     * @return
     */
    public static Integer toCents(MoneyUnit srcUnit,Object value,Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }

        BigDecimal decimal = new BigDecimal(String.valueOf(value));
        switch (srcUnit){
            case 元:
                return decimal.movePointRight(2).intValue();
            case 角:
                return decimal.movePointRight(1).intValue();
            case 分:
                return decimal.intValue();
            case 厘:
                return decimal.movePointLeft(1).intValue();
            case 毫:
                return decimal.movePointLeft(2).intValue();
        }

        throw new RuntimeException("不支持的金额格式:"+srcUnit);
    }

    /**
     * 格式化金额
     * @param cents
     * @return 格式化金额，单位元
     */
    public static String format(Integer cents) {
        return format(MoneyUnit.分,cents,1);
    }

    public static String format(MoneyUnit srcUnit,Object value){
        return format(srcUnit,value,1);
    }
    /**
     * 格式化金额
     * @param value
     * @return 格式化金额，单位元
     */
    public static String format(MoneyUnit srcUnit,Object value, int scale) {
        return format(toYuan(srcUnit,value),scale);
    }

    /**
     * 格式化浮点数
     * @param value
     * @param scale 小数点后保留几位
     * @return 格式化字符
     */
    public static String format(double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }

        String pattern = "#.";
        if(scale == 0){
            pattern = "#.#";
        }else{
            for(int i = 0; i < scale; i++){
                pattern = pattern + "#";
            }
        }

        DecimalFormat decimalFormat = new DecimalFormat(pattern);
        return decimalFormat.format(value);
    }

}
