package com.ruoyi.pmrsch.common.tools;/**
 * <AUTHOR>
 * @Date 1/20/21
 */

import com.ruoyi.pmrsch.common.entity.TimeRange;
import com.ruoyi.pmrsch.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 时间处理
 * @ClassName TimeUtil
 * <AUTHOR>
 * @Date
 **/
@Slf4j
public class TimeUtil {

    private static final String defaultTimeformatter = "yyyy-MM-dd HH:mm:ss";

    public final static String MM_DD = "MM-dd";

    public final static String HH_MM = "HH:mm";

    /**
     * 一天的秒数
     */
    public final static Integer DAY_SECOND = 86400;

    /**
     * 一小时的秒数
     */
    public final static Integer HOUR_SECOND = 3600;

    /**
     * 一个月的天数
     */
    public final static Integer MONTH_DAY = 30;

    /**
     * 一年的天数
     */
    public final static Integer YEAR_DAY = 365;

    public final static String YYYY_MM_DD = "yyyy-MM-dd";
    public final static String YYYYMMDD = "yyyyMMdd";

    public final static String YYYY_MM = "yyyy-MM";

    public final static String YYYYMMDD_HHMMSS = "yyyyMMdd_HHmmss";

    public static String getCurrentTimeFormat() {
        return toString(getCurrentDate(), null);
    }

    public static String getCurrentTimeFormat(String format) {
        return toString(getCurrentDate(), format);
    }

    public static long getCurrentTime() {
        return getCurrentDate().getTime();
    }

    public static Date getCurrentDate() {
        return new Date();
    }

    public static String toString(Date time) {
        if (time == null) {
            return null;
        }
        return toString(time.getTime(), null);
    }

    public static String toString(Date time, String format) {
        return toString(time.getTime(), format);
    }

    public static String toString(long time) {
        return toString(time, null);
    }

    public static String toString(Integer time, String format) {
        return toString((long) time * 1000, format);
    }

    public static String toString(long time, String format) {
        SimpleDateFormat simpleDateFormat = null;

        if (StringUtils.isBlank(format)) {
            simpleDateFormat = new SimpleDateFormat(defaultTimeformatter, Locale.CHINA);
        } else {
            simpleDateFormat = new SimpleDateFormat(format, Locale.CHINA);
        }

        return simpleDateFormat.format(new Date(time));
    }

    /**
     * 将秒转化为时分秒格式
     *
     * @param secondsTime 时间戳： 秒
     * @return xx小时xx分x秒
     */
    public static String formatSeconds(int secondsTime) {
        return formatSeconds(secondsTime, TimeUnit.DAYS, true);
    }

    public static String formatSeconds(int secondsTime, TimeUnit timeUnit) {
        return formatSeconds(secondsTime, timeUnit, true);
    }

    public static String formatSeconds(int secondsTime, TimeUnit timeUnit, boolean fullFormat) {
        if (fullFormat) {

            return _fullFormatSeconds(secondsTime, timeUnit);

        } else {

            if (TimeUnit.DAYS.equals(timeUnit)) {
                return String.format("%1$,d天", secondsTime / (60 * 60 * 24));
            }

            if (TimeUnit.HOURS.equals(timeUnit)) {
                return String.format("%1$,d小时", secondsTime / (60 * 60));
            }

            if (TimeUnit.MINUTES.equals(timeUnit)) {
                return String.format("%1$,d分钟", secondsTime / (60));
            }

            if (TimeUnit.SECONDS.equals(timeUnit)) {
                return String.format("%1$,d秒", secondsTime);
            }
        }

        throw new RuntimeException("格式不正确");
    }

    private static String _fullFormatSeconds(int secondsTime, TimeUnit timeUnit) {

        if (TimeUnit.DAYS.equals(timeUnit)) {

            int days = secondsTime / (60 * 60 * 24);
            int hours = secondsTime / (60 * 60) - days * 24;
            int minutes = secondsTime / 60 - hours * 60 - days * 24 * 60;
            int seconds = secondsTime - minutes * 60 - hours * 60 * 60 - days * 24 * 60 * 60;

            return _fullFormatSeconds(days, hours, minutes, seconds);
        }

        if (TimeUnit.HOURS.equals(timeUnit)) {
            int hours = secondsTime / (60 * 60);
            int minutes = secondsTime / 60 - hours * 60;
            int seconds = secondsTime - minutes * 60 - hours * 60 * 60;

            return _fullFormatSeconds(0, hours, minutes, seconds);
        }

        if (TimeUnit.MINUTES.equals(timeUnit)) {

            int minutes = secondsTime / 60;
            int seconds = secondsTime - minutes * 60;

            return _fullFormatSeconds(0, 0, minutes, seconds);
        }

        if (TimeUnit.SECONDS.equals(timeUnit)) {
            int seconds = secondsTime;
            return _fullFormatSeconds(0, 0, 0, seconds);
        }

        throw new RuntimeException("格式不正确");
    }

    private static String _fullFormatSeconds(int days, int hours, int minutes, int seconds) {
        List<Object> array = new ArrayList<>();
        String format = "";

        if (days > 0) {
            format = "%s天";
            array.add(days);
        }

        if (hours > 0) {
            format = format + "%s小时";
            array.add(hours);
        }

        if (minutes > 0) {
            format = format + "%s分钟";
            array.add(minutes);
        }

        if (seconds > 0) {
            format = format + "%s秒";
            array.add(seconds);
        }

        return String.format(format, array.toArray());
    }

    /**
     * 区分时间段
     *
     * @param time 时间戳，单位：秒
     * @return 上午
     */
    public static String timePeriod(int time) {
        long timeMillis = time * 1000;
        Calendar instance = Calendar.getInstance();
        instance.setTimeInMillis(timeMillis);
        int hour = instance.get(Calendar.HOUR_OF_DAY);
        if (hour < 8) {
            return "早晨";
        } else if (hour < 12) {
            return "上午";
        } else if (hour < 14) {
            return "中午";
        } else if (hour < 18) {
            return "下午";
        } else if (hour < 21) {
            return "晚上";
        } else {
            return "深夜";
        }
    }

    /**
     * time离当前时间超过days天
     *
     * @param time 指定时间戳
     * @param days 天数
     * @return
     */
    public static boolean overDayTime(int time, int days) {
        return overTime(time, TimeUnit.DAYS, days);
    }

    /**
     * btime 与 etime 间隔是否超过days
     *
     * @param btime
     * @param etime
     * @param days
     * @return
     */
    public static boolean overDayTime(int btime, int etime, int days) {
        return overTime(btime, etime, TimeUnit.DAYS, days);
    }

    public static boolean overMinuteTime(Date time, int minutes) {
        return overMinuteTime(time.getTime(),minutes);
    }

    /**
     * time离当前时间超过minutes分钟
     *
     * @param time    指定时间戳
     * @param minutes 分钟数
     * @return
     */
    public static boolean overMinuteTime(long time, int minutes) {
        return overTime(time, TimeUnit.MINUTES, minutes);
    }

    public static boolean overSecondTime(long time, int second) {
        return overTime(time, TimeUnit.SECONDS, second);
    }

    /**
     * btime 与 etime 间隔是否超过minutes
     *
     * @param btime
     * @param etime
     * @param minutes
     * @return
     */
    public static boolean overMinuteTime(long btime, int etime, int minutes) {
        return overTime(btime, etime, TimeUnit.MINUTES, minutes);
    }

    /**
     * time离当前时间是否超过指定的分钟、小时、天
     *
     * @param time     指定时间戳
     * @param timeUnit 类型
     * @param minus    数值
     * @return
     */
    private static boolean overTime(long time, TimeUnit timeUnit, int minus) {
        return calAddOrMinus(time, minus, timeUnit).before(Calendar.getInstance());
    }

    public static Date toDate(String dateString) {
        return toDate(dateString, null);
    }

    public static Date toDate(String dateString, String format) {
        SimpleDateFormat simpleDateFormat = null;

        if (StringUtils.isBlank(format)) {
            simpleDateFormat = new SimpleDateFormat(defaultTimeformatter, Locale.CHINA);
        } else {
            simpleDateFormat = new SimpleDateFormat(format, Locale.CHINA);
        }

        try {
            return simpleDateFormat.parse(dateString);
        } catch (ParseException e) {
            log.error("解析日期发生异常[{}],[{}]", dateString, format, e);
        }

        return null;
    }

    /**
     * btime 与 etime 间隔是否超过minus
     *
     * @param btime
     * @param etime
     * @param timeUnit
     * @param minus
     * @return
     */
    private static boolean overTime(long btime, int etime, TimeUnit timeUnit, int minus) {
        Calendar bcal = Calendar.getInstance();
        bcal.setTime(new Date(btime * 1000L));

        return calAddOrMinus(etime, minus, timeUnit).before(bcal);
    }

    public static Calendar calAddOrMinus(String time, int addOrMinus, TimeUnit timeUnit) {
        Date dateTime = toDate(time);
        return calAddOrMinus(dateTime.getTime(), addOrMinus, timeUnit);
    }

    public static Calendar calAddOrMinus(Date time, int addOrMinus, TimeUnit timeUnit) {
        return calAddOrMinus(time.getTime(), addOrMinus, timeUnit);
    }

    public static Calendar calAddOrMinus(int addOrMinus, TimeUnit timeUnit) {
        return calAddOrMinus(getMillisTime(), addOrMinus, timeUnit);
    }

    /**
     * 日期加减
     *
     * @param time
     * @param addOrMinus
     * @param timeUnit
     * @return
     */
    public static Calendar calAddOrMinus(long time, int addOrMinus, TimeUnit timeUnit) {
        if (time <= 0) {
            throw new RuntimeException("time值不正确" + time);
        }

        int field = 0;
        if (TimeUnit.SECONDS.equals(timeUnit)) {
            field = Calendar.SECOND;
        }

        if (TimeUnit.MINUTES.equals(timeUnit)) {
            field = Calendar.MINUTE;
        }

        if (TimeUnit.HOURS.equals(timeUnit)) {
            field = Calendar.HOUR_OF_DAY;
        }

        if (TimeUnit.DAYS.equals(timeUnit)) {
            field = Calendar.DAY_OF_MONTH;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(time));
        cal.add(field, addOrMinus);
        return cal;
    }

    /**
     * 当前时间 - bTime
     *
     * @param bTime
     * @param timeUnit
     * @return
     */
    public static long calDifference(Date bTime, TimeUnit timeUnit) {
        ParamUtil.checkObjParam(bTime, "bTime");

        return calDifference(bTime.getTime(), getCurrentTime(), timeUnit);
    }

    /**
     * eTime - bTime
     *
     * @param bTime
     * @param eTime
     * @param timeUnit
     * @return
     */
    public static long calDifference(Date bTime, Date eTime, TimeUnit timeUnit) {
        ParamUtil.checkObjParam(bTime, "bTime");
        ParamUtil.checkObjParam(eTime, "eTime");

        return calDifference(bTime.getTime(), eTime.getTime(), timeUnit);
    }

    /**
     * 当前时间 - bTime
     *
     * @param bTime
     * @param timeUnit
     * @return
     */
    public static long calDifference(int bTime, TimeUnit timeUnit) {
        return calDifference((long) bTime * 1000, getCurrentTime(), timeUnit);
    }

    /**
     * eTime -bTime
     *
     * @param bTime
     * @param eTime
     * @param timeUnit
     * @return
     */
    public static long calDifference(int bTime, int eTime, TimeUnit timeUnit) {
        return calDifference((long) bTime * 1000, (long) eTime * 1000, timeUnit);
    }

    /**
     * 当前时间 - bTime
     *
     * @param bTime
     * @param timeUnit
     * @return
     */
    public static long calDifference(long bTime, TimeUnit timeUnit) {
        return calDifference(bTime, getCurrentTime(), timeUnit);
    }

    /**
     * etime - btime 时间差值
     *
     * @param bTime
     * @param eTime
     * @param timeUnit
     * @return
     */
    public static long calDifference(long bTime, long eTime, TimeUnit timeUnit) {
        if (bTime <= 0) {
            throw new RuntimeException("bTime 值不正确" + bTime);
        }

        if (bTime <= 0) {
            throw new RuntimeException("eTime 值不正确" + eTime);
        }

        ParamUtil.checkObjParam(timeUnit, "timeUnit");

        Long d_value = eTime - bTime;
        Long d_value_convert = null;

        if (TimeUnit.MILLISECONDS.equals(timeUnit)) {
            d_value_convert = d_value;
        }

        if (TimeUnit.SECONDS.equals(timeUnit)) {
            d_value_convert = d_value / 1000;
        }

        if (TimeUnit.MINUTES.equals(timeUnit)) {
            d_value_convert = d_value / 1000 / 60;
        }

        if (TimeUnit.HOURS.equals(timeUnit)) {
            d_value_convert = d_value / 1000 / 60 / 60;
        }

        if (TimeUnit.DAYS.equals(timeUnit)) {
            d_value_convert = d_value / 1000 / 60 / 60 / 24;
        }

        if (d_value_convert != null) {
            return d_value_convert;
        } else {
            throw new RuntimeException("timeUnit 不支持");
        }
    }

    /**
     * 时间日期转换
     *
     * @param dateTime 基于秒的int类型时间
     * @return 格式化后的日期时间
     */
    public static String timeFormatted(Integer dateTime) {
        return timeFormatted(dateTime, null);
    }

    /**
     * 时间日期转换
     *
     * @param dateTime 基于秒的int类型时间
     * @param pattern  日期格式，缺省值yyyy-MM-dd HH:mm:ss
     * @return 格式化后的日期时间
     */
    public static String timeFormatted(Integer dateTime, String pattern) {
        if (dateTime == null) {
            return null;
        }
        return timeFormatted(new BigDecimal(dateTime).movePointRight(3).longValue(), pattern);
    }

    /**
     * 时间日期转换
     *
     * @param dateTime 基于毫秒的long类型时间
     * @param pattern  日期格式，缺省值yyyy-MM-dd HH:mm:ss
     */
    public static String timeFormatted(Long dateTime, String pattern) {
        if (StringUtils.isEmpty(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.CHINA);
        Date date = new Date(dateTime);
        return sdf.format(date);
    }

    /**
     * 时间日期转换成 基于秒数的int类型时间
     *
     * @param dateTime 基于毫秒的long类型时间
     */
    public static int timeFormattedToInt(long dateTime) {
        return ((int) (dateTime / 1000L));
    }

    /**
     * 根据日期格式截取时间戳
     *
     * @param dateTime 基于毫秒的long类型时间
     * @param pattern  日期格式，缺省值yyyy-MM-dd HH:mm:ss
     */
    public static long time(long dateTime, String pattern) {
        if (StringUtils.isEmpty(pattern))
            pattern = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date = new Date(dateTime);
        String dateString = sdf.format(date);
        try {
            return sdf.parse(dateString).getTime();
        } catch (Exception ex) {
            return 0L;
        }
    }

    /**
     * 把(HH:mm:ss)格式的String转换成Time格式
     */
    public static Time parseTime(String hour, String minute) {
        return parseTime(hour, minute, null);
    }

    /**
     * 转换时间
     *
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static Time parseTime(String hour, String minute, String second) {
        try {
            second = Optional.ofNullable(second).orElse("00");
            String time = hour + ":" + minute + ":" + second;

            return Time.valueOf(time);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static String parseString(Time time, TimeUnit timeUnit) {
        String timestr = time.toString();

        if (TimeUnit.MINUTES.equals(timeUnit)) {
            return timestr.substring(0, 5);
        }

        return timestr;
    }


    /**
     * 把指定(如HH:mm:ss)格式的String转换成int(时间戳)格式
     *
     * @param strTime
     * @return
     */
    public static int strToIntTime(String strTime, String pattern) {
        if ("HH:mm:ss".equals(pattern)) {
            String[] strings = strTime.split(":");
            int hour = Integer.valueOf(strings[0]);
            int minute = Integer.valueOf(strings[1]);
            int second = Integer.valueOf(strings[2]);
            return hour * 60 * 60 + minute * 60 + second;
        }

        if (StringUtils.isEmpty(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        Date date = null;
        try {
            date = simpleDateFormat.parse(strTime);
        } catch (Exception e) {
            throw new ApiException("时间格式有误", ApiException.FORM_INVALID);
        }
        return (int) (date.getTime() / 1000L);
    }

    public static int calendarOperation(Date date, int addMonth, int addDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, addMonth);
        calendar.add(Calendar.DAY_OF_MONTH, addDay);
        calendar.add(Calendar.SECOND, -1);

        return timeFormattedToInt(calendar.getTimeInMillis());
    }

    /**
     * 获取时间，注意：该方法可能返回负数
     *
     * @param time       基础时间戳(单位： 秒)
     * @param addOrMinus 增量/减量
     * @param field      增减类型，事例：Calendar.DAY_OF_YEAR
     * @return
     */
    public static int getTimeChange(int time, int addOrMinus, int field) {
        Date date_time = new Date(time * 1000L);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date_time);
        calendar.add(field, addOrMinus);
        return timeFormattedToInt(calendar.getTime().getTime());
    }

    /**
     * 返回一个偏移的时间 偏移量单位:秒
     */
    public static Date upcoming(int offset) {
        Calendar now = Calendar.getInstance();
        now.add(Calendar.SECOND, offset);
        return now.getTime();
    }

    /**
     * 取得当前时间戳(10位)
     *
     * @return
     */
    public static int getTime() {
        return (int) (getMillisTime() / 1000L);
    }

    /**
     * 取得当前时间戳(13位)
     */
    public static long getMillisTime() {
        return Calendar.getInstance().getTime().getTime();
    }

    /**
     * 获取当天剩余的秒数
     */
    public static int getCurDayRemainSecond() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Integer seconds = (int) ((calendar.getTime().getTime() - getMillisTime()) / 1000);
        return seconds;
    }

    /**
     * 获取时间的天数
     *
     * @param time 秒数
     * @return 天数
     */
    public static Integer getDay(Integer time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(time * 1000L));
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        return day;
    }


    public static Integer getHour() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(getTime() * 1000L));
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取时间的星期数
     *
     * @param time 秒数
     * @return 星期数
     */
    public static Integer getWeek(Integer time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(time * 1000L));
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        int sunday = 1;
        if (week == sunday) {
            week = 7;
        } else {
            week--;
        }
        return week;
    }

    /**
     * 获取某个月最后一天
     *
     * @param year  年
     * @param month 月
     */
    public static Integer getMonthLastDay(int year, int month) {
        LocalDate localDate = LocalDate.of(year, month, 1);
        LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return lastDay.getDayOfMonth();
    }

    /**
     * 字符串转date
     *
     * @param dateStr 日期
     * @param format  日期格式
     */
    public static Date formatDateStr(String dateStr, String format) throws ParseException {
        ParamUtil.checkStrParam(dateStr, "dateStr");
        ParamUtil.checkStrParam(format, "format");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        Date date = simpleDateFormat.parse(dateStr);
        return date;
    }

    /**
     * 获取某天开始时间戳
     *
     * @param time 时间戳秒
     * @return 秒
     */
    public static int getDayStartTime(int time) {
        LocalDate localDate = Instant.ofEpochSecond(time).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDateTime todayStart = LocalDateTime.of(localDate, LocalTime.MIN);
        long startTime = todayStart.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) startTime;
    }

    /**
     * 获取某天结束时间戳
     *
     * @param time 时间戳秒
     * @return 秒
     */
    public static int getDayEndTime(int time) {
        LocalDate localDate = Instant.ofEpochSecond(time).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDateTime todayEnd = LocalDateTime.of(localDate, LocalTime.MAX);
        long endTime = todayEnd.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) endTime;
    }

    /**
     * 获取当天开始时间戳
     *
     * @return 秒
     */
    public static int getDayStartTime() {
        return getDayStartTime(getTime());
    }

    /**
     * 获取当天结束时间戳
     *
     * @return 秒
     */
    public static int getDayEndTime() {
        return getDayEndTime(getTime());
    }

    /**
     * 获取某月开始时间戳
     *
     * @param time 时间戳秒
     * @return 秒
     */
    public static int getMonthStartTime(int time) {
        LocalDate localDate = Instant.ofEpochSecond(time).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate firstDay = localDate.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime todayStart = LocalDateTime.of(firstDay, LocalTime.MIN);
        long startTime = todayStart.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) startTime;
    }

    /**
     * 获取某月结束时间戳
     *
     * @param time 时间戳秒
     * @return 秒
     */
    public static int getMonthEndTime(int time) {
        LocalDate localDate = Instant.ofEpochSecond(time).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime todayEnd = LocalDateTime.of(lastDay, LocalTime.MAX);
        long endTime = todayEnd.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) endTime;
    }

    /**
     * 获取当月开始时间戳
     *
     * @return 秒
     */
    public static int getMonthStartTime() {
        return getMonthStartTime(getTime());
    }

    /**
     * 获取当月结束时间戳
     *
     * @return 秒
     */
    public static int getMonthEndTime() {
        return getMonthEndTime(getTime());
    }

    /**
     * 获取某月开始时间戳
     *
     * @param year  年
     * @param month 月
     * @return 秒
     */
    public static int getMonthStartTime(int year, int month) {
        LocalDate localDate = LocalDate.of(year, month, 1);
        long startTime = localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().getEpochSecond();
        return (int) startTime;
    }

    /**
     * 获取某月结束时间戳
     *
     * @param year  年
     * @param month 月
     * @return 秒
     */
    public static int getMonthEndTime(int year, int month) {
        LocalDate localDate = LocalDate.of(year, month, 1);
        LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime todayEnd = LocalDateTime.of(lastDay, LocalTime.MAX);
        long endTime = todayEnd.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) endTime;
    }

    /**
     * 获取当前月份增加或减少月份后某月开始时间戳
     *
     * @param changeMonth 增加或减少的月份（负数为减少）
     * @return 秒
     */
    public static int getChangeMonthStartTime(int changeMonth) {
        LocalDate localDate = LocalDate.now();
        LocalDate plusDate = localDate.plusMonths(changeMonth);
        return getMonthStartTime(plusDate.getYear(), plusDate.getMonthValue());
    }

    /**
     * 获取当前月份增加或减少月份后某月结束时间戳
     *
     * @param changeMonth 增加或减少的月份（负数为减少）
     * @return 秒
     */
    public static int getChangeMonthEndTime(int changeMonth) {
        LocalDate localDate = LocalDate.now();
        LocalDate plusDate = localDate.plusMonths(changeMonth);
        return getMonthEndTime(plusDate.getYear(), plusDate.getMonthValue());
    }

    /**
     * 获取某年开始时间戳
     *
     * @param time 时间戳秒
     * @return 秒
     */
    public static int getYearStartTime(int time) {
        LocalDate localDate = Instant.ofEpochSecond(time).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate firstDay = localDate.with(TemporalAdjusters.firstDayOfYear());
        LocalDateTime todayStart = LocalDateTime.of(firstDay, LocalTime.MIN);
        long startTime = todayStart.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) startTime;
    }

    /**
     * 获取某年结束时间戳
     *
     * @param time 时间戳秒
     * @return 秒
     */
    public static int getYearEndTime(int time) {
        LocalDate localDate = Instant.ofEpochSecond(time).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfYear());
        LocalDateTime todayEnd = LocalDateTime.of(lastDay, LocalTime.MAX);
        long endTime = todayEnd.toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        return (int) endTime;
    }

    public static boolean checkTimeInterval(Integer startTime, Integer intervalMinutes) {
        if (null == startTime) {
            return false;
        }

        long lastMinutes = TimeUtil.calDifference(startTime, TimeUtil.getTime(), TimeUnit.MINUTES);
        if (lastMinutes > 0 && (lastMinutes % (intervalMinutes)) == 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 按当前时间获取最近的时间范围
     *
     * @param recent   最近时间，日、小时、分钟
     * @param timeUnit 最近时间单位
     * @return
     */
    public static TimeRange getRecentTimeRange(int recent, TimeUnit timeUnit) {
        ParamUtil.checkObjParam(timeUnit, "timeUnit");
        if (recent <= 0) {
            throw new RuntimeException("recent 不能小于或等于0");
        }

        int bTime = (int) (calAddOrMinus(getTime(), -recent, timeUnit).getTime().getTime() / 1000L);
        int eTime = getTime();

        return new TimeRange(bTime, eTime);
    }

    /**
     * 以当前时间为基准下个周期的时间
     *
     * @param per
     * @param timeUnit
     * @return
     */
    public static Date getNextPeriodTime(int per, TimeUnit timeUnit) {
        if (per < 0) {
            throw new RuntimeException("per不能小于0");
        }

        String perString = per < 10 ? "0" + per : "" + per;
        if (TimeUnit.SECONDS.equals(timeUnit)) {
            if (per > 59) {
                throw new RuntimeException("per不能大于59");
            }

            Date nextPeriodTime = calAddOrMinus(getCurrentDate(), 1, TimeUnit.MINUTES).getTime();
            String nextTimeStr = toString(nextPeriodTime, "yyyy-MM-dd HH:mm").concat(":").concat(perString);
            return toDate(nextTimeStr);
        }

        if (TimeUnit.MINUTES.equals(timeUnit)) {
            if (per > 59) {
                throw new RuntimeException("per不能大于59");
            }

            Date nextPeriodTime = calAddOrMinus(getCurrentDate(), 1, TimeUnit.HOURS).getTime();
            String nextTimeStr = toString(nextPeriodTime, "yyyy-MM-dd HH").concat(":").concat(perString).concat(":00");
            return toDate(nextTimeStr);
        }

        if (TimeUnit.HOURS.equals(timeUnit)) {
            if (per > 23) {
                throw new RuntimeException("per不能大于23");
            }

            Date nextPeriodTime = calAddOrMinus(getCurrentDate(), 1, TimeUnit.DAYS).getTime();
            String nextTimeStr = toString(nextPeriodTime, "yyyy-MM-dd ").concat(perString).concat(":00:00");
            return toDate(nextTimeStr);
        }

        throw new RuntimeException("仅支持时、分、秒周期");
    }

    public static String formatDateNotice(Integer startTime) {
        String dayFormat = null;

        Long difTimes = calDifference(startTime, TimeUnit.HOURS);
        Integer difDays = (difTimes.intValue() / 24);
        long remainderHours = difTimes % 24;

        Long curHourPlusRemain = getHour() + Math.abs(remainderHours);
        switch (difDays) {
            case -2:
                if (curHourPlusRemain <= 24) {
                    dayFormat = "后天";
                } else {
                    dayFormat = null;
                }
                break;
            case -1:
                if (curHourPlusRemain <= 24) {
                    dayFormat = "明天";
                } else {
                    dayFormat = "后天";
                }
                break;
            case 0:
                if (curHourPlusRemain <= 24) {
                    dayFormat = "今天";
                } else {
                    if (difTimes < 0) {
                        dayFormat = "明天";
                    } else {
                        dayFormat = "昨天";
                    }
                }
                break;
            case 1:
                if (curHourPlusRemain <= 24) {
                    dayFormat = "昨天";
                } else {
                    dayFormat = "前天";
                }
                break;
            case 2:
                if (curHourPlusRemain <= 24) {
                    dayFormat = "前天";
                } else {
                    dayFormat = null;
                }
                break;
            default:
                dayFormat = null;
                break;
        }

        if (dayFormat == null) {
            return toString(startTime, "yyyy-MM-dd HH:mm");
        } else {
            return dayFormat + toString(startTime, "HH:mm");
        }

    }

    public static Date getBeginOfDay(Date date) {
        if(date == null){
            return null;
        }

        Instant instant = date.toInstant();
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        LocalDate localDate = zonedDateTime.toLocalDate();

        // 获取当天的第一秒的时间
        LocalDateTime startOfDay = localDate.atTime(0, 0, 0, 0);

        // 转换为 Date 类型并返回
        Date startDay = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        return startDay;
    }

    public static String getBeginOfDayToStr(Date date) {
        // 转换为 Date 类型并返回
        Date startDay = getBeginOfDay(date);
        return toString(startDay);
    }

    public static Date getEndOfDay(Date date) {
        if(date == null){
            return null;
        }

        // 将 Date 转换为 LocalDate
        Instant instant = date.toInstant();
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        LocalDate localDate = zonedDateTime.toLocalDate();

        // 获取当天的最后一秒的时间
        LocalDateTime endOfDay = localDate.atTime(23, 59, 59, 999_000_000);

        // 转换为 Date 类型并返回
        Date endDay = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        return endDay;
    }

    public static String getEndOfDayToStr(Date date) {
        // 转换为 Date 类型并返回
        Date endDay = getEndOfDay(date);
        return toString(endDay);
    }

    public static Date getBeginOfDay(String dateStr) {
        if(StringUtils.isBlank(dateStr)){
            return null;
        }

        Date date = toDate(dateStr);
        return getBeginOfDay(date);
    }

    public static String getBeginOfDayToStr(String dateStr) {
        if(StringUtils.isBlank(dateStr)){
            return null;
        }

        Date date = toDate(dateStr);
        return getBeginOfDayToStr(date);
    }

    public static Date getEndOfDay(String dateStr) {
        if(StringUtils.isBlank(dateStr)){
            return null;
        }

        Date date = toDate(dateStr);
        return getEndOfDay(date);
    }

    public static String getEndOfDayToStr(String dateStr) {
        if(StringUtils.isBlank(dateStr)){
            return null;
        }

        Date date = toDate(dateStr);
        return getEndOfDayToStr(date);
    }

    /**
     * int时间转Date类型
     * @param intTime
     * @return
     */
    public static Date toDate(Integer intTime) {
        if (intTime == null) {
            throw new IllegalArgumentException("intTime must not be null");
        }

        return new Date(intTime * 1000L);
    }

    public static void main(String[] args) throws Exception {
        /*System.out.println(TimeUtil.calDifference(1660026164, TimeUtil.getTime(), TimeUnit.SECONDS));
        System.out.println(TimeUtil.calDifference(1660026164, TimeUtil.getTime(), TimeUnit.MINUTES));
        System.out.println(TimeUtil.calDifference(1660026164, TimeUtil.getTime(), TimeUnit.HOURS));
        System.out.println(TimeUtil.calDifference(1660026164, TimeUtil.getTime(), TimeUnit.DAYS));
        System.out.println(TimeUtil.formatSeconds(24 * 60 * 60 + 62,TimeUnit.MINUTES,true));*/

        /*double v = (1.2 * 24 * 60 * 60) % (1 * 24 * 60 * 60);
        System.out.println(47 / 24);
        System.out.println(47 % 24);
        List<TTest> tests = new ArrayList<>();
        tests.add(new TTest("1","2",1));
        tests.add(new TTest("1","1",2));
        tests.add(new TTest("1","2",1));

        TTest t1 = new TTest("1","1",1);
        System.out.println(tests.contains(t1));
        System.out.println(tests.size());*/

        System.out.println(toString(new Date(),"yyyyMM"));
    }

    @Data
    @AllArgsConstructor
    private static class TTest {
        private String p1;
        private String p2;
        private Integer a;
    }
}
