package com.ruoyi.pmrsch.common.exception;

public class ApiException extends RuntimeException {

    private static final long serialVersionUID = -738439098559776438L;

    /** 正常响应 */
    public static final int NORMAL = 200;
    
    /** 重定向跳转 */
    public static final int REDIRECT = 300;

    /** 错误的请求 **/
    public static final int BAD_REQUEST = 400;

    /** 无效的认证 */
    public static final int AUTH_INVALID = 401;
    
    /** 权限不足 */
    public static final int AUTH_DENIED  = 402;

    /** 表单验证失败 */
    public static final int FORM_INVALID = 403;
    
    /** 地址未开放 */
    public static final int NOT_FOUND =  404;
    
    /** 系统未知异常 */
    public static final int SYSTEM = 500;

    /** 系统初始化 */
    public static final int SYSTEM_INIT = 501;
    /**
     * 执行失败异常 可表示系统执行未达到期望效果的状态
     * 未指定状态码时默认该状态码
     */
    public static final int OPERATION = 499;

    public static final int AREA_SELECT=205;

    public static final int stock_Insufficient = 601;

    /**
     * （默认）执行失败状态码
     */
    protected int statusCode = OPERATION;

    public int getStatusCode() {
        return statusCode;
    }

    public ApiException() {
        super();
    }

    public ApiException(String message) {
        super(message);
    }

    public ApiException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }

}
