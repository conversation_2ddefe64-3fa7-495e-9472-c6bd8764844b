package com.ruoyi.pmrsch.common.tools;

import org.springframework.core.io.ClassPathResource;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * YAML配置文件读取工具类
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public class ConfigUtil {
    private static Map<String, Object> yamlMap;

    static {
        try {
            loadYaml();
        } catch (IOException e) {
            throw new RuntimeException("Failed to load YAML configuration", e);
        }
    }

    /**
     * 加载YAML配置文件
     */
    private static void loadYaml() throws IOException {
        ClassPathResource resource = new ClassPathResource("application.yml");
        try (InputStream inputStream = resource.getInputStream()) {
            Yaml yaml = new Yaml(new Constructor(Map.class));
            yamlMap = yaml.load(inputStream);
        }
    }

    /**
     * 获取YAML配置值，如果不存在则返回默认值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getValue(String key, String defaultValue) {
        if (yamlMap == null) {
            return defaultValue;
        }

        String[] keys = key.split("\\.");
        Object value = yamlMap;
        
        for (String k : keys) {
            if (value instanceof Map) {
                value = ((Map<?, ?>) value).get(k);
            } else {
                return defaultValue;
            }
        }

        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 获取YAML配置值，如果不存在则返回默认值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static Integer getValue(String key, Integer defaultValue) {
        String value = ConfigUtil.getValue(key, (String)null);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 获取YAML配置值，如果不存在则返回默认值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static Boolean getValue(String key, Boolean defaultValue) {
        String value = ConfigUtil.getValue(key, (String)null);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }

    /**
     * 获取YAML配置值，如果不存在则返回默认值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static Double getValue(String key, Double defaultValue) {
        String value = ConfigUtil.getValue(key, (String)null);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 获取YAML配置值，如果不存在则抛出异常
     *
     * @param key 配置键
     * @return 配置值
     * @throws IllegalStateException 当配置不存在或无效时抛出异常
     */
    public static String getRequiredString(String key) {
        String value = ConfigUtil.getValue(key, (String)null);
        if (value == null) {
            throw new IllegalStateException("Required configuration '" + key + "' is not set");
        }
        return value;
    }

    /**
     * 获取YAML配置值，如果不存在则抛出异常
     *
     * @param key 配置键
     * @return 配置值
     * @throws IllegalStateException 当配置不存在或无效时抛出异常
     */
    public static Integer getRequiredInteger(String key) {
        String value = getRequiredString(key);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            throw new IllegalStateException("Configuration '" + key + "' is not a valid integer: " + value);
        }
    }

    /**
     * 获取YAML配置值，如果不存在则抛出异常
     *
     * @param key 配置键
     * @return 配置值
     * @throws IllegalStateException 当配置不存在或无效时抛出异常
     */
    public static Boolean getRequiredBoolean(String key) {
        String value = getRequiredString(key);
        if (!value.equalsIgnoreCase("true") && !value.equalsIgnoreCase("false")) {
            throw new IllegalStateException("Configuration '" + key + "' is not a valid boolean: " + value);
        }
        return Boolean.parseBoolean(value);
    }

    /**
     * 获取YAML配置值，如果不存在则抛出异常
     *
     * @param key 配置键
     * @return 配置值
     * @throws IllegalStateException 当配置不存在或无效时抛出异常
     */
    public static Double getRequiredDouble(String key) {
        String value = getRequiredString(key);
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            throw new IllegalStateException("Configuration '" + key + "' is not a valid double: " + value);
        }
    }
} 