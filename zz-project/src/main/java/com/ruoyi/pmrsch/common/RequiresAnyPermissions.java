package com.ruoyi.pmrsch.common;/**
 * <AUTHOR>
 * @Date 2025/2/23
 */

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 描述
 * @className RequiresAnyPermissions
 * <AUTHOR>
 * @date 2025年02月23日 14:58
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresAnyPermissions {
    String[] value();
}
