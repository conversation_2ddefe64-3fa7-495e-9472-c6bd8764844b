package com.ruoyi.pmrsch.common.tools;/**
 * <AUTHOR>
 * @Date 2025/2/5
 */

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述
 * @className SetUtil
 * <AUTHOR>
 * @date 2025年02月05日 17:35
 */
public class SetUtil {
    public static List<String> intersection(List<String> list1,List<String> list2){
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);

        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        return intersection.stream().collect(Collectors.toList());
    }

    /**
     * 比较list1、list2
     * @param list1
     * @param list2
     * @return list2 移除 list1 相同的元素后剩下的元素
     */
    public static List<String> difference(List<String> list1,List<String> list2){
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);

        Set<String> missingInList = new HashSet<>(set2);
        missingInList.removeAll(set1);
        return missingInList.stream().collect(Collectors.toList());
    }
}
