package com.ruoyi.pmrsch.common.entity;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ruoyi.pmrsch.common.exception.ApiException;

public interface EnumIntegerInterface {

    @JsonValue
    int getValue();

    static <E extends Enum<E> & EnumIntegerInterface> E getByValue(int value, Class<E> clazz) {
        for (E i : clazz.getEnumConstants()) {
            if (i.getValue() == value) {
                return i;
            }
        }
        return null;
    }

    static <E extends Enum<E> & EnumIntegerInterface> E checkValue(int value, Class<E> clazz, String errName) {
        E e = getByValue(value, clazz);
        if (e == null) {
            throw new ApiException(errName + "参数错误");
        }
        return e;
    }
}
