package com.ruoyi.pmrsch.common.tools;/**
 * <AUTHOR>
 * @Date 1/20/21
 */

import com.ruoyi.pmrsch.common.exception.ApiException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 参数工具
 * @ClassName ParamUil
 * <AUTHOR>
 * @Date
 **/
public class ParamUtil {

    private final static String MOBILE_REGEX = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";

    private final static List<String> illegalParam = Arrays.asList("#", "$", "%", "&", "null", "undefined", " ");

    /**
     * 校验参数是否为空
     * */
    public static void checkObjParam(Object param, String paramName) {
        if(null == param){
            throw new ApiException(paramName + "不能为空", ApiException.FORM_INVALID);
        }
    }

    /**
     * 校验参数是否为空
     * */
    public static void checkStrParam(String param, String paramName) {
        if(StringUtils.isBlank(param)){
            throw new ApiException(paramName + "不能为空",ApiException.FORM_INVALID);
        }
    }

    /**
     * 校验参数是否为空
     * */
    public static void checkArrParam(Collection param, String paramName) {
        if(CollectionUtils.isEmpty(param)){
            throw new ApiException(paramName + "不能为空",ApiException.FORM_INVALID);
        }
    }

    /**
     * 校验最小值（可以为数字和字符串）
     * */
    public static void checkMinParam(Object param, String paramName, Integer min) {
        checkObjParam(param, paramName);
        _checkMinParam(param, paramName, min);
    }

    /**
     * 校验最大值（可以为数字和字符串）
     * */
    public static void checkMaxParam(Object param, String paramName, Integer max) {
        checkObjParam(param, paramName);
        _checkMaxParam(param, paramName, max);
    }

    /**
     * 校验最小值和最大值（可以为数字和字符串）
     * */
    public static void checkMinAndMaxParam(Object param, String paramName, Integer min, Integer max) {
        checkObjParam(param, paramName);
        _checkMinParam(param, paramName, min);
        _checkMaxParam(param, paramName, max);
    }

    /**
     * 校验参数是否为空
     * */
    public static void checkApiObjParam(Object param, String paramName) {
        if(null == param){
            throw new ApiException(paramName + "不能为空",ApiException.FORM_INVALID);
        }
    }

    /**
     * 校验参数是否非法
     * */
    public static void checkApiStrParamIllegal(String param, String paramName) {
        checkApiStrParam(param, paramName);
        if (illegalParam.contains(param)) {
            throw new ApiException(paramName + "存在非法的字符",ApiException.FORM_INVALID);
        }
    }

    /**
     * 校验参数是否为空
     * */
    public static void checkApiStrParam(String param, String paramName) {
        if(StringUtils.isBlank(param)){
            throw new ApiException(paramName + "不能为空",ApiException.FORM_INVALID);
        }
    }

    /**
     * 校验最小值（可以为数字和字符串）
     * */
    public static void checkApiMinParam(Object param, String paramName, Integer min) {
        checkApiObjParam(param, paramName);
        _checkApiMinParam(param, paramName, min);
    }

    /**
     * 校验最大值（可以为数字和字符串）
     * */
    public static void checkApiMaxParam(Object param, String paramName, Integer max) {
        checkApiObjParam(param, paramName);
        _checkApiMaxParam(param, paramName, max);
    }

    /**
     * 校验最小值和最大值（可以为数字和字符串）
     * */
    public static void checkApiMinAndMaxParam(Object param, String paramName, Integer min, Integer max) {
        checkApiObjParam(param, paramName);
        _checkApiMinParam(param, paramName, min);
        _checkApiMaxParam(param, paramName, max);
    }

    /**
     * 校验小数最小值
     * @param maxPlace 最大位数(不能大于5)
     * */
    public static void checkApiMinDecimalParam(Object param, String paramName, Integer min, Integer maxPlace) {
        checkApiMinParam(param, paramName, min);
        _checkApiDecimalMaxPlace(param, paramName, maxPlace);
    }

    /**
     * 校验小数最大值
     * @param maxPlace 最大位数(不能大于5)
     * */
    public static void checkApiMaxDecimalParam(Object param, String paramName, Integer max, Integer maxPlace) {
        checkApiMaxParam(param, paramName, max);
        _checkApiDecimalMaxPlace(param, paramName, maxPlace);
    }

    /**
     * 校验小数最小值和最大值
     * @param maxPlace 最大位数(不能大于5)
     * */
    public static void checkApiMinAndMaxDecimalParam(Object param, String paramName, Integer min, Integer max, Integer maxPlace) {
        checkApiMinAndMaxParam(param, paramName, min, max);
        _checkApiDecimalMaxPlace(param, paramName, maxPlace);
    }

    /**
     * 校验参数是否为空
     * */
    public static void checkApiArrParam(Collection param, String paramName) {
        if(CollectionUtils.isEmpty(param)){
            throw new ApiException(paramName + "不能为空",ApiException.FORM_INVALID);
        }
    }

    public static Boolean checkMobile(String mobile) {
        return Pattern.matches(MOBILE_REGEX, mobile);
    }

    /**
     * 判断传入值是否为空，如果为空则返回默认值，否则返回原值
     * @param value 需检查的原值
     * @param defaultValue 为空时返回默认值
     * @return
     */
    public static <T> T blankDefault(T value, T defaultValue) {
        if(null == value)
        {
            return defaultValue;
        }

        if(value instanceof String && StringUtils.isBlank((String)value)){
            return defaultValue;
        }

        return value;
    }

    public static <T> boolean strIsBlank(T value){
        if(null == value){
            return true;
        }
        return StringUtils.isBlank(value.toString());
    }

    public static <T> boolean strIsNotBlank(T value){
        if(null == value){
            return false;
        }
        return StringUtils.isNotBlank(value.toString());
    }

    public static void main(String[] args) throws Exception {
    }

    public static String requireNotBlank(String value, String errorMsg) {

        if (StringUtils.isBlank(value)) {
            throw new ApiException(errorMsg,ApiException.FORM_INVALID);
        }

        return value;
    }

    public static  <T> T requireNotNull(T value, String errorMsg) {
        try{
            if(value == null){
                return Objects.requireNonNull(value,errorMsg);
            }

            if(value instanceof String){
                if(StringUtils.isBlank((String)value)){
                    throw new RuntimeException(errorMsg);
                }else{
                    return value;
                }
            }

            return Objects.requireNonNull(value,errorMsg);

        }catch (Exception ex){
            throw new ApiException(ex.getMessage(),ApiException.FORM_INVALID);
        }

    }

    public static boolean ifNumber(Object param) {
        return param instanceof Integer ||
                param instanceof Float ||
                param instanceof Double ||
                param instanceof Short ||
                param instanceof Long;
    }

    /**
     * 替换字符串中的占位符 {}
     * @param template 模板字符串，包含占位符 {}
     * @param args 替换占位符的参数
     * @return 替换后的字符串
     */
    public static String format(String template, Object... args) {
        if (template == null) {
            return null;
        }
        StringBuilder result = new StringBuilder();
        int lastPos = 0;
        int placeholderIndex = 0;

        while (true) {
            int placeholderPos = template.indexOf("{}", lastPos);
            if (placeholderPos == -1) {
                break;
            }
            if (placeholderIndex >= args.length) {
                throw new IllegalArgumentException("占位符数量超过了参数数量");
            }

            // 添加占位符前的部分
            result.append(template, lastPos, placeholderPos);

            // 添加对应的参数
            result.append(args[placeholderIndex]);

            // 更新位置
            lastPos = placeholderPos + 2;
            placeholderIndex++;
        }

        // 添加剩余的部分
        result.append(template.substring(lastPos));

        return result.toString();
    }

    private static void _checkMinParam(Object param, String paramName, Integer min) {
        if (!checkMinParam(param, min)) {
            throw new ApiException(paramName + "不能小于" + min,ApiException.FORM_INVALID);
        }
    }

    private static void _checkMaxParam(Object param, String paramName, Integer max) {
        if (!checkMaxParam(param, max)) {
            throw new ApiException(paramName + "不能大于" + max,ApiException.FORM_INVALID);
        }
    }

    private static void _checkApiMinParam(Object param, String paramName, Integer min) {
        if (!checkMinParam(param, min)) {
            throw new ApiException(paramName + "不能小于" + min,ApiException.FORM_INVALID);
        }
    }

    private static void _checkApiMaxParam(Object param, String paramName, Integer max) {
        if (!checkMaxParam(param, max)) {
            throw new ApiException(paramName + "不能大于" + max,ApiException.FORM_INVALID);
        }
    }

    private static void _checkApiDecimalMaxPlace(Object param, String paramName, Integer maxPlace) {
        if (!checkDecimalMaxPlace(param, maxPlace)) {
            throw new ApiException(paramName + "小数位数不能大于" + maxPlace + "位",ApiException.FORM_INVALID);
        }
    }

    private static Boolean checkDecimalMaxPlace(Object param, Integer maxPlace) {
        Integer validMaxPlace = 5;
        if (maxPlace > validMaxPlace) {
            maxPlace = validMaxPlace;
        }
        String str = String.valueOf(param);
        String decimalPattern = "^\\d+(\\.\\d{1," + maxPlace + "})?$";
        return str.matches(decimalPattern);
    }

    private static boolean checkMinParam(Object param, Integer min) {
        if (ParamUtil.ifNumber(param)) {
            BigDecimal decimal = new BigDecimal(String.valueOf(param));
            BigDecimal minDecimal = new BigDecimal(String.valueOf(min));
            if (decimal.compareTo(minDecimal) == -1) {
                return false;
            }
            return true;
        }
        if (param instanceof String) {
            String paramStr = (String)param;
            if (paramStr.length() < min) {
                return false;
            }
            return true;
        }
        throw new ApiException("参数必须为数字或字符串",ApiException.FORM_INVALID);
    }

    private static boolean checkMaxParam(Object param, Integer max) {
        if (ParamUtil.ifNumber(param)) {
            BigDecimal decimal = new BigDecimal(String.valueOf(param));
            BigDecimal maxDecimal = new BigDecimal(String.valueOf(max));
            if (decimal.compareTo(maxDecimal) == 1) {
                return false;
            }
            return true;
        }
        if (param instanceof String) {
            String paramStr = (String)param;
            if (paramStr.length() > max) {
                return false;
            }
            return true;
        }
        throw new ApiException("参数必须为数字或字符串",ApiException.FORM_INVALID);
    }
}
