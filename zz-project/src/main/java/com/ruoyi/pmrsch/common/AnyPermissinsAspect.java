package com.ruoyi.pmrsch.common;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import com.ruoyi.common.core.context.PermissionContextHolder;
import com.ruoyi.common.utils.StringUtils;

@Aspect
@Component
public class AnyPermissinsAspect {
    @Before("@annotation(controllerRequiresAnyPermissions)")
    public void doBeforeAny(JoinPoint point, RequiresAnyPermissions controllerRequiresAnyPermissions) throws Throwable
    {
        handleRequiresAnyPermissions(point, controllerRequiresAnyPermissions);
    }

    protected void handleRequiresAnyPermissions(final JoinPoint joinPoint, RequiresAnyPermissions requiresAnyPermissions)
    {
        PermissionContextHolder.setContext(StringUtils.join(requiresAnyPermissions.value(), ","));
    }
}
