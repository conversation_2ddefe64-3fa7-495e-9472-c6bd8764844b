package com.ruoyi.pmrsch.common.tools;/**
 * <AUTHOR>
 * @Date 2025/1/21
 */

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

/**
 * 描述
 * @className BeanUtil
 * <AUTHOR>
 * @date 2025年01月21日 13:50
 */
public class BeanUtil {

    /**
     * 复制源对象的属性到目标对象
     * @param source 源对象
     * @param target 目标对象
     * @param skipNull 是否跳过空属性值
     * @param <T> 源对象类型
     * @param <R> 目标对象类型
     */
    public static <T, R> void copyProperties(T source, R target, boolean skipNull) {
        if (source == null || target == null) {
            throw new IllegalArgumentException("Source and target objects cannot be null.");
        }

        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();

        // 遍历源对象的所有字段
        Field[] sourceFields = sourceClass.getDeclaredFields();
        for (Field sourceField : sourceFields) {
            sourceField.setAccessible(true); // 确保可以访问私有字段

            try {
                Object sourceValue = sourceField.get(source); // 获取源字段的值

                // 如果跳过空值且当前值为空，则跳过
                if (skipNull && (sourceValue == null || "".equals(sourceValue.toString().trim()))) {
                    continue;
                }

                // 检查字段是否是static，如果是，则跳过
                if (Modifier.isStatic(sourceField.getModifiers())) {
                    continue;
                }

                // 在目标对象中查找同名字段
                Field targetField = targetClass.getDeclaredField(sourceField.getName());
                targetField.setAccessible(true); // 确保可以访问私有字段

                // 将源字段的值复制到目标字段
                targetField.set(target, sourceValue);

            } catch (NoSuchFieldException e) {
                // 如果目标对象中没有同名字段，忽略
                continue;
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access field: " + sourceField.getName(), e);
            }
        }
    }

    public static void main(String[] args) {
        // 示例：测试代码
        class Source {
            private String name;
            private int age = 25;
            private String address = null;

            @Override
            public String toString() {
                return "Source{name='" + name + "', age=" + age + ", address='" + address + "'}";
            }
        }

        class Target {
            private String name = "dfd";
            private int age = 88;
            private String address;

            @Override
            public String toString() {
                return "Target{name='" + name + "', age=" + age + ", address='" + address + "'}";
            }
        }

        Source source = new Source();
        Target target = new Target();

        // 复制属性，跳过空值
        copyProperties(source, target, true);
        System.out.println("Source: " + source);
        System.out.println("Target (skipNull=true): " + target);

        // 复制属性，不跳过空值
        copyProperties(source, target, false);
        System.out.println("Target (skipNull=false): " + target);
    }
}
