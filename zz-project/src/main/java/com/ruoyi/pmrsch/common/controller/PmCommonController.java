package com.ruoyi.pmrsch.common.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.service.ICustomerService;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.service.IProjectTaskBudgetService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Controller
@RequestMapping("/pmrsch/common")
public class PmCommonController extends BaseController{
    @Autowired
    private ICustomerService customerService;

    @Autowired
    private IProjectTaskBudgetService budgetService;

    /**
     * 查询客户信息列表
     */
    @RequiresPermissions("pmdata:customer:list")
    @GetMapping("/all")
    @ResponseBody
    public Map listAll(){
        List<Customer> customers = customerService.selectCustomerList(new Customer());

        List<CusRsp> cusRsps = customers.stream().map(c -> {return new CusRsp(c.getId(),c.getName());}).collect(Collectors.toList());
        Map data = new HashMap<>();

        data.put("value",cusRsps);
        return data;
    }

    /**
     * 预算
     */
    @RequiresPermissions("task:budget:apply:view")
    @GetMapping("/budget/view/{id}")
    public String appyView(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskBudget projectTaskBudget = budgetService.selectProjectTaskBudgetById(id);
        mmap.put("projectTaskBudget", projectTaskBudget);
        return "fragment/budget/view";
    }

    @Data
    @AllArgsConstructor
    private class CusRsp {
        private String id;
        private String name;
    }
}
