package com.ruoyi.pmrsch.common.tools;

import com.ruoyi.pmrsch.common.exception.ApiException;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

/**
 * 随机数生成
 * <AUTHOR>
 */
public class RandomStringUtil {
    /**
     * 2008-03-27
     */
    private static final long BEING_TIMESTAMP = 1206576000;

    private static final int SUFFIX_LEN = 3;

    public static String getTime() {
        long mt = System.currentTimeMillis();
        long nowTimestamp = mt / 1000L;
        return String.valueOf(nowTimestamp - BEING_TIMESTAMP);
    }

    /**
     * 生成 MD5 字符串
     * @param content 原始字符串
     * @param salt 加盐串
     * @return MD5 字符串
     */
    public static String getMd5(String content, String salt) {
        try {
            // 获取 MessageDigest 实例，指定 MD5 算法
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将加盐串转换为字节数组
            byte[] saltBytes = salt.getBytes();
            // 更新摘要，添加原始字符串和盐的字节数组
            md.update(saltBytes);
            md.update(content.getBytes());
            // 完成哈希计算，获取结果字节数组
            byte[] digest = md.digest();
            // 将结果字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            // 处理异常，MD5 算法不存在的情况
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }

    public static String getMd5(String content) {
        return getMd5(content, "#E%Rywetrtyw^%^^@#wtet");
    }

    /**
     * 随机字符串 (包含[a-z][A-Z][0-9])
     */
    public static String genRandomStr(int length) {
        return genRandomStr(null, length);
    }

    public static String genRandomStr(String gather, int length) {
        if (gather == null || "".equals(gather)) {
            gather = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        }
        char[] charArray = gather.toCharArray();
        int count = charArray.length;
        StringBuffer str = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            str.append(charArray[random.nextInt(count)]);
        }

        return str.toString();
    }

    /**
     * 32位UUID
     */
    public static String gen32UUID() {
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }
    /**
     * 源于QeePHP Fastuuid 缺省生成策略 18位
     */
    public static String gen18UUID() {
        long mt = System.currentTimeMillis();
        long nowTimestamp = mt / 1000L;
        long nanoTime = System.nanoTime();

        Random random = new Random();
        String nanoTimeStr = String.valueOf(nanoTime);
        nanoTimeStr = nanoTimeStr.substring(nanoTimeStr.length() - 7, nanoTimeStr.length() - 1);

        return nowTimestamp - BEING_TIMESTAMP + nanoTimeStr + getRandomLengthNum(3);
    }

    /**
     * 获取随机自定义长度的字符串
     *  默认长度为10 ，有大写字母和数字拼成的字符串
     * @param length 自定义长度 传null默认长度10 传小于1的数字会抛异常
     * @return 字符串
     */
    public static String getRandomLengthStr(int length){
        StringBuffer buffer = new StringBuffer("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        StringBuffer sb = new StringBuffer();
        Random r = new Random();
        r.setSeed(System.currentTimeMillis());
        int lengths = buffer.length();
        if (length <= 0){
            throw new ApiException("随机字符串长度不能小于1", ApiException.FORM_INVALID);
        }
        IntStream.range(0, length).forEach(i -> sb.append(buffer.charAt(r.nextInt(lengths))));
        return sb.toString();
    }

    /**
     * 获取随机自定义长度的数字
     * @param length 自定义长度 传小于1的数字会抛异常
     * @return 字符串
     */
    public static String getRandomLengthNum(int length){
        if (length <= 0){
            throw new ApiException("随机字符串长度不能小于1", ApiException.FORM_INVALID);
        }
        String [] s={"0","1","2","3","4","5","6","7","8","9"};
        StringBuilder sb=new StringBuilder();
        Random random = new Random();
        for (int i = 0; i <length ; i++) {
            int number=random.nextInt(s.length);
            sb.append(s[number]);
        }
        return sb.toString();
    }

    public static String genSNByDate(){
        return genSNByDate(TimeUtil.getTime());
    }

    /**
     * 生成20位SN码（含字母），格式为：年月日-时分秒-随机串，如 210401-150000-ABC123
     * @param createTime 创建时间，单位为秒
     * @return 20位SN码
     */
    public static String genSNByDate(int createTime){
        return TimeUtil.timeFormatted(createTime * 1000L, "yyMMdd-HHmmss-") + getRandomLengthStr(3);
    }

    public static String genSNByDateInc(){
        return genSNByDateInc(TimeUtil.getTime());
    }

    /**
     * 生成20位SN码（纯数字+横杠），格式为：年月日-时分秒-随机串，如 210401-150000-361433
     * @param createTime 创建时间，单位为秒
     * @return 20位SN码，纯数字
     */
    public static String genSNByDateInc(int createTime){
        return TimeUtil.timeFormatted(createTime * 1000L, "yyMMdd-") + genRandomStr("1234567890", 4);
    }

    public static void main(String[] args) {
        System.out.println(gen18UUID());
        System.out.println(gen18UUID().length());
    }
}
