package com.ruoyi.pmrsch.file.manager;

import com.ruoyi.pmrsch.file.AliyunOssUtil;
import com.ruoyi.pmrsch.file.entity.FileBType;
import com.ruoyi.pmrsch.file.entity.SaveFileResult;
import com.ruoyi.pmrsch.file.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.InputStream;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2022-12-07 17:34
 **/
@Service
@Slf4j
public class OssSaver implements IFileSaver {

    @Override
    public SaveFileResult saveFile(FileBType fileBType, InputStream fileStream, String fileName, String fileNameExt, String contentType) {
        if(!fileName.contains(".")) {
            String extName = ".".concat(FileUtil.getExtNameByCType(contentType));
            fileName = fileName.concat(extName);
        }

        String fullFilePath;
        if(StringUtils.isNotBlank(fileBType.getTag())) {
            fullFilePath = fileBType.getFullPath().concat(fileName);
        }else {
            fullFilePath = fileName;
        }

        boolean success = AliyunOssUtil.putFile(fileBType, fullFilePath, fileStream, contentType);

        return success ?
                 new SaveFileResult(fullFilePath,
                                    AliyunOssUtil.getFileSize(fileBType, fullFilePath),
                                    AliyunOssUtil.getFileUrl(fileBType, fullFilePath),
                                    AliyunOssUtil.getFileMd5(fileBType, fullFilePath),
                                    AliyunOssUtil.getFileType(fileBType, fullFilePath)) : null;
    }

    @Override
    public void deleteFile(String fullFilePath, FileBType fileBType) {
        if(StringUtils.isBlank(fullFilePath) || fileBType == null){
            throw new RuntimeException("文件不存在");
        }
        AliyunOssUtil.deleteFile(fileBType, fullFilePath);
    }

    @Override
    public byte[] getFileBytes(String fullFilePath, FileBType fileBType) {
        if(StringUtils.isBlank(fullFilePath) || fileBType == null){
            throw new RuntimeException("文件不存在");
        }
        return AliyunOssUtil.getFile(fileBType, fullFilePath);
    }

}
