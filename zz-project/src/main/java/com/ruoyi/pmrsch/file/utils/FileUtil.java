package com.ruoyi.pmrsch.file.utils;

import cn.hutool.core.io.FileTypeUtil;
import com.ruoyi.pmrsch.common.ConfigKey;
import com.ruoyi.pmrsch.common.tools.ConfigUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.activation.MimetypesFileTypeMap;
import java.io.*;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * author: wutx<br/>
 * create on 2023/9/22<br/>
 * description:
 */
@Slf4j
public class FileUtil {

    public static String getFileMD5(byte[] bytes){
       return DigestUtils.md5Hex(bytes);
    }

    private final static Map<String,String> contentTypeToExtension = new HashMap<>();
    static {
        contentTypeToExtension.put("image/jpeg", "jpg");
        contentTypeToExtension.put("image/png", "png");
        contentTypeToExtension.put("image/gif", "gif");
        contentTypeToExtension.put("application/pdf", "pdf");
        contentTypeToExtension.put("text/plain", "txt");
        contentTypeToExtension.put("application/msword", "doc");
        contentTypeToExtension.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx");
        contentTypeToExtension.put("application/vnd.ms-excel", "xls");
        contentTypeToExtension.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx");
        contentTypeToExtension.put("application/vnd.ms-powerpoint", "ppt");
        contentTypeToExtension.put("application/vnd.openxmlformats-officedocument.presentationml.presentation", "pptx");
        contentTypeToExtension.put("application/zip", "zip");
        contentTypeToExtension.put("application/x-rar-compressed", "rar");
        contentTypeToExtension.put("application/x-tar", "tar");
        contentTypeToExtension.put("application/x-gzip", "gz");
        contentTypeToExtension.put("application/json", "json");
        contentTypeToExtension.put("application/xml", "xml");
        contentTypeToExtension.put("text/html", "html");
        contentTypeToExtension.put("text/css", "css");
        contentTypeToExtension.put("text/javascript", "js");
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名（包含扩展名）
     * @return 文件扩展名（不包含点）
     */
    public static String getExtNameByName(String filename) {
        // 检查文件名是否为空
        if (filename == null || filename.isEmpty()) {
            return "";
        }

        // 找到最后一个点的位置
        int lastDotIndex = filename.lastIndexOf('.');

        // 如果文件名中没有点，或者点在文件名的开头（如隐藏文件），则没有扩展名
        if (lastDotIndex == -1 || lastDotIndex == 0) {
            return "";
        }

        // 从最后一个点的位置开始截取，直到字符串末尾，去掉点
        return filename.substring(lastDotIndex + 1);
    }

    public static String getFileMD5(InputStream in) {
        MessageDigest digest;
        byte[] buffer = new byte[8192];
        int len;
        try{
            digest = MessageDigest.getInstance("MD5");
            while((len = in.read(buffer)) != - 1){
                digest.update(buffer,  0 , len);
            }
            BigInteger bigInt = new BigInteger( 1 , digest.digest());
            return  bigInt.toString( 16);
        }catch(Exception e) {
            log.error("获取文件md5值失败",e);
            return null;
        }
    }

    /** 根据文件名取得扩展名 */
    public static String getExtensionType(String originalFilename) {
        if(StringUtils.isEmpty(originalFilename)) {
            return "";
        }

        Pattern pattern = Pattern.compile("\\.([^\\.]*?)$");
        Matcher matcher = pattern.matcher(originalFilename);
        if (matcher.find()) {
            return matcher.group(1).toLowerCase();
        }

        return "";
    }

    /**
     * 复制文件
     * @param fromFilePath 源文件
     * @param toFilePath 目标文件
     * @return
     */
    public static boolean copy(String fromFilePath,String toFilePath){
        return copy(fromFilePath,toFilePath,false);
    }

    /**
     * 复制文件
     * @param fromFilePath 源文件
     * @param toFilePath 目标文件
     * @param isAppend true 添加,false 覆盖
     * @return
     */
    public static boolean copy(String fromFilePath,String toFilePath,boolean isAppend){

        try{
            if(StringUtils.isBlank(fromFilePath)){
                throw new RuntimeException("复制出错fromFilePath不能空");
            }

            if(StringUtils.isBlank(toFilePath)){
                throw new RuntimeException("复制文件出错toFilePath不能空");
            }

            File fromFile = new File(fromFilePath);
            if(!fromFile.exists()){
                throw new RuntimeException("复制文件原文件不存在");
            }

            InputStream fromFileStream = new FileInputStream(fromFile);
            return save(fromFileStream,toFilePath,isAppend) != null;

        }catch (Throwable ex){
            log.error("复制文件出错。fromFilePath[%s],toFilePath[%s]",fromFilePath,toFilePath,ex);
        }

        return false;
    }

    /**
     * 保存文件
     * @param content 内容
     * @param toFilePath 目标文件
     * @return
     */
    public static FileInfo save(String content,String toFilePath){
        return save(content,toFilePath,false);
    }

    /**
     * 保存文件
     * @param content 内容
     * @param toFilePath 目标文件
     * @param isAppend true 添加,false 覆盖
     * @return
     */
    public static FileInfo save(String content,String toFilePath, boolean isAppend){
        if(StringUtils.isBlank(content)){
            log.error("保存文件出错content不能空");
            return null;
        }

        return save(content.getBytes(StandardCharsets.UTF_8),toFilePath,isAppend);
    }

    /**
     * 保存文件
     * @param content 内容
     * @param toFilePath 目标文件
     * @return
     */
    public static FileInfo save(byte[] content,String toFilePath){
        return save(content,toFilePath,false);
    }

    /**
     * 保存文件
     * @param content
     * @param toFilePath 目标文件
     * @param isAppend true 添加,false 覆盖
     * @return
     */
    public static FileInfo save(byte[] content,String toFilePath,boolean isAppend){

        FileOutputStream toFileStream = null;
        try{
            if(content == null){
                throw new RuntimeException("保存文件出错data不能空");
            }

            if(StringUtils.isBlank(toFilePath)){
                throw new RuntimeException("保存文件出错toFilePath不能空");
            }

            File toFile = new File(toFilePath);
            boolean toFiledirExist = toFile.getParentFile().exists();
            boolean toFileExists = toFile.exists();

            if(!toFiledirExist){
                if(!toFile.getParentFile().mkdirs()){
                    throw new RuntimeException("创建文件目录失败"+toFile.getParentFile().getPath());
                }
            }

            if(!toFileExists){
                if(!toFile.createNewFile()){
                    throw new RuntimeException("创建文件失败"+toFile.getPath());
                }
            }

            toFileStream = new FileOutputStream(toFile,isAppend);
            toFileStream.write(content);
            return getFileInfo(toFile);
        }catch (Throwable ex){
            log.error("保存文件出错。toFilePath[%s]",toFilePath,ex);
        }finally {
            try{
                if(toFileStream != null){
                    toFileStream.close();
                }
            }catch (Throwable ex){

            }
        }

        return null;
    }

    /**
     * 保存文件
     * @param srcFileStream
     * @param toFilePath 目标文件
     * @param isAppend true 添加,false 覆盖
     * @return
     */
    public static FileInfo save(InputStream srcFileStream,String toFilePath,boolean isAppend){
        if(srcFileStream == null){
            log.error("保存文件出错srcFileStream不能空");
            return null;
        }

        if(StringUtils.isBlank(toFilePath)){
            log.error("保存文件出错toFilePath不能空");
            return null;
        }

        FileOutputStream toFileStream = null;
        try{
            File toFile = getOrCreateFile(toFilePath);
            toFileStream = new FileOutputStream(toFile,isAppend);
            byte[] buffer = new byte[1024];
            int byteCount;
            while ((byteCount = srcFileStream.read(buffer)) != -1) {
                toFileStream.write(buffer, 0, byteCount);
            }
            toFileStream.flush();

            return getFileInfo(toFile);
        }catch (Throwable ex){
            log.error("保存文件出错。toFilePath[%s]",toFilePath,ex);
        }finally {
            try{
                srcFileStream.close();
                if(toFileStream != null){
                    toFileStream.close();
                }
            }catch (Throwable ex){

            }

        }
        return null;
    }

    private static File getOrCreateFile(String filePath) throws IOException {
        File file = new File(filePath);
        boolean toFiledirExist = file.getParentFile().exists();
        boolean toFileExists = file.exists();

        if(!toFiledirExist){
            if(!file.getParentFile().mkdirs()){
                throw new RuntimeException("创建文件目录失败"+file.getParentFile().getPath());
            }

            EnumSet<PosixFilePermission> perms = EnumSet.of(
                    PosixFilePermission.OWNER_READ,
                    PosixFilePermission.OWNER_WRITE,
                    PosixFilePermission.OWNER_EXECUTE,
                    PosixFilePermission.GROUP_READ,
                    PosixFilePermission.GROUP_EXECUTE,
                    PosixFilePermission.OTHERS_READ,
                    PosixFilePermission.OTHERS_EXECUTE
            );
            Path path = Paths.get(file.getParentFile().getPath());
            Files.setPosixFilePermissions(path, perms);
        }

        if(!toFileExists){
            if(!file.createNewFile()){
                throw new RuntimeException("创建文件失败"+file.getPath());
            }
        }

        return file;
    }

    /**
     * 删除文件
     * @param file
     * @return
     */
    public static boolean delete(File file){
        if(file == null){
            return false;
        }

        boolean isSuccess = false;
        try{
            if(!file.exists()){
                return false;
            }

            if (file.isFile()) {
                isSuccess = file.delete();
                log.info("删除文件[{}].[{}]",isSuccess ? "成功":"失败",file.getPath());
                return isSuccess;
            }

            File[] fs = file.listFiles();
            for (File f : fs) {
                delete(f);
            }

            isSuccess = file.delete();
            log.info("删除目录[{}].[{}]",isSuccess ? "成功":"失败",file.getPath());

            return isSuccess;

        }catch (Throwable ex){
            log.error("删除文件或目录出错。filePath[%s]",file.getPath(),ex);
        }

        return false;
    }

    /**
     * 删除文件
     * @param filePath
     * @return
     */
    public static boolean delete(String filePath){
        if(StringUtils.isBlank(filePath)){
            log.error("删除文件出错。fromFilePath不能空");
            return false;
        }

        File file = new File(filePath);
        return delete(file);
    }

    /**
     * 文件重命名
     * @param filePath
     * @param newName
     * @return
     */
    public static FileInfo rename(String filePath,String newName){
        if(StringUtils.isBlank(filePath)){
            log.error("filePath不能为空");
            return null;
        }

        if(StringUtils.isBlank(filePath)){
            log.error("newName不能为空");
            return null;
        }

        FileInfo fileInfo = getFileInfo(filePath);
        if(fileInfo == null){
            log.warn("获取文件信息失败。filtPath[%s]",filePath);
            return null;
        }

        try{
            File newFile = new File(fileInfo.getFile().getParentFile(), newName);
            boolean isSuccess = fileInfo.getFile().renameTo(newFile);
            if(isSuccess){
                return getFileInfo(newFile);
            }

            throw new RuntimeException("重命名文件出错");

        }catch (Throwable ex){
            log.error("重命名文件出错。filtPath[%s],newName[%s]",filePath,newName,ex);
        }

        return null;
    }

    /**
     * 文件是否存在
     * @param filePath
     * @return
     */
    public static boolean fileExist(String filePath){
        if(StringUtils.isBlank(filePath)){
            log.error("文件检查出错fromFilePath不能空");
            return false;
        }

        File file = new File(filePath);
        return file.exists();
    }

    /**
     * 获取文件信息
     * @param filePath
     * @return
     */
    public static FileInfo getFileInfo(String filePath){
        if(StringUtils.isBlank(filePath)){
            log.error("filePath不能为空");
            return null;
        }

        File file = new File(filePath);
        return getFileInfo(file);
    }

    /**
     * 
     * @param filePath
     * @return
     */
    public static String getFileSha1(String filePath){
        FileInfo fileInfo = getFileInfo(filePath);
        if(fileInfo == null){
            return null;
        }

        return getFileSha1(fileInfo.getFile());
    }

    /**
     * 
     * @param file
     * @return
     */
    public static String getFileSha1(File file) {
        if(file == null || !file.exists() || !file.isFile()){
            return null;
        }

        FileInputStream in = null;
        try {
            in = new FileInputStream(file);
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] buffer = new byte[10485760]; // 1024*1024*10

            int len;
            while ((len = in.read(buffer)) > 0) {
                digest.update(buffer, 0, len);
            }
            String sha1 = new BigInteger(1, digest.digest()).toString(16).toUpperCase();

            int length = 40 - sha1.length();
            if (length > 0) {
                for (int i = 0; i < length; i++) {
                    sha1 = "0" + sha1;
                }
            }
            return sha1;
        } catch (Exception e) {
            log.error("getFileSha1.", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (Exception e) {
                    log.error("getFileSha1..", e);
                }
            }
        }

        return null;
    }

    /**
     * 
     * @param fileDir
     * @param filter
     * @return
     */
    public static List<FileInfo> getFileInfos(String fileDir,String filter){
        List<FileInfo> fileInfos = getFileInfos(fileDir);
        List<FileInfo> filterFileInfos = new ArrayList<>();

        for(FileInfo fileInfo:fileInfos){
            if (!Pattern.matches(filter, fileInfo.getFileFullName())) {
                continue;
            }
            filterFileInfos.add(fileInfo);
        }

        return filterFileInfos;
    }

    /**
     * 
     * @param fileDir
     * @return
     */
    public static List<FileInfo> getFileInfos(String fileDir){
        FileInfo fileInfo = getFileInfo(fileDir);

        List<FileInfo> fileInfos = new ArrayList<>();
        if(fileInfo == null || !fileInfo.isDirectory()){
            return fileInfos;
        }

        for(File file : fileInfo.getFile().listFiles()){
            fileInfos.add(getFileInfo(file));
        }

        return fileInfos;
    }

    /**
     * 获取文件信息
     * @param filePath
     * @param ifNoExistCreate
     * @return
     */
    public static FileInfo getFileInfo(String filePath,boolean ifNoExistCreate){
        if(!ifNoExistCreate){
            return getFileInfo(filePath);
        }

        FileInfo fileInfo = getFileInfo(filePath);
        if(fileInfo != null){
            return fileInfo;
        }

        try {
            File file = getOrCreateFile(filePath);
            long fileSize = 0;

            String[] names = file.getName().split("\\.");
            String fileName = names[0];
            String fileExtension = names.length > 1 ? names[1] : null;

            fileInfo = new FileInfo();
            fileInfo.setFile(file);
            fileInfo.setFileName(fileName);
            fileInfo.setFileExtension(fileExtension);
            fileInfo.setFileSize(fileSize);

        }catch (Throwable ex){
            log.error("创建文件失败",ex);
        }

        return null;
    }

    private static FileInfo getFileInfo(File file){
        if(file == null){
            log.error("file不能为空");
            return null;
        }

        if(!file.exists()){
            log.warn("文件不存在。filePath:{}",file.getPath());
            return null;
        }

        try{
            FileInfo fileInfo = new FileInfo();
            fileInfo.setDirectory(file.isDirectory());
            fileInfo.setFile(file);
            fileInfo.setFileFullName(file.getName());

            if(!file.isDirectory()){
                String[] names = file.getName().split("\\.");
                String fileName = names[0];
                String fileExtension = names.length > 1 ? names[1] : null;

                fileInfo.setFileName(fileName);
                fileInfo.setFileExtension(fileExtension);
                fileInfo.setFileSize(file.length());
                fileInfo.setFileType(FileTypeUtil.getType(file));
            }

            return fileInfo;

        }catch (Throwable ex){
            log.error("获取文件信息出错",ex);
        }

        return null;
    }

    /**
     * 获取文件字符内容
     * @param filePath 文件路径
     * @return
     */
    public static String getFileContent(String filePath) {
        FileInfo fileInfo = getFileInfo(filePath);
        if(fileInfo == null){
            return null;
        }

        File file = fileInfo.getFile();

        byte[] buffer = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            int length = fis.available();
            buffer = new byte[length];
            fis.read(buffer);
        }catch (Exception ex){
            log.error("获取文件字符内容异常。filtPath[%s]",filePath,ex);
        }finally {
            if(fis != null){
                try {
                    fis.close();
                } catch (IOException e) {

                }
            }
        }

        return buffer != null ? new String(buffer, StandardCharsets.UTF_8) : null;
    }

    public static String getUploadPath() {
        String uploadPath = ConfigUtil.getValue(ConfigKey.系统_文件_本地_SAVEPATH,"");

        if(StringUtils.isNotBlank(uploadPath)){
            return uploadPath.concat(File.separator);
        }

        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath:/static/");
            for (Resource resource : resources) {
                uploadPath = resource.getURI().getPath();
                break;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return uploadPath;
        //System.getProperty("user.dir")).concat(File.separator)
    }

    /**
     * 根据 MultipartFile 的 ContentType 获取文件扩展名
     * @param contentType
     * @return 文件扩展名，如果没有匹配的扩展名返回空字符串
     */
    public static String getExtNameByCType(String contentType) {
        String ctype = contentType.toLowerCase();
        return contentTypeToExtension.getOrDefault(ctype,"none");
    }

    public static String getContentType(String extensionName) {
        Map.Entry<String,String> findType = contentTypeToExtension
                .entrySet().stream()
                .filter(e -> e.getValue().equals(extensionName))
                .collect(Collectors.toSet())
                .stream().findAny().get();

        return findType != null ? findType.getKey() : null;
    }

    @Data
    public static class FileInfo {
        private String fileName;
        private String fileExtension;
        private File file;
        private long fileSize;
        private String fileFullName;
        private String fileType;
        private boolean isFile;
        private boolean directory;
    }
}
