package com.ruoyi.pmrsch.file;

import cn.hutool.core.io.FileTypeUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.ruoyi.pmrsch.common.ConfigKey;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ConfigUtil;
import com.ruoyi.pmrsch.file.entity.FileBType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public class AliyunOssUtil {
    /**
     * 不区分环境的公开资源的路由前缀
     */
    public static final String STATIC_URL = "https://sili-static.oss-cn-shenzhen.aliyuncs.com/";

    private static OSSClient getOssClient(FileBType fileBType) {
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);
        DefaultCredentialProvider credProvider = new DefaultCredentialProvider(spaceInfo.getKey(), spaceInfo.getSecret());

        return new OSSClient("https://".concat(spaceInfo.getEndpoint()), credProvider, null);
    }

    /**
     * 上传文件到公开空间
     * @param fileId 文件key
     * @param inputStream 文件输入流
     * @param contentType 文件类型
     */
    public static boolean putFile(FileBType fileBType, String fileId, InputStream inputStream, String contentType) {
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);

        OSSClient ossClient = getOssClient(fileBType);
        return putAliYunFile(ossClient, spaceInfo.getBucket(), fileId, inputStream, contentType);
    }

    /**
     * 获取公开空间的文件
     * @param fileId key
     * @return 字节数组
     * @throws IOException 异常
     */
    public static byte[] getFile(FileBType fileBType, String fileId){

        SpaceInfo spaceInfo = getSpaceInfo(fileBType);
        OSSClient ossClient = getOssClient(fileBType);

        return getFile(ossClient, spaceInfo.getBucket(), fileId);
    }

    public static long getFileSize(FileBType fileBType, String fileId){
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);
        OSSClient ossClient = getOssClient(fileBType);
        return getFileSize(ossClient,spaceInfo.getBucket(),fileId);
    }

    private static long getFileSize(OSSClient ossClient,String bucketName,String fileId){
        ObjectMetadata objectMetadata = ossClient.getObjectMetadata(bucketName, fileId);
        return objectMetadata.getContentLength();
    }

    public static String getFileMd5(FileBType fileBType, String fileId){
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);
        OSSClient ossClient = getOssClient(fileBType);
        return getFileMd5(ossClient,spaceInfo.getBucket(),fileId);
    }
    private static String getFileMd5(OSSClient ossClient,String bucketName,String fileId){
        ObjectMetadata objectMetadata = ossClient.getObjectMetadata(bucketName, fileId);
        return objectMetadata.getContentMD5();
    }

    public static String getFileType(FileBType fileBType, String fileId){
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);
        OSSClient ossClient = getOssClient(fileBType);
        return getFileType(ossClient,spaceInfo.getBucket(),fileId);
    }

    private static String getFileType(OSSClient ossClient,String bucketName,String fileId){
        OSSObject object = ossClient.getObject(bucketName, fileId);
        return FileTypeUtil.getType(object.getObjectContent());
    }


    /**
     * 获取公开空间的文件全路径
     * @param fileId key
     * @return 文件全路径 https://aliyun.com/xx
     */
    public static String getFileUrl(FileBType fileBType, String fileId) {
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);
        return getFileUrl(spaceInfo.getCaname(), spaceInfo.getBucket(),spaceInfo.getEndpoint(), fileId);
    }

    private static SpaceInfo getSpaceInfo(FileBType fileBType) {

        String caname = ConfigUtil.getRequiredString(ConfigKey.系统_文件_阿里OSS_CNAME),
                bucket = ConfigUtil.getRequiredString(ConfigKey.系统_文件_阿里OSS_BUCKET),
                secret = ConfigUtil.getRequiredString(ConfigKey.系统_文件_阿里OSS_SECRET),
                key = ConfigUtil.getRequiredString(ConfigKey.系统_文件_阿里OSS_KEY),
                endpoint = ConfigUtil.getRequiredString(ConfigKey.系统_文件_阿里OSS_ENDPOINT);

        return new SpaceInfo(bucket,caname,secret,key,endpoint);
    }

    /**
     * 删除文件
     * @param fileId key
     */
    public static void deleteFile(FileBType fileBType, String fileId) {
        OSSClient ossClient = getOssClient(fileBType);
        SpaceInfo spaceInfo = getSpaceInfo(fileBType);

        deleteFile(ossClient,spaceInfo.getBucket(), fileId);
    }



    private static byte[] getFile(OSSClient ossClient, String bucketName, String fileId) {
        OSSObject object = null;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            object = ossClient.getObject(bucketName, fileId);
            byte[] b = new byte[ 8 * 1024];
            int n;
            while ((n = object.getObjectContent().read(b)) != -1) {
                bos.write(b, 0, n);
            }
            return bos.toByteArray();
        } catch (Exception e) {
            log.error("从阿里云取得文件失败![{}] : {},\n {}", fileId, e.getMessage(), ExceptionUtils.getStackTrace(e));
            throw new ApiException(e.getMessage());
        }finally {
            try {
                bos.close();
            } catch (IOException e) {
                log.error("关闭文件流异常",e);
            }
            if(object != null) {
                try {
                    object.close();
                } catch (IOException e) {
                    log.error("关闭文件流异常",e);
                }
            }
            ossClient.shutdown();
        }
    }

    private static String getFileUrl(String cName, String bucketName,String endpoint, String fileId) {
        if (StringUtils.isEmpty(fileId)) {
            return "";
        }
        StringBuilder builder = new StringBuilder();
        builder.append("https://");
        if (StringUtils.isEmpty(cName)) {
            builder.append(bucketName);
            builder.append(".");
            builder.append(endpoint.replace("-internal", ""));
        } else {
            builder.append(cName);
        }
        builder.append("/");
        builder.append(fileId);
        return builder.toString();
    }

    /**
     * 上传文件
     * @param ossClient
     * @param bucketName
     * @param fileId
     * @param inputStream
     * @param contentType
     */
    private static boolean putAliYunFile(OSSClient ossClient, String bucketName, String fileId, InputStream inputStream, String contentType){
        try {

            // 创建上传Object的Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(contentType);
            // 上传文件
            ossClient.putObject(bucketName, fileId, inputStream, objectMetadata);
            return true;

        } catch (Exception e) {
            log.error("文件上传至阿里云异常!", e);
        } finally {
            ossClient.shutdown();
        }

        return false;
    }

    private static boolean deleteFile(OSSClient ossClient, String bucketName, String fileId){
        try {

            ossClient.deleteObject(bucketName, fileId);
            return true;

        } catch (Exception e) {
            log.error("文件删除异常! ", e);
        } finally {
            ossClient.shutdown();
        }

        return false;
    }

    @Data
    @AllArgsConstructor
    private static class SpaceInfo {
        /**
         * 空间
         */
        private String bucket;
        /**
         * 域名
         */
        private String caname;
        private String secret;
        private String key;
        /**
         * 访问地址
         */
        private String endpoint;
    }
}
