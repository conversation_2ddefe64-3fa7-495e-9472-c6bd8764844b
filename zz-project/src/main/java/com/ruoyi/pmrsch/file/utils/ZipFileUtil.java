package com.ruoyi.pmrsch.file.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 打包工具类
 */
@Slf4j
public class ZipFileUtil {
    //文件后缀名
    public static final String ZIP = "zip";

    public static final String RAR = "rar";

    /**
     * 文件解压
     * @param zipFilePath 压缩包文件全路径
     * @param toPath 解压目录
     * @return
     */
    public static boolean unzip(String zipFilePath, String toPath) {
        long start = System.currentTimeMillis();

        FileUtil.FileInfo fileInfo = FileUtil.getFileInfo(zipFilePath);
        if(fileInfo == null){
            log.error("文件解压失败，压缩包文件不存在。zipFilePath[{}],toFilePath[{}]",zipFilePath,toPath);
            return false;
        }

        // 开始解压
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(fileInfo.getFile(), Charset.forName("GBK"));
            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
                String tpName = entry.getName().replace("\\", File.separator);
                // 如果是文件夹，就创建个文件夹
                if(!entry.isDirectory()){
                    String toFilePath = toPath.concat(File.separator).concat(tpName);
                    FileUtil.save(zipFile.getInputStream(entry),toFilePath,false);

                    log.info("解压文件[{}]",tpName);
                }
            }
            long end = System.currentTimeMillis();

            log.info("解压完成，耗时：{}ms。zipFilePath[{}],toFilePath[{}]",(end - start),zipFilePath,toPath);
            return true;
        } catch (Exception e) {
            log.error("文件解压异常。zipFilePath[{}],toFilePath[{}]",zipFilePath,toPath,e);
        } finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                } catch (Exception e) {
                    log.error("文件流关闭异常。zipFilePath[{}],toFilePath[{}]",zipFilePath,toPath,e);
                }
            }
        }

        return false;
    }


    /** 解压zip文件
     * @param in 需要解压的文件
     * @param toPath 解压后的文件路径
     */
    public static void unzip(InputStream in, String toPath) throws IOException {
        ZipInputStream zIn = null;
        try {
            //构建解压输入流,设置编码，防止有中文报错
            zIn = new ZipInputStream(in, Charset.forName("GBK"));
            ZipEntry entry;
            File file;
            while ((entry = zIn.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    file = new File(toPath, entry.getName());
                    if (!file.exists()) {
                        //创建此文件的上级目录
                        new File(file.getParent()).mkdirs();
                    }
                    OutputStream out = new FileOutputStream(file);
                    BufferedOutputStream bos = new BufferedOutputStream(out);
                    int len = -1;
                    byte[] buf = new byte[1024];
                    while ((len = zIn.read(buf)) != -1) {
                        bos.write(buf, 0, len);
                    }
                    // 关流顺序，先打开的后关闭
                    bos.close();
                    out.close();
                }
            }
        }finally {
            if(zIn!=null){
                try {
                    zIn.close();
                } catch (IOException e) {
                    log.error("关闭文件流出错",e);
                }
            }
        }
    }

    /**
     * 创建ZIP文件
     * @param filePath 文件或文件夹路径
     * @param toFilePath 生成的zip文件存在路径（包括文件名）
     * @param delOriginfile  是否删除原文件:true删除、false不删除
     */
    public static boolean zip(List<String> filePath, String toFilePath,Boolean delOriginfile) {
        boolean success;

        try {
            FileOutputStream outputStream = new FileOutputStream(toFilePath);
            success = writeToOutStream(filePath,outputStream,delOriginfile);

        }catch (Exception ex){
            success = false;
            log.error("压缩zip异常",ex);
        }

        return success;
    }

    public static byte[] zip(List<String> filePath,Boolean delOriginfile) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        writeToOutStream(filePath,outputStream,delOriginfile);
        return outputStream.toByteArray();
    }

    /**
     * 压缩文件
     * @param filePath 等待压缩文件。格式：文件路径,新文件名
     * @param outputStream
     * @param delOriginfile
     * @return
     */
    private static boolean writeToOutStream(List<String> filePath,OutputStream outputStream,Boolean delOriginfile) {
        ZipOutputStream zos = null;

        boolean success = true;
        try {
            zos = new ZipOutputStream(outputStream);

            for(String f:filePath){
                String[] _fp = f.split(",");
                String fp = _fp[0];
                String newFileName = _fp.length > 1 ? _fp[1].concat(".").concat(FileUtil.getExtensionType(fp)) : null;

                File source = new File(fp);
                if (!source.exists()) {
                    continue;
                }
                write(source,newFileName,"",zos,delOriginfile);
            }

        } catch (Exception e) {
            success = false;
            log.error("压缩zip异常",e);
        } finally {
            try {
                if (zos != null) {
                    zos.close();
                }
            } catch (IOException e) {
                log.error("压缩zip异常",e);
            }
        }

        return success;
    }

    public static void downLoadZipFile(String zipName, List<File> fileList, HttpServletResponse response){
        //设置压缩包的名字
        //解决不同浏览器压缩包名字含有中文时乱码的问题
        response.setContentType("APPLICATION/OCTET-STREAM");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipName);
        //设置压缩流：直接写入response，实现边压缩边下载
        ZipOutputStream zipos = null;
        try {
            zipos = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()));
            zipos.setMethod(ZipOutputStream.DEFLATED);//设置压缩方法
        } catch (Exception e) {
            e.printStackTrace();
        }
        DataOutputStream os = null;
        //循环将文件写入压缩流
        for (int i = 0; i < fileList.size(); i++) {
            File file = fileList.get(i);//要下载文件的路径
            try {
                //添加ZipEntry，并ZipEntry中写入文件流
                //这里，加上i是防止要下载的文件有重名的导致下载失败
                zipos.putNextEntry(new ZipEntry(file.getName()));
                os = new DataOutputStream(zipos);
                InputStream is = new FileInputStream(file);
                byte[] b = new byte[100];
                int length = 0;
                while ((length = is.read(b)) != -1) {
                    os.write(b, 0, length);
                }
                is.close();
                zipos.closeEntry();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //关闭流
        try {
            assert os != null;
            os.flush();
            os.close();
            zipos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void write(File file,String newFileName, String parentPath, ZipOutputStream zos, Boolean delOriginfile) throws IOException {
        if(!file.exists()){
            log.warn("创建zip文件失败，文件不存在。filepath[{}]",file.getPath());
            return;
        }

        if(file.isDirectory()){
            //处理文件夹
            parentPath += file.getName() + File.separator;
            File [] files = file.listFiles();

            if(files.length > 0) {
                for(File f:files){
                    write(f, newFileName,parentPath, zos,delOriginfile);
                }
            } else {
                zos.putNextEntry(new ZipEntry(parentPath));
            }

        }else{

            FileInputStream fis = null;
            try {

                fis = new FileInputStream(file);
                ZipEntry ze = new ZipEntry(parentPath + (StringUtils.isNotBlank(newFileName) ? newFileName : file.getName()));
                zos.putNextEntry(ze);

                byte [] content=new byte[1024];
                int len;
                while((len = fis.read(content)) != -1){
                    zos.write(content,0,len);
                    zos.flush();
                }

            } finally{
                try {
                    if(fis!=null){
                        fis.close();
                    }
                    if(delOriginfile){
                        FileUtil.delete(file);
                    }
                }catch (Exception e) {
                    log.error("关闭ZIP文件流异常",e);
                }
            }
        }
    }
}
