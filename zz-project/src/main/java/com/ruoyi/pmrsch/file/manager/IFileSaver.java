package com.ruoyi.pmrsch.file.manager;


import com.ruoyi.pmrsch.file.entity.FileBType;
import com.ruoyi.pmrsch.file.entity.SaveFileResult;

import java.io.InputStream;

public interface IFileSaver {

    /**
     * 保存文件
     *
     * @param fileBType 文件业务类型 {@link FileBType}
     * @param fileStream 要保存的文件
     * @param fileName 文件名称
     * @param contentType 文件类型
     * @return SaveFileResult {@link SaveFileResult}
     */
    SaveFileResult saveFile(FileBType fileBType, InputStream fileStream, String fileName, String fileNameExt, String contentType);

    /**
     * 删除文件
     *
     * @param fullFilePath 文件路径
     * @param fileBType 文件业务类型 {@link FileBType}
     */
    void deleteFile(String fullFilePath, FileBType fileBType);

    /**
     * 获取文件字节
     *
     * @param fullFilePath 文件路径
     * @param fileBType 文件业务类型 {@link FileBType}
     */
    byte[] getFileBytes(String fullFilePath, FileBType fileBType);
}
