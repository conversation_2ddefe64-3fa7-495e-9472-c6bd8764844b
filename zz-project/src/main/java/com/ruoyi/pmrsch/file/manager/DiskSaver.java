package com.ruoyi.pmrsch.file.manager;

import com.ruoyi.pmrsch.file.entity.FileBType;
import com.ruoyi.pmrsch.file.entity.SaveFileResult;
import com.ruoyi.pmrsch.file.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> <EMAIL>
 * @date : 2022-12-07 17:34
 **/
@Service
@Slf4j
public class DiskSaver implements IFileSaver {

    @Override
    public SaveFileResult saveFile(FileBType fileBType, InputStream fileStream, String fileName, String fileNameExt, String contentType){
        try {
            if(!fileName.contains(".")) {
                String extName = ".".concat(fileNameExt);
                fileName = fileName.concat(extName);
            }

            String filePath = fileBType.getFullPath().concat(fileName);
            String fileUrl = fileBType.getUrlPath().concat(fileName);

            byte[] fileBytes = IOUtils.toByteArray(fileStream);
            FileUtil.FileInfo file = FileUtil.save(fileBytes,filePath);
            long fileSize = file.getFileSize();
            return fileSize > 0 ? new SaveFileResult(filePath, fileSize, fileUrl, FileUtil.getFileMD5(fileBytes), file.getFileType()) : null;

        } catch (Exception ex) {
            log.error("保存文件出错。fileBType={},fileName={},fileNameExt={},contentType={}",fileBType,fileName,fileNameExt,contentType,ex);
        }
        return null;
    }

    @Override
    public void deleteFile(String fullFilePath, FileBType fileBType) {
        if(StringUtils.isBlank(fullFilePath)){
            throw new RuntimeException("deleteFile失败，找不到文件路径");
        }
        File file = new File(fullFilePath);
        FileUtil.delete(file);
    }

    @Override
    public byte[] getFileBytes(String fullFilePath, FileBType fileBType) {
        try {
            if(StringUtils.isBlank(fullFilePath)){
                throw new RuntimeException("getFileBytes失败，找不到文件路径");
            }
            File file = new File(fullFilePath);
            return FileUtils.readFileToByteArray(file);
        } catch (Exception e) {
            log.error("getFileBytes失败。fullFilePath={},fileBType={}",fullFilePath,fileBType,e);
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws IOException {
    }
}
