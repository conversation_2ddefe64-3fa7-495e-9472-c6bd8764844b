package com.ruoyi.pmrsch.file.api;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.pmrsch.common.entity.ApiResponse;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.JsonUtil;
import com.ruoyi.pmrsch.file.UploadFilesUtil;
import com.ruoyi.pmrsch.file.dao.model.UploadFiles;
import com.ruoyi.pmrsch.file.entity.FileBType;
import com.ruoyi.pmrsch.file.entity.UploadFileRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;

@Controller
@Slf4j
@RequestMapping(value = "/pmrsch/common")
public class FileUploadController extends BaseController{

	@Autowired
	private UploadFilesUtil filesService;

	@PostMapping("file/upload")
	@ResponseBody
	public AjaxResult uploadFile(@RequestParam(value = "file",required = false) MultipartFile file,
						   @RequestParam(value = "fileBType",required = false) String fileBType){

		FileBType fileBTypeEnum = StringUtils.isBlank(fileBType) ? FileBType.临时本地 : FileBType.getByValue(Integer.valueOf(fileBType));
		UploadFiles files = filesService.saveFile(fileBTypeEnum,file);
		return AjaxResult.success(UploadFileRsp.fromFiles(files));
	}

	@GetMapping("file/{fileId}")
	@ResponseBody
	public AjaxResult getFileInfo(@PathVariable String fileId){
		UploadFiles files = filesService.getByFileId(fileId);
		return AjaxResult.success(UploadFileRsp.fromFiles(files));
	}

	@PostMapping("file/upload/base64")
	public void uploadBase64File(HttpServletRequest request, Model model) {
		try {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			int len;
			while ((len = request.getInputStream().read(buffer)) != -1) {
				baos.write(buffer, 0, len);
			}
			String reqJson = new String(baos.toByteArray());
			Map requestBody = JsonUtil.toBean(reqJson, Map.class);

			// Get parameters from request body
			String base64Data = requestBody.getOrDefault("base64Data","").toString();
			String fileName = requestBody.getOrDefault("fileName","").toString();
			String fileBType = requestBody.getOrDefault("fileBType","").toString();

			// Validate required parameters
			if (StringUtils.isBlank(base64Data) || StringUtils.isBlank(fileName)) {
				throw new ApiException("缺少必要参数");
			}

			// Decode base64 data
			byte[] fileData = Base64.getDecoder().decode(base64Data);

			// Detect content type from file data and filename
			String contentType = detectContentType(fileData, fileName);
			if (StringUtils.isBlank(contentType)) {
				contentType = "application/octet-stream"; // Default content type
			}

			// Create input stream from byte array
			InputStream inputStream = new ByteArrayInputStream(fileData);

			// Get file type enum
			FileBType fileBTypeEnum = StringUtils.isBlank(fileBType) ? FileBType.临时本地 : FileBType.getByValue(Integer.valueOf(fileBType));

			// Save file using existing service
			UploadFiles files = filesService.saveFile(fileBTypeEnum, fileName, null, null, inputStream, contentType);

			ApiResponse.success(UploadFileRsp.fromFiles(files), model);
		} catch (Exception e) {
			log.error("Failed to upload base64 file", e);
			throw new ApiException("文件上传失败: " + e.getMessage());
		}
	}

	/**
	 * 从文件数据和文件名中检测文件类型
	 * @param fileData 文件数据
	 * @param fileName 文件名
	 * @return 文件类型
	 */
	private String detectContentType(byte[] fileData, String fileName) {
		// 1. 从文件头检测文件类型
		if (fileData.length >= 4) {
			// 检查常见文件类型的魔数
			if (fileData[0] == (byte)0xFF && fileData[1] == (byte)0xD8) {
				return "image/jpeg";
			} else if (fileData[0] == (byte)0x89 && fileData[1] == (byte)0x50) {
				return "image/png";
			} else if (fileData[0] == (byte)0x47 && fileData[1] == (byte)0x49 && fileData[2] == (byte)0x46) {
				return "image/gif";
			} else if (fileData[0] == (byte)0x25 && fileData[1] == (byte)0x50 && fileData[2] == (byte)0x44 && fileData[3] == (byte)0x46) {
				return "application/pdf";
			} else if (fileData[0] == (byte)0x50 && fileData[1] == (byte)0x4B && fileData[2] == (byte)0x03 && fileData[3] == (byte)0x04) {
				return "application/zip";
			}
		}

		// 2. 从文件名扩展名检测文件类型
		if (StringUtils.isNotBlank(fileName)) {
			String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
			switch (extension) {
				case "jpg":
				case "jpeg":
					return "image/jpeg";
				case "png":
					return "image/png";
				case "gif":
					return "image/gif";
				case "pdf":
					return "application/pdf";
				case "doc":
					return "application/msword";
				case "docx":
					return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
				case "xls":
					return "application/vnd.ms-excel";
				case "xlsx":
					return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				case "zip":
					return "application/zip";
				case "rar":
					return "application/x-rar-compressed";
				case "txt":
					return "text/plain";
				case "csv":
					return "text/csv";
				case "json":
					return "application/json";
				case "xml":
					return "application/xml";
				case "html":
				case "htm":
					return "text/html";
				case "css":
					return "text/css";
				case "js":
					return "application/javascript";
			}
		}

		return null;
	}
	
}