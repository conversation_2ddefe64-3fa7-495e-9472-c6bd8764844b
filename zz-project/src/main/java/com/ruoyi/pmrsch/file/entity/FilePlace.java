package com.ruoyi.pmrsch.file.entity;


import com.ruoyi.pmrsch.common.ConfigKey;
import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;
import com.ruoyi.pmrsch.common.tools.ConfigUtil;
import com.ruoyi.pmrsch.file.utils.FileUtil;

/**
 * 文件保存路径类型枚举
 */
public enum FilePlace implements EnumIntegerInterface {

    OSS(1,""),
    本地(2, FileUtil.getUploadPath()),
    ZIP(3, FileUtil.getUploadPath());

    private Integer value;
    private String path;

    FilePlace(Integer value,String path) {
        this.value = value;
        this.path = path;
    }

    public static FilePlace getByValue(Integer value) {
        return EnumIntegerInterface.getByValue(value, FilePlace.class);
    }

    public String getUrlPath(){
        switch (this){
            case OSS:
                return ConfigUtil.getRequiredString(ConfigKey.系统_文件_阿里OSS_URL);
            case 本地:
                return ConfigUtil.getRequiredString(ConfigKey.系统_文件_本地_URL);
            case ZIP:
                return ConfigUtil.getRequiredString(ConfigKey.系统_文件_本地_URL);
            default:
                return null;
        }
    }

    public String getPath(){
        return this.path;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
