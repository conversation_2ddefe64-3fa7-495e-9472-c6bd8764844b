package com.ruoyi.pmrsch.file.entity;


import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;
import com.ruoyi.pmrsch.common.tools.TimeUtil;

import java.io.File;
import java.io.Serializable;

/**
 * 文件来源枚举
 */
public enum FileBType implements EnumIntegerInterface, Serializable {

    车头正面(0, "car-front", FilePlace.本地),
    车架号(1, "vin", FilePlace.本地),
    上板完成(2, "load-complete", FilePlace.本地),
    卸车完成(3, "unload-complete", FilePlace.本地),
    人车合影(4, "car-people", FilePlace.本地),
    上板过程(5, "loading", FilePlace.本地),
    拖车终点(6, "towing-destination", FilePlace.本地),
    工单照(7, "work-order", FilePlace.本地),
    报备照(8, "report", FilePlace.本地),
    双证照(9, "documents", FilePlace.本地),
    到达视频(10, "arrival-video", FilePlace.本地),
    结束视频(11, "completion-video", FilePlace.本地),
    空驶地点(12, "empty-drive", FilePlace.本地),
    牵引过程(13, "towing-process", FilePlace.本地),
    牵引到地面(14, "towing-to-ground", FilePlace.本地),
    搭电正负极连接(15, "jump-start", FilePlace.本地),
    搭电过程(16, "jump-process", FilePlace.本地),
    搭电成功后仪表盘照片(17, "jump-success", FilePlace.本地),
    轮胎充气(18, "inflate", FilePlace.本地),
    轮胎亏气(19, "tire-deflated", FilePlace.本地),
    轮胎充气过程(20, "inflate-process", FilePlace.本地),
    轮胎充气完成(21, "inflate-complete", FilePlace.本地),
    车辆轮胎异常(22, "tire-exception", FilePlace.本地),
    换胎过程(23, "tire-replacement", FilePlace.本地),
    轮胎更换完成(24, "tire-replacement-complete", FilePlace.本地),
    送油过程(25, "fuel-delivery", FilePlace.本地),
    车辆启动情况(26, "engine-start", FilePlace.本地),
    送防冻液加水过程照片(27, "antifreeze-process", FilePlace.本地),
    防冻液水加注完成照片(28, "antifreeze-complete", FilePlace.本地),
    困境脱困过程(29, "extrication-process", FilePlace.本地),
    困境脱困成功(30, "extrication-success", FilePlace.本地),
    吊车脱困过程(31, "towing-process", FilePlace.本地),
    吊车脱困成功(32, "towing-success", FilePlace.本地),
    导航截图(33, "nav-snap", FilePlace.本地),
    人车路牌(34, "tech-vech-road", FilePlace.本地),
    其它(99, "other", FilePlace.本地),
    临时OSS(-1, "tmp", FilePlace.OSS),
    临时本地(-2, "tmp", FilePlace.本地);


    private final int value;
    private final FilePlace filePlace;
    private String tag;

    FileBType(int value, String tag, FilePlace savePlace) {
        this.value = value;
        this.tag = tag;
        this.filePlace = savePlace;
    }

    public static FileBType getByValue(int value) {
        return EnumIntegerInterface.getByValue(value, FileBType.class);
    }
    @Override
    public int getValue() {
        return this.value;
    }

    public FilePlace getFilePlace() {
        return filePlace;
    }

    public String getTag() {
        return tag;
    }

    public String getUrlPath() {
        return filePlace.getUrlPath()
                .concat(File.separator)
                .concat(tag)
                .concat(File.separator)
                .concat(TimeUtil.getCurrentTimeFormat(TimeUtil.YYYYMMDD))
                .concat(File.separator);
    }

    public String getFullPath() {
        return filePlace.getPath()
                .concat(tag)
                .concat(File.separator)
                .concat(TimeUtil.getCurrentTimeFormat(TimeUtil.YYYYMMDD))
                .concat(File.separator);
    }
}
