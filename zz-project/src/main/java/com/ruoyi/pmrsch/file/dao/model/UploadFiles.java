package com.ruoyi.pmrsch.file.dao.model;

import lombok.Data;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 文件表Entity
 * <AUTHOR>
 * @version 2025-01-17
 */
@Data
@Table(name = "tx_upload_files")
public class UploadFiles{
	
	private static final long serialVersionUID = 1L;
	
	@Id
	private String fileId;		// 文件ID
	private Integer fileBType;		// 业务类型
	private String userId;		// 用户ID
	private String description;		// 文件描述
	private String filename;		// 文件名称
	private String extension;		// 文件扩展名
	private Integer createdTime;		// 创建时间
	private Integer updatedTime;		// 最后更新时间
	private Integer fileSize;		// 文件大小
	private String md5;		// 文件md5值
	private String fileSavePlace;		// 文件存储目录路径
	private String fileUrl;		// 文件完整路径
	private String fileType;		// 文件类型
	
}