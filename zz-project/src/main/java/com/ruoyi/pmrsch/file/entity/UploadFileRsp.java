package com.ruoyi.pmrsch.file.entity;/**
 * <AUTHOR>
 * @Date 2025/1/19
 */

import com.ruoyi.pmrsch.file.dao.model.UploadFiles;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 描述
 * @className UploadFileRsp
 * <AUTHOR>
 * @date 2025年01月19日 07:30
 */
@Data
@AllArgsConstructor
public class UploadFileRsp {
    private String fileId;
    private String fileName;
    private String fileUrl;

    public static UploadFileRsp fromFiles(UploadFiles files) {
        return new UploadFileRsp(files.getFileId(),files.getFilename(),files.getFileUrl());
    }
}
