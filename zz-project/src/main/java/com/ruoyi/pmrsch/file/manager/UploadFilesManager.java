package com.ruoyi.pmrsch.file.manager;

import com.ruoyi.pmrsch.file.dao.mapper.UploadFilesMapper;
import com.ruoyi.pmrsch.file.dao.model.UploadFiles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 文件表Service
 * <AUTHOR>
 * @version 2025-01-17
 */
@Service
public class UploadFilesManager {
	@Autowired
	private UploadFilesMapper uploadFilesMapper;

	public UploadFiles get(String fileId) {
		return uploadFilesMapper.selectUploadFilesByFileId(fileId);
	}

	public int insert(UploadFiles file) {
		return uploadFilesMapper.insertUploadFiles(file);
	}

	public int update(UploadFiles file) {
		return uploadFilesMapper.updateUploadFiles(file);
	}

	public int deleteById(String fileId) {
		return uploadFilesMapper.deleteUploadFilesByFileId(fileId);
	}
}