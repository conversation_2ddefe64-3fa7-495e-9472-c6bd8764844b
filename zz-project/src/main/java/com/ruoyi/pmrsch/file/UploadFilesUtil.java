package com.ruoyi.pmrsch.file;

import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.file.entity.FileBType;
import com.ruoyi.pmrsch.file.entity.FilePlace;
import com.ruoyi.pmrsch.file.entity.SaveFileResult;
import com.ruoyi.pmrsch.file.dao.model.UploadFiles;
import com.ruoyi.pmrsch.file.manager.*;
import com.ruoyi.pmrsch.file.utils.FileUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
@Slf4j
@Data
@RequiredArgsConstructor
public class UploadFilesUtil {

    @Autowired
    private final UploadFilesManager filesManager;
    @Autowired
    private final OssSaver aliyunOssPlace;
    @Autowired
    private final DiskSaver localDiskPlace;
    @Autowired
    private final ZipDiskSaver zipDiskSaver;

    private static UploadFilesUtil instance(){
        UploadFilesUtil ins = SpringUtils.getBean(UploadFilesUtil.class);
        return ins;
    }

    public static void checkExists(String photos) {
        checkExists(Arrays.asList(photos));
    }
    public static void checkExists(List<String> photos) {
        if(photos == null){
            return;
        }

        for(String p : photos){
            if(StringUtils.isBlank(UploadFilesUtil.getFileUrl(p))){
                throw new ApiException("找不到文件，请检查fileId是否正确");
            }
        }
    }

    /**
     * 检查文件是否通过验证
     *
     * @param file      待检查文件
     * @param maxSize   文件大小限制(MB) (null:不作限制)
     * @param allowType 允许上传的文件类型  (null:不作限制)
     * @return 验证提示  (null:验证通过)
     */
    public String checkFileByDouble(MultipartFile file, Double maxSize, String[] allowType) {
        if (file.isEmpty()) {
            return "文件为空!";
        }

        if (maxSize != null) {
            long limit = (long) (maxSize * 1024 * 1024L);
            if (file.getSize() > limit) {
                if (maxSize > 1) {
                    return "只能上传" + maxSize + "MB以内的文件";
                } else {
                    return "只能上传" + (int) (maxSize * 1024) + "KB以内的文件";
                }
            }
        }

        if (allowType != null) {
            String ft = FileUtil.getExtensionType(file.getOriginalFilename());
            boolean has = false;
            StringBuilder format = new StringBuilder();
            for (String aType : allowType) {
                format.append(aType).append(",");
                if (!StringUtils.isEmpty(aType) && aType.equals(ft)) {
                    has = true;
                    break;
                }
            }
            if (!has) {
                return "只能上传指定格式的文件：" + format.substring(0, format.length() - 1);
            }
        }

        return null;
    }

    /**
     * 检查文件是否通过验证
     *
     * @param file      待检查文件
     * @param maxSize   文件大小限制(MB) (null:不作限制)
     * @param allowType 允许上传的文件类型  (null:不作限制)
     * @return 验证提示  (null:验证通过)
     */
    public String checkFile(MultipartFile file, Integer maxSize, String[] allowType) {
        return this.checkFileByDouble(file, maxSize == null ? null : maxSize.doubleValue(), allowType);
    }

    /**
     * 保存文件 并更新文件资源表内容 (本方法不对文件做有效性验证)
     *
     * @param file     要保存的文件
     * @param desc     文件业务描述字符
     * @param uploader 上传者userId
     * @return WdFile
     * @throws IOException IOException
     */
    public UploadFiles saveFile(FileBType fileBType, MultipartFile file, String desc, String uploader) throws IOException {
        return this.saveOrUpdate(fileBType, null, file.getOriginalFilename(), desc, uploader, file.getInputStream(), file.getContentType());
    }


    /**
     * 保存文件 (本方法不对文件做有效性验证)
     *
     * @param fileName    要保存的文件名
     * @param desc        文件业务描述字符
     * @param uploader    上传者userId
     * @param inputStream ""
     * @return WdFile
     */
    public UploadFiles saveFile(FileBType fileBType, String fileName, String desc, String uploader, InputStream inputStream, String contentType) {
        return this.saveOrUpdate(fileBType, null, fileName, desc, uploader, inputStream, contentType);
    }

    /**
     * 保存或者更新文件 并更新文件资源表内容 (本方法不对文件做有效性验证)
     *
     * @param fileBType {@link FileBType}
     * @param multipartFile multipartFile
     * @param fileId 存放文件的id
     * @return WdFile {@link UploadFiles}
     */
    public UploadFiles saveOrUpdate(FileBType fileBType, MultipartFile multipartFile, String fileId) {
        try {
            return saveOrUpdate(fileBType,fileId,null,"-1",null,multipartFile.getInputStream(),multipartFile.getContentType());
        } catch (Throwable ex) {
            log.error("保存文件失败。fileBType -> [{}], contentType -> [{}]", fileBType, multipartFile.getContentType(), ex);
        }
        return null;
    }

    /**
     * 保存或者更新文件 并更新文件资源表内容 (本方法不对文件做有效性验证)
     *
     * @param fileBType {@link FileBType}
     * @param fileName    要保存的文件名如a.png
     * @param desc        文件业务描述字符
     * @param uploader    上传者userId
     * @param inputStream 文件流
     * @param fileId      存放文件的id
     * @return WdFile {@link UploadFiles}
     */
    public UploadFiles saveOrUpdate(FileBType fileBType, String fileId, String fileName, String desc, String uploader, InputStream inputStream, String contentType) {
        try {
            boolean isAdd = StringUtils.isBlank(fileId) || getByFileId(fileId) == null;
            UploadFiles wdFile;
            if (isAdd) {
                wdFile = _saveFile(fileBType, fileId, null, fileName, contentType,uploader,desc,inputStream);
            } else {
                wdFile = _saveFile(fileBType, null, fileId, fileName, contentType,uploader,desc,inputStream);
            }
            if (null == wdFile) {
                throw new RuntimeException("保存文件失败");
            }
            return wdFile;
        } catch (Throwable t) {
            log.error("保存文件失败", t);
            throw new RuntimeException("保存文件失败");
        }
    }

    public UploadFiles saveFile(FileBType fileBType, MultipartFile multipartFile) {
        return _saveFile(fileBType, null, null, null, null, null, multipartFile);
    }

    public static UploadFiles saveFile(FileBType fileBType, String filePath) {
        ParamUtil.checkObjParam(fileBType,"fileBType");
        ParamUtil.checkStrParam(filePath,"filePath");

        InputStream fromFileStream = null;
        try {

            fromFileStream = new FileInputStream(filePath);
            return instance()._saveFile(fileBType, null, null, null, FileUtil.getContentType(FileUtil.getExtensionType(filePath)), null, null, fromFileStream);

        } catch (FileNotFoundException e) {
            log.error("保存文件异常",e);
            throw new RuntimeException(e);

        }finally {
            if(fromFileStream != null){
                try {
                    fromFileStream.close();
                } catch (IOException e) {
                    log.error("关闭文件流异常",e);
                }
            }
        }
    }

    private UploadFiles _saveFile(FileBType fileBType, String newFileId, String fileId, String fileName, String uploader, String fileDesc, MultipartFile multipartFile) {

        try {
            return _saveFile(fileBType, newFileId, fileId, StringUtils.isBlank(fileName) ? multipartFile.getOriginalFilename() : fileName, multipartFile.getContentType(), uploader, fileDesc, multipartFile.getInputStream());
        } catch (IOException e) {
            log.error("保存文件异常",e);
        }

        return null;

    }

    /**
     * 保存文件
     *
     * @param fileBType   业务类型
     * @param newFileId   外部业务指定文件id
     * @param fileId      文件id
     * @param fileName    文件名称
     * @param contentType 文件类型
     * @param fileStream  要保存的文件
     * @param uploader    上传的用户id
     * @param fileDesc    文件说明
     * @param fileStream  文件流
     * @return WdFile
     */
    private UploadFiles _saveFile(FileBType fileBType,
                                  String newFileId,
                                  String fileId,
                                  String fileName,
                                  String contentType,
                                  String uploader,
                                  String fileDesc,
                                  InputStream fileStream) {

        ParamUtil.checkObjParam(fileStream, "in");
        ParamUtil.checkStrParam(contentType, "contentType");

        if (fileBType == null && StringUtils.isBlank(fileId)) {
            throw new RuntimeException("fileBType,fileId不可同时为空");
        }

        IFileSaver fileSaver = getSaveFile(fileBType);
        SaveFileResult saveFileResult = null;
        try {
            UploadFiles file = null;
            if (StringUtils.isNotBlank(fileId)) {
                file = ParamUtil.requireNotNull(getFilesManager().get(fileId), "找不到文件信息:fileId" + fileId);
                fileBType = fileBType == null ? FileBType.getByValue(file.getFileBType()) : fileBType;
            }

            ParamUtil.checkObjParam(fileBType, "fileBType");

            if (StringUtils.isNotBlank(fileId)) {
                Objects.requireNonNull(file);
                fileSaver.deleteFile(file.getFileSavePlace(), FileBType.getByValue(file.getFileBType()));
            } else {
                fileId = StringUtils.isNotBlank(newFileId) ? newFileId : RandomStringUtil.gen18UUID();
            }

            saveFileResult = fileSaver.saveFile(fileBType,fileStream, fileId,StringUtils.isNotBlank(fileName) ? FileUtil.getExtNameByName(fileName) : FileUtil.getExtNameByCType(contentType),contentType);
            if(saveFileResult == null){
                log.error("保存文件失败。fileBType -> [{}],fileId -> [{}], fileName -> [{}], contentType -> [{}]",fileBType,fileId, fileName, contentType);
                return null;
            }

            boolean isNewFile = file == null;

            if (file == null) {
                file = new UploadFiles();
            }

            if (isNewFile) {
                file.setFileId(fileId);
                file.setCreatedTime(TimeUtil.getTime());
            }
            file.setUpdatedTime(TimeUtil.getTime());
            file.setFileBType(fileBType.getValue());

            file.setFilename(StringUtils.isNotBlank(fileName) ?  fileName : fileId);

            if (StringUtils.isNotBlank(fileDesc)) {
                file.setDescription(fileDesc);
            }

            if (StringUtils.isNotBlank(fileName)) {
                file.setExtension(FileUtil.getExtensionType(fileName));
            }

            file.setFileType(saveFileResult.getFileType());
            if (StringUtils.isNotBlank(uploader)) {
                file.setUserId(uploader);
            }

            file.setFileSize((int) saveFileResult.getFilesize());
            file.setFileSavePlace(saveFileResult.getFilePath());
            file.setFileUrl(saveFileResult.getFileUrl());
            file.setMd5(saveFileResult.getMd5());

            if (isNewFile) {
                getFilesManager().insert(file);
            } else {
                getFilesManager().update(file);
            }

            return file;

        } catch (Throwable ex) {
            log.error("保存文件失败。fileBType -> [{}],fileId -> [{}], fileName -> [{}], contentType -> [{}]", fileBType, fileId, fileName, contentType, ex);
            if(saveFileResult != null) {
                fileSaver.deleteFile(saveFileResult.getFilePath(), fileBType);
            }
        }

        return null;
    }

    /**
     * 删除文件 并更新文件资源表内容 (本方法不对文件做有效性验证)
     *
     * @param fileId 文件id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(String fileId) {
        UploadFiles file = getByFileId(fileId);
        if (null == file) {
            throw new RuntimeException("找不到文件 fileId:" + fileId);
        }
        FileBType fileBType = FileBType.getByValue(file.getFileBType());
        getFilesManager().deleteById(file.getFileId());

        IFileSaver savePlace = getSaveFile(fileBType);
        savePlace.deleteFile(file.getFileSavePlace(), fileBType);
    }

    public static String getFileUrls(String fileIds) {
        if(StringUtils.isBlank(fileIds)){
            return "";
        }

        String fileUrls = "";
        String[] fileIdArray = fileIds.split(",");
        for (String s : fileIdArray) {
            String f = getFileUrl(s);
            if(StringUtils.isBlank(f)){
                log.warn("找不到文件。fielid:{}",s);
                continue;
            }

            if(StringUtils.isNotBlank(fileUrls)){
                fileUrls = fileUrls.concat(",");
            }

            fileUrls = fileUrls.concat(f);
        }

        return fileUrls;
    }

    /**
     * 获取文件url
     *
     * @param fileId 文件id
     */
    public static String getFileUrl(String fileId) {
        return instance().getFileUrlIns(fileId);
    }

    public String getFileUrlIns(String fileId) {
        if (StringUtils.isBlank(fileId)) {
            return null;
        }
        UploadFiles file = getByFileId(fileId);
        if(file != null){
            return file.getFileUrl();
        }
        return null;
    }

    public static UploadFiles getFileInfo(String fileId){
        if (StringUtils.isBlank(fileId)) {
            return null;
        }
        
        return instance().getByFileId(fileId);
    }

    /**
     * 获取文件bytes
     *
     * @param fileId 文件id
     */
    public static byte[] getFileBytes(String fileId) {
        UploadFilesUtil instance = instance();
        return instance.getFileBytesIns(fileId);
    }

    public static String getFileBase64(String fileId){
        byte[] bytes = getFileBytes(fileId);
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 获取文件bytes
     *
     * @param fileId 文件id
     */
    public byte[] getFileBytesIns(String fileId) {
        if (StringUtils.isBlank(fileId)) {
            return null;
        }
        UploadFiles file = getByFileId(fileId);
        if(file != null) {
            FileBType fileBType = FileBType.getByValue(file.getFileBType());
            IFileSaver savePlace = getSaveFile(fileBType);
            return savePlace.getFileBytes(file.getFileSavePlace(), fileBType);
        }
        return null;
    }
    
    public static String getFileMD5(String fileId) {
        byte[] fileBytes = getFileBytes(fileId);
        return FileUtil.getFileMD5(fileBytes);
    }

    /**
     * 获取文件(二进制数组形式)及其信息 并校验
     *
     * @param fileId 文件主键
     * @return Map { WdFile "info" , byte[] "file"}
     */
    public Map<String, Object> getFile(String fileId) {
        UploadFiles file = getAndCheckByFileId(fileId);
        byte[] fileBytes = getFileBytes(fileId);
        if (fileBytes == null) {
            throw new ApiException("找不到文件!");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("info", file);
        result.put("file", fileBytes);
        return result;
    }


    /**
     * 获取文件
     *
     * @param fileId 文件id
     * @return WdFile 找不到返回null
     */
    public UploadFiles getByFileId(String fileId) {
        ParamUtil.checkStrParam(fileId, "fileId");
        UploadFiles wdFile = this.getFilesManager().get(fileId);
        if (wdFile == null || StringUtils.isEmpty(wdFile.getFileId())) {
            return null;
        }
        return wdFile;
    }

    /**
     * 获取并检查文件的有效性
     *
     * @param fileId 文件id
     * @return WdFile
     */
    public UploadFiles getAndCheckByFileId(String fileId) {
        ParamUtil.checkStrParam(fileId, "fileId");
        UploadFiles wdFile = this.getFilesManager().get(fileId);
        if (wdFile == null || StringUtils.isEmpty(wdFile.getFileId())) {
            throw new ApiException("找不到文件信息!!");
        }
        return wdFile;
    }

    private IFileSaver getSaveFile(FileBType fileBType) {
        ParamUtil.checkObjParam(fileBType, "fileBType");
        FilePlace savePlaceType = fileBType.getFilePlace();
        switch (savePlaceType) {
            case OSS:
                return this.getAliyunOssPlace();
            case 本地:
                return this.getLocalDiskPlace();
            case ZIP:
                return this.getZipDiskSaver();
            default:
                log.warn("savePlaceType：" + savePlaceType + "不正确 fileBType:" + fileBType);
                throw new RuntimeException("savePlaceType：" + savePlaceType + "不正确 fileBType:" + fileBType);
        }
    }
}
