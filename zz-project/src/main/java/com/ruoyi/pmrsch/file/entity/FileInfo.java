package com.ruoyi.pmrsch.file.entity;

import lombok.Data;

/**
 * @Description: 上传的文件名格式化
 * @Author: hhq
 * @Date: 2021/5/8 11:44
 */
@Data
public class FileInfo {
    /**
     * 文件类型，0:摄像头抓拍，1:日志
     */
    private Integer devFileType;
    /**
     * 文件创建时间
     */
    private Integer createTime;
    /**
     * 摄像头通道
     */
    private String cameraChannelNo;
    /**
     * 用户事件id
     */
    private String eventId;
    /**
     * 除去后缀名的文件名称
     */
    private String fileName;
    /**
     * 文件的后缀名
     */
    private String suffix;

    public FileInfo(String fileName){
        String[] names = fileName.split("\\.");

        this.fileName = names[0];
        if(names.length > 1){
            this.suffix = names[1];
        }

    }
}
