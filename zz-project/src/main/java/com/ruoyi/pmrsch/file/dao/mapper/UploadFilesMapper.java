package com.ruoyi.pmrsch.file.dao.mapper;

import com.ruoyi.pmrsch.file.dao.model.UploadFiles;

import java.util.List;

/**
 * 文件表DAO接口
 * <AUTHOR>
 * @version 2025-01-17
 */
public interface UploadFilesMapper {
    /**
     * 查询文件
     *
     * @param fileId 文件主键
     * @return 文件
     */
    public UploadFiles selectUploadFilesByFileId(String fileId);

    /**
     * 查询文件列表
     *
     * @param uploadFiles 文件
     * @return 文件集合
     */
    public List<UploadFiles> selectUploadFilesList(UploadFiles uploadFiles);

    /**
     * 新增文件
     *
     * @param uploadFiles 文件
     * @return 结果
     */
    public int insertUploadFiles(UploadFiles uploadFiles);

    /**
     * 修改文件
     *
     * @param uploadFiles 文件
     * @return 结果
     */
    public int updateUploadFiles(UploadFiles uploadFiles);

    /**
     * 删除文件
     *
     * @param fileId 文件主键
     * @return 结果
     */
    public int deleteUploadFilesByFileId(String fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUploadFilesByFileIds(String[] fileIds);
}
