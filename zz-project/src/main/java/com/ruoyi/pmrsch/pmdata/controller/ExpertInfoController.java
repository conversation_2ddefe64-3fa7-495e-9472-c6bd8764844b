package com.ruoyi.pmrsch.pmdata.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.pmrsch.pmdata.domain.ExpertInfo;
import com.ruoyi.pmrsch.pmdata.service.IExpertInfoService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 专家信息Controller
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
@Controller
@RequestMapping("/pmdata/expertinfo")
public class ExpertInfoController extends BaseController
{
    private String prefix = "pmdata/expertinfo";

    @Autowired
    private IExpertInfoService expertInfoService;

    @RequiresPermissions("pmdata:expertinfo:view")
    @GetMapping()
    public String info()
    {
        return prefix + "/expertinfo";
    }

    /**
     * 查询专家信息列表
     */
    @RequiresPermissions("pmdata:expertinfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExpertInfo expertInfo)
    {
        startPage();
        List<ExpertInfo> list = expertInfoService.selectExpertInfoList(expertInfo);
        return getDataTable(list);
    }

    /**
     * 导出专家信息列表
     */
    @RequiresPermissions("pmdata:expertinfo:export")
    @Log(title = "专家信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ExpertInfo expertInfo)
    {
        List<ExpertInfo> list = expertInfoService.selectExpertInfoList(expertInfo);
        ExcelUtil<ExpertInfo> util = new ExcelUtil<ExpertInfo>(ExpertInfo.class);
        return util.exportExcel(list, "专家信息数据");
    }
    
    /**
     * 下载导入模板
     */
    @RequiresPermissions("pmdata:expertinfo:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<ExpertInfo> util = new ExcelUtil<ExpertInfo>(ExpertInfo.class);
        return util.importTemplateExcel("专家信息数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("pmdata:expertinfo:import")
    @Log(title = "专家信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ExpertInfo> util = new ExcelUtil<ExpertInfo>(ExpertInfo.class);
        List<ExpertInfo> expertInfoList = util.importExcel(file.getInputStream());
        String message = importExpertInfo(expertInfoList, updateSupport);
        return AjaxResult.success(message);
    }
    
    /**
     * 导入专家信息数据
     * 
     * @param expertInfoList 专家信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importExpertInfo(List<ExpertInfo> expertInfoList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(expertInfoList) || expertInfoList.size() == 0)
        {
            throw new ServiceException("导入专家信息数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ExpertInfo expertInfo : expertInfoList)
        {
            try
            {
                // 验证是否存在这个专家信息
                ExpertInfo query = new ExpertInfo();
                query.setExpertName(expertInfo.getExpertName());
                query.setPhone(expertInfo.getPhone());
                List<ExpertInfo> existList = expertInfoService.selectExpertInfoList(query);
                
                if (existList == null || existList.isEmpty())
                {
                    expertInfoService.insertExpertInfo(expertInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、专家 " + expertInfo.getExpertName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    expertInfo.setId(existList.get(0).getId());
                    expertInfoService.updateExpertInfo(expertInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、专家 " + expertInfo.getExpertName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、专家 " + expertInfo.getExpertName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、专家 " + expertInfo.getExpertName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增专家信息
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存专家信息
     */
    @RequiresPermissions("pmdata:expertinfo:add")
    @Log(title = "专家信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ExpertInfo expertInfo)
    {
        return toAjax(expertInfoService.insertExpertInfo(expertInfo));
    }

    /**
     * 修改专家信息
     */
    @RequiresPermissions("pmdata:expertinfo:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        ExpertInfo expertInfo = expertInfoService.selectExpertInfoById(id);
        mmap.put("expertInfo", expertInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存专家信息
     */
    @RequiresPermissions("pmdata:expertinfo:edit")
    @Log(title = "专家信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ExpertInfo expertInfo)
    {
        return toAjax(expertInfoService.updateExpertInfo(expertInfo));
    }

    /**
     * 删除专家信息
     */
    @RequiresPermissions("pmdata:expertinfo:remove")
    @Log(title = "专家信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(expertInfoService.deleteExpertInfoByIds(ids));
    }

    /**
     * 获取专家列表（用于下拉选择）
     */
    @GetMapping("/suggestList")
    @ResponseBody
    public Map suggestList(ExpertInfo expertInfo)
    {
        List<ExpertInfo> list = expertInfoService.selectExpertInfoList(expertInfo);
        Map data = new HashMap<>();

        data.put("value",list.stream().map(e -> {
            Map einfo = new HashMap<>();
            einfo.put("id",e.getId());
            einfo.put("phone",e.getPhone());
            einfo.put("expertName",e.getExpertName());
            einfo.put("industry",e.getIndustry());
            return einfo;
        }).collect(Collectors.toList()));
        
        return data;
    }
}
