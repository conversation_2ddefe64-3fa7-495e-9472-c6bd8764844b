package com.ruoyi.pmrsch.pmdata.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 客户信息对象 zz_customer
 */
@Data
public class Customer extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String name;

    /** 客户地址 */
    @Excel(name = "客户地址")
    private String address;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private Long deptId;
}
