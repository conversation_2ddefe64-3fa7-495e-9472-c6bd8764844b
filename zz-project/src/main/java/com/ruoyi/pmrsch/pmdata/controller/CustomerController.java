package com.ruoyi.pmrsch.pmdata.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.service.ICustomerService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 客户信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Controller
@RequestMapping("/pmdata/customer")
public class CustomerController extends BaseController{
    private String prefix = "pmdata/customer";

    @Autowired
    private ICustomerService customerService;

    @RequiresPermissions("pmdata:customer:view")
    @GetMapping()
    public String customer(){
        return prefix + "/customer";
    }

    /**
     * 查询客户信息列表
     */
    @RequiresPermissions("pmdata:customer:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Customer customer){
        startPage();
        List<Customer> list = customerService.selectCustomerList(customer);
        return getDataTable(list);
    }

    /**
     * 导出客户信息列表
     */
    @RequiresPermissions("pmdata:customer:export")
    @Log(title = "客户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Customer customer){
        List<Customer> list = customerService.selectCustomerList(customer);
        ExcelUtil<Customer> util = new ExcelUtil<Customer>(Customer.class);
        return util.exportExcel(list, "客户信息数据");
    }

    /**
     * 下载模板
     */
    @RequiresPermissions("pmdata:customer:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<Customer> util = new ExcelUtil<Customer>(Customer.class);
        return util.importTemplateExcel("客户信息数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("pmdata:customer:import")
    @Log(title = "客户信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<Customer> util = new ExcelUtil<Customer>(Customer.class);
        List<Customer> customerList = util.importExcel(file.getInputStream());
        String message = importCustomer(customerList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入客户数据
     * 
     * @param customerList 客户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importCustomer(List<Customer> customerList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(customerList) || customerList.size() == 0)
        {
            throw new ServiceException("导入客户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (Customer customer : customerList)
        {
            try
            {
                // 验证是否存在相同名称的客户
                Customer existCustomer = new Customer();
                existCustomer.setName(customer.getName());
                List<Customer> customers = customerService.selectCustomerList(existCustomer);
                if (StringUtils.isEmpty(customers))
                {
                    // 新增客户
                    successNum++;
                    customerService.insertCustomer(customer);
                    successMsg.append("<br/>" + successNum + "、客户 " + customer.getName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    // 更新客户
                    successNum++;
                    customer.setId(customers.get(0).getId());
                    customerService.updateCustomer(customer);
                    successMsg.append("<br/>" + successNum + "、客户 " + customer.getName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、客户 " + customer.getName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、客户 " + customer.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增客户信息
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/add";
    }

    /**
     * 新增保存客户信息
     */
    @RequiresPermissions("pmdata:customer:add")
    @Log(title = "客户信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Customer customer){
        return toAjax(customerService.insertCustomer(customer));
    }

    /**
     * 修改客户信息
     */
    @RequiresPermissions("pmdata:customer:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        Customer customer = customerService.selectCustomerById(id);
        mmap.put("customer", customer);
        return prefix + "/edit";
    }

    /**
     * 修改保存客户信息
     */
    @RequiresPermissions("pmdata:customer:edit")
    @Log(title = "客户信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Customer customer){
        return toAjax(customerService.updateCustomer(customer));
    }

    /**
     * 删除客户信息
     */
    @RequiresPermissions("pmdata:customer:remove")
    @Log(title = "客户信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(customerService.deleteCustomerByIds(ids));
    }
}
