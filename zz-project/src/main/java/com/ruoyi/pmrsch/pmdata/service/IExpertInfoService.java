package com.ruoyi.pmrsch.pmdata.service;

import java.util.List;
import com.ruoyi.pmrsch.pmdata.domain.ExpertInfo;

/**
 * 专家信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IExpertInfoService 
{
    /**
     * 查询专家信息
     * 
     * @param id 专家信息主键
     * @return 专家信息
     */
    public ExpertInfo selectExpertInfoById(String id);

    /**
     * 查询专家信息列表
     * 
     * @param expertInfo 专家信息
     * @return 专家信息集合
     */
    public List<ExpertInfo> selectExpertInfoList(ExpertInfo expertInfo);

    /**
     * 新增专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    public int insertExpertInfo(ExpertInfo expertInfo);

    /**
     * 修改专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    public int updateExpertInfo(ExpertInfo expertInfo);

    /**
     * 批量删除专家信息
     * 
     * @param ids 需要删除的专家信息主键集合
     * @return 结果
     */
    public int deleteExpertInfoByIds(String ids);

    /**
     * 删除专家信息信息
     * 
     * @param id 专家信息主键
     * @return 结果
     */
    public int deleteExpertInfoById(String id);
}
