package com.ruoyi.pmrsch.pmdata.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.pmrsch.pmdata.mapper.ExpertInfoMapper;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.pmdata.domain.ExpertInfo;
import com.ruoyi.pmrsch.pmdata.service.IExpertInfoService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.DateUtils;

/**
 * 专家信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class ExpertInfoServiceImpl implements IExpertInfoService 
{
    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    /**
     * 查询专家信息
     * 
     * @param id 专家信息主键
     * @return 专家信息
     */
    @Override
    public ExpertInfo selectExpertInfoById(String id)
    {
        return expertInfoMapper.selectExpertInfoById(id);
    }

    /**
     * 查询专家信息列表
     * 
     * @param expertInfo 专家信息
     * @return 专家信息
     */
    @Override
    public List<ExpertInfo> selectExpertInfoList(ExpertInfo expertInfo)
    {
        return expertInfoMapper.selectExpertInfoList(expertInfo);
    }

    /**
     * 新增专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int insertExpertInfo(ExpertInfo expertInfo)
    {
        expertInfo.setId(RandomStringUtil.gen18UUID());
        expertInfo.setCreatedAt(DateUtils.getNowDate());
        expertInfo.setUserId(ShiroUtils.getUserId());
        expertInfo.setDeptId(ShiroUtils.getSysUser().getDeptId());
        return expertInfoMapper.insertExpertInfo(expertInfo);
    }

    /**
     * 修改专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int updateExpertInfo(ExpertInfo expertInfo)
    {
        expertInfo.setUpdateBy(ShiroUtils.getUserId()+"");
        expertInfo.setUpdateTime(new Date());
        return expertInfoMapper.updateExpertInfo(expertInfo);
    }

    /**
     * 批量删除专家信息
     * 
     * @param ids 需要删除的专家信息主键
     * @return 结果
     */
    @Override
    public int deleteExpertInfoByIds(String ids)
    {
        return expertInfoMapper.deleteExpertInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除专家信息信息
     * 
     * @param id 专家信息主键
     * @return 结果
     */
    @Override
    public int deleteExpertInfoById(String id)
    {
        return expertInfoMapper.deleteExpertInfoById(id);
    }
}
