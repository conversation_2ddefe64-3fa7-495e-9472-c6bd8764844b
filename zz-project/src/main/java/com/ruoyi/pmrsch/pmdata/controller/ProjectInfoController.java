package com.ruoyi.pmrsch.pmdata.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.service.IProjectInfoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Controller
@RequestMapping("/pmdata/projectinfo")
public class ProjectInfoController extends BaseController{
    private String prefix = "pmdata/projectinfo";

    @Autowired
    private IProjectInfoService projectInfoService;

    @RequiresPermissions("pmdata:projectinfo:view")
    @GetMapping()
    public String projectinfo(){
        return prefix + "/projectinfo";
    }

    /**
     * 查询项目信息列表
     */
    @RequiresPermissions("pmdata:projectinfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectInfo projectInfo){
        startPage();
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        return getDataTable(list);
    }

    /**
     * 查询项目信息列表
     */
    @RequiresPermissions("pmdata:projectinfo:list")
    @PostMapping("/list-data")
    @ResponseBody
    public List<ProjectInfo> listData(){
        ProjectInfo q = new ProjectInfo();
        return projectInfoService.selectProjectInfoList(q);
    }

    /**
     * 导出项目信息列表
     */
    @RequiresPermissions("pmdata:projectinfo:export")
    @Log(title = "项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectInfo projectInfo){
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        ExcelUtil<ProjectInfo> util = new ExcelUtil<ProjectInfo>(ProjectInfo.class);
        return util.exportExcel(list, "项目信息数据");
    }

    /**
     * 新增项目信息
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/add";
    }

    /**
     * 新增保存项目信息
     */
    @RequiresPermissions("pmdata:projectinfo:add")
    @Log(title = "项目信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectInfo projectInfo){
        return toAjax(projectInfoService.insertProjectInfo(projectInfo));
    }

    /**
     * 修改项目信息
     */
    @RequiresPermissions("pmdata:projectinfo:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id,String fragment, ModelMap mmap){
        ProjectInfo projectInfo = projectInfoService.selectProjectInfoById(id);
        mmap.put("projectInfo", projectInfo);

        return StringUtils.isBlank(fragment) ? prefix + "/edit" : "fragment/project::"+fragment;
    }

    /**
     * 修改保存项目信息
     */
    @RequiresPermissions("pmdata:projectinfo:edit")
    @Log(title = "项目信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectInfo projectInfo){
        return toAjax(projectInfoService.updateProjectInfo(projectInfo));
    }

    /**
     * 删除项目信息
     */
    @RequiresPermissions("pmdata:projectinfo:remove")
    @Log(title = "项目信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(projectInfoService.deleteProjectInfoByIds(ids));
    }
}
