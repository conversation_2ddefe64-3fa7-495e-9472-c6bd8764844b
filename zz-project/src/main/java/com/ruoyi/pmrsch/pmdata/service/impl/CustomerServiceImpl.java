package com.ruoyi.pmrsch.pmdata.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.mapper.CustomerMapper;
import com.ruoyi.pmrsch.pmdata.service.ICustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;

import java.util.Date;
import java.util.List;

/**
 * 客户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Service
public class CustomerServiceImpl implements ICustomerService {
    @Autowired
    private CustomerMapper customerMapper;

    /**
     * 查询客户信息
     * 
     * @param id 客户信息主键
     * @return 客户信息
     */
    @Override
    public Customer selectCustomerById(String id){
        return customerMapper.selectCustomerById(id);
    }

    /**
     * 查询客户信息列表
     * 
     * @param customer 客户信息
     * @return 客户信息
     */
    @Override
    public List<Customer> selectCustomerList(Customer customer){
        return customerMapper.selectCustomerList(customer);
    }

    /**
     * 新增客户信息
     * 
     * @param customer 客户信息
     * @return 结果
     */
    @Override
    public int insertCustomer(Customer customer)
    {
        customer.setId(RandomStringUtil.genSNByDate());
        customer.setCreateTime(new Date());
        customer.setUserId(ShiroUtils.getUserId());
        customer.setDeptId(ShiroUtils.getSysUser().getDeptId());
        return customerMapper.insertCustomer(customer);
    }

    /**
     * 修改客户信息
     * 
     * @param customer 客户信息
     * @return 结果
     */
    @Override
    public int updateCustomer(Customer customer){
        return customerMapper.updateCustomer(customer);
    }

    /**
     * 批量删除客户信息
     * 
     * @param ids 需要删除的客户信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerByIds(String ids){
        return customerMapper.deleteCustomerByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除客户信息信息
     * 
     * @param id 客户信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerById(String id){
        return customerMapper.deleteCustomerById(id);
    }
}
