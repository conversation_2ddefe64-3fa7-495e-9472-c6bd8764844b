package com.ruoyi.pmrsch.pmdata.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 专家信息对象 zz_expert_info
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
public class ExpertInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;

    /** 来源 */
    @Excel(name = "来源")
    private String source;

    /** 专家类型 */
    private Long expertTypeCode;

    @Excel(name = "专家类型")
    private String expertType;

    /** 所属行业 */
    @Excel(name = "所属行业")
    private String industry;

    /** 公司/医院名称 */
    @Excel(name = "公司/医院名称")
    private String companyName;

    /** 部门/科室 */
    @Excel(name = "部门/科室")
    private String department;

    /** 专家姓名 */
    @Excel(name = "专家姓名")
    private String expertName;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 微信 */
    @Excel(name = "微信")
    private String wechat;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 职位/职级 */
    @Excel(name = "职位/职级")
    private String position;

    /** 专家背景 */
    @Excel(name = "专家背景")
    private String background;

    /** 价格/小时 */
    @Excel(name = "建议推荐价")
    private BigDecimal price;

    /** 评价 */
    @Excel(name = "评价")
    private String evaluation;

    /** 简介 */
    @Excel(name = "简介")
    private String intro;

    /** 名片/工作证 */
    @Excel(name = "名片/工作证")
    private String businessCard;

    /** 上任公司 */
    @Excel(name = "上任公司")
    private String previousCompany;

    /** 擅长领域 */
    @Excel(name = "擅长领域")
    private String expertiseArea;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private Long deptId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getExpertTypeCode() {
        return expertTypeCode;
    }

    public void setExpertTypeCode(Long expertTypeCode) {
        this.expertTypeCode = expertTypeCode;
    }

    public String getExpertType() {
        return expertType;
    }

    public void setExpertType(String expertType) {
        this.expertType = expertType;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getExpertName() {
        return expertName;
    }

    public void setExpertName(String expertName) {
        this.expertName = expertName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getBusinessCard() {
        return businessCard;
    }

    public void setBusinessCard(String businessCard) {
        this.businessCard = businessCard;
    }

    public String getPreviousCompany() {
        return previousCompany;
    }

    public void setPreviousCompany(String previousCompany) {
        this.previousCompany = previousCompany;
    }

    public String getExpertiseArea() {
        return expertiseArea;
    }

    public void setExpertiseArea(String expertiseArea) {
        this.expertiseArea = expertiseArea;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
