package com.ruoyi.pmrsch.pmdata.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.pmrsch.task.entity.TaskTotal;

import java.util.Date;
import java.util.List;

/**
 * 项目信息对象 zz_project_info
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
public class ProjectInfo extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String pNo;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String pName;

    /** 项目类型 ID */
    @Excel(name = "项目类型 ID")
    private String pTypeCode;

    /** 项目类型名称 */
    @Excel(name = "项目类型")
    private String pTypeLabel;

    /** 客户项目编号 */
    @Excel(name = "客户项目编号")
    private String pNoCus;

    /** 客户ID */
    @Excel(name = "客户ID")
    private String customerId;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 状态（进行中、已完成、已删除） */
    @Excel(name = "状态", readConverterExp = "进行中、已完成、已删除")
    private Integer statusCode;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private Long deptId;

    /** 所属月份 */
    @Excel(name = "所属月份")
    private String ofMouth;

    private List<TaskTotal> taskTotalList;
    private Customer customer;

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpNo() {
        return pNo;
    }

    public void setpNo(String pNo) {
        this.pNo = pNo;
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName;
    }

    public String getpTypeCode() {
        return pTypeCode;
    }

    public void setpTypeCode(String pTypeCode) {
        this.pTypeCode = pTypeCode;
    }

    public String getpTypeLabel() {
        return pTypeLabel;
    }

    public void setpTypeLabel(String pTypeLabel) {
        this.pTypeLabel = pTypeLabel;
    }

    public String getpNoCus() {
        return pNoCus;
    }

    public void setpNoCus(String pNoCus) {
        this.pNoCus = pNoCus;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getOfMouth() {
        return ofMouth;
    }

    public void setOfMouth(String ofMouth) {
        this.ofMouth = ofMouth;
    }

    public List<TaskTotal> getTaskTotalList() {
        return taskTotalList;
    }

    public void setTaskTotalList(List<TaskTotal> taskTotalList) {
        this.taskTotalList = taskTotalList;
    }
}
