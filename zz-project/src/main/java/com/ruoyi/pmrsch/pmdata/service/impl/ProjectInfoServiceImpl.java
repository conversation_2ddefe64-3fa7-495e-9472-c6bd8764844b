package com.ruoyi.pmrsch.pmdata.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.mapper.CustomerMapper;
import com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper;
import com.ruoyi.pmrsch.pmdata.service.IProjectInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.ShiroUtils;

import java.util.Date;
import java.util.List;

/**
 * 项目信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Service
public class ProjectInfoServiceImpl implements IProjectInfoService {
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    @Autowired
    private CustomerMapper customerMapper;

    /**
     * 查询项目信息
     * 
     * @param id 项目信息主键
     * @return 项目信息
     */
    @Override
    public ProjectInfo selectProjectInfoById(String id){
        ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(id);
        supplement(projectInfo);
        return projectInfo;
    }

    /**
     * 查询项目信息列表
     * 
     * @param projectInfo 项目信息
     * @return 项目信息
     */
    @DataScope(deptAlias = "t", userAlias = "t")
    @Override
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo){
        List<ProjectInfo> projectInfos = projectInfoMapper.selectProjectInfoList(projectInfo);
        projectInfos.forEach(this::supplement);
        
        return projectInfos;
    }

    /**
     * 新增项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    @Override
    public int insertProjectInfo(ProjectInfo projectInfo)
    {
        projectInfo.setId(RandomStringUtil.gen32UUID());
        projectInfo.setpNo(RandomStringUtil.genSNByDateInc());
        projectInfo.setOfMouth(TimeUtil.toString(new Date(), "yyyyMM"));
        projectInfo.setCreatedAt(new Date());
        
        projectInfo.setUserId(ShiroUtils.getUserId());
        projectInfo.setDeptId(ShiroUtils.getSysUser().getDeptId());
        return projectInfoMapper.insertProjectInfo(projectInfo);
    }

    /**
     * 修改项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    @Override
    public int updateProjectInfo(ProjectInfo projectInfo){
        return projectInfoMapper.updateProjectInfo(projectInfo);
    }

    /**
     * 批量删除项目信息
     * 
     * @param ids 需要删除的项目信息主键
     * @return 结果
     */
    @Override
    public int deleteProjectInfoByIds(String ids){
        return projectInfoMapper.deleteProjectInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目信息信息
     * 
     * @param id 项目信息主键
     * @return 结果
     */
    @Override
    public int deleteProjectInfoById(String id){
        return projectInfoMapper.deleteProjectInfoById(id);
    }

    private void supplement(ProjectInfo projectInfo){
        if(projectInfo == null){
            return;
        }

        Customer customer = customerMapper.selectCustomerById(projectInfo.getCustomerId());
        projectInfo.setCustomer(customer);
    }
}
