package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.task.domain.ProjectTaskLabor;

import java.util.List;
import java.util.Map;

/**
 * 项目任务劳务费Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskLaborMapper {
    /**
     * 查询项目任务劳务费
     * 
     * @param id 项目任务劳务费主键
     * @return 项目任务劳务费
     */
    public ProjectTaskLabor selectProjectTaskLaborById(String id);

    /**
     * 查询项目任务劳务费列表
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 项目任务劳务费集合
     */
    public List<ProjectTaskLabor> selectProjectTaskLaborList(ProjectTaskLabor projectTaskLabor);

    /**
     * 新增项目任务劳务费
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 结果
     */
    public int insertProjectTaskLabor(ProjectTaskLabor projectTaskLabor);

    /**
     * 修改项目任务劳务费
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 结果
     */
    public int updateProjectTaskLabor(ProjectTaskLabor projectTaskLabor);

    /**
     * 删除项目任务劳务费
     * 
     * @param id 项目任务劳务费主键
     * @return 结果
     */
    public int deleteProjectTaskLaborById(String id);

    /**
     * 批量删除项目任务劳务费
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskLaborByIds(String[] ids);

    /**
     * 更新项目任务劳务费付款信息
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 结果
     */
    public int updateProjectTaskLaborPayment(ProjectTaskLabor projectTaskLabor);

    /**
     * 查询项目任务劳务费统计信息
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 统计信息
     */
    public Map<String, Object> selectProjectTaskLaborTotal(ProjectTaskLabor projectTaskLabor);
}
