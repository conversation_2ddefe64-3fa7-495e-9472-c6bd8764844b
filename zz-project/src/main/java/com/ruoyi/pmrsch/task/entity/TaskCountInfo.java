package com.ruoyi.pmrsch.task.entity;/**
 * <AUTHOR>
 * @Date 2025/3/14
 */

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 描述
 * @className TaskCountInfo
 * <AUTHOR>
 * @date 2025年03月14日 07:08
 */
@Data
public class TaskCountInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private String taskId;

    /** 申请人 */
    @Excel(name = "督导")
    private String taskAppler;

    /** 申请状态 */
    @Excel(name = "申请状态", readConverterExp = "待=提交、待审核、已审核")
    private Integer applyStatusCode;

    /** 决算状态 */
    @Excel(name = "决算状态", readConverterExp = "待=提交、财务待审、经理待审、完成")
    private Integer settleStatusCode;

    /** 申请编号日期 */
    @Excel(name = "申请编号日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyNoDate;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 客户地址 */
    @Excel(name = "客户地址")
    private String customerAddress;

    /** 客户联系人 */
    @Excel(name = "客户联系人")
    private String customerContact;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;

    /** 公司项目编号 */
    @Excel(name = "公司项目编号")
    private String companyProjectNo;

    /** 客户项目编号 */
    @Excel(name = "客户项目编号")
    private String customerProjectNo;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 执行城市 */
    @Excel(name = "执行城市")
    private String executionCity;

    /** 项目类型 */
    @Excel(name = "项目类型")
    private String projectType;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 实际开始时间 */
    @Excel(name = "实际开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualStartTime;

    /** 实际结束时间 */
    @Excel(name = "实际结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualEndTime;

    /** 数量 */
    @Excel(name = "数量")
    private Integer sampleQuantity;

    /** 样本额 */
    @Excel(name = "样本额")
    private Double sampleAmount;

    /** 其它费用量 */
    @Excel(name = "其它费用量")
    private Integer otherQuantity;

    /** 其它费用额 */
    @Excel(name = "其它费用额")
    private Double otherAmount;

    /** 样本结算量 */
    @Excel(name = "样本结算量")
    private Integer sampleQuantityFinish;

    /** 样本结算额 */
    @Excel(name = "样本结算额")
    private Double sampleAmountFinish;

    /** 样本补充结算量 */
    @Excel(name = "样本补充结算量")
    private Integer sampleQuantityFinishAdd;

    /** 样本补充结算额 */
    @Excel(name = "样本补充结算额")
    private Double sampleAmountFinishAdd;

    /** 其它费用结算量 */
    @Excel(name = "其它费用结算量")
    private Integer otherQuantityFinish;

    /** 其它费用结算额 */
    @Excel(name = "其它费用结算额")
    private Double otherAmountFinish;

    /** 其它费用补充结算量 */
    @Excel(name = "其它费用补充结算量")
    private Integer otherQuantityFinishAdd;

    /** 其它费用补充结算额 */
    @Excel(name = "其它费用补充结算额")
    private Double otherAmountFinishAdd;

    /** 合同金额 = 样本额 + 其它费用额 */
    @Excel(name = "合同金额")
    private Double contractAmount;

    /** 最终合同额 = 样本结算额 + 样本补充结算额 + 其它费用结算额 + 其它费用补充结算额 */
    @Excel(name = "最终合同额")
    private Double finalContractAmount;

    /** 实际合同差额 = 最终合同额 - 合同金额 */
    @Excel(name = "实际合同差额")
    private Double actualContractDifference;
    
    /** 预算申请时间 */
    @Excel(name = "预算申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date budgtApplyTime;

    /** 预算总额 */
    @Excel(name = "预算总额")
    private Double budgetTotal;

    /** 借款申请时间 */
    @Excel(name = "借款申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loansApplyTime;

    /** 借款总额 */
    @Excel(name = "借款总额")
    private Double loansTotal;

    /** 决算申请时间 */
    @Excel(name = "决算申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleApplyTime;

    /** 决算总额 */
    @Excel(name = "决算总额")
    private Double settleTotal;

    /** 发票申请时间 */
    @Excel(name = "开票时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date invoiceTime;

    /** 开票总额 */
    @Excel(name = "开票总额")
    private Double invoiceTotal;

    /** 收款申请时间 */
    @Excel(name = "收款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date collectTime;

    /** 收款总额 */
    @Excel(name = "收款总额")
    private Double collectionTotal;

    /** 扣款 */
    @Excel(name = "扣款")
    private Double deductionAmount;

    /** 扣款比例 */
    @Excel(name = "扣款比例")
    private Double deductionRatio;

    /** 费用核对日期 */
    @Excel(name = "费用核对日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date costCheckDate;

    /** 决算比例（利润） */
    @Excel(name = "决算比例（利润）")
    private Double finalProfitRatio;

    /** 预决算差额 = 决算总额 - 预算总额 */
    @Excel(name = "预决算差额")
    private Double settleDifference;

    /** 其它需要支付费用 */
    @Excel(name = "其它需要支付费用")
    private Double otherCost;

    /** 已收预付费 */
    @Excel(name = "已收预付费")
    private Double prepaidCost;

    /** 营业额 = 最终合同额 - 扣款 */
    @Excel(name = "营业额")
    private Double businessAmount;

    /** 应收款 = 最终合同额 - 其它需要支付费用 - 已收预付费 */
    @Excel(name = "应收款")
    private Double receivables;

    /** 已收 */
    @Excel(name = "已收")
    private Double received;

    /** 未开票总额 = 应收款 - 开票总额 */
    @Excel(name = "未开票总额")
    private Double unpaidTotal;

    /** 未收款总额 = 应收款 - 收款总额 */
    @Excel(name = "未收款总额")
    private Double unpaidCollectionTotal;

    /** 项目回扣 */
    @Excel(name = "项目回扣")
    private Double projectRebate;

    /** 客户评议 */
    @Excel(name = "客户评议")
    private String customerComment;

    /** 所属月份 */
    @Excel(name = "所属月份")
    private String ofMouth;
}
