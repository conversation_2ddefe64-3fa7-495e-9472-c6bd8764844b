package com.ruoyi.pmrsch.task.controller;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import lombok.Data;

@Data
public class ExpertConsultationExportVO {

    /** 督导名称 */
    @Excel(name = "督导")
    private String supervisorName;

    @Excel(name = "咨询日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date consultationDate;

    /** 咨询时间 */
    @Excel(name = "咨询时间", width = 30)
    private Time consultationTime;

    @Excel(name = "项目编号")
    private String projectNo; // 对应 projectInfo.pNo

    @Excel(name = "项目名称")
    private String projectName; // 对应 projectInfo.pName

    @Excel(name = "专家姓名")
    private String expertName; // 对应 expertInfo.expertName

    @Excel(name = "专家手机号")
    private String expertPhone; // 对应 expertInfo.phone

    @Excel(name = "客户名称")
    private String customerName; // 对应 customer.name

    /** 费用类型 code */
    @Excel(name = "费用类型 code")
    private String sampleTypeCode;

    /** 定量数量或访谈时长(min) */
    @Excel(name = "定量数量或访谈时长(min)")
    private Long consultationDuration;

    /** 单价(RMB) */
    @Excel(name = "单价(RMB)")
    private BigDecimal unitPrice;

    @Excel(name = "客户费用合计")
    private BigDecimal customerTotalCost;

    /** 渠道 */
    @Excel(name = "渠道")
    private String channel;

    /** 渠道报价(RMB) */
    @Excel(name = "渠道报价(RMB)")
    private BigDecimal channelPrice;

    @Excel(name = "渠道成本")
    private BigDecimal channelCost;

    /** 问题 */
    @Excel(name = "问题")
    private String question;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系人邮箱 */
    @Excel(name = "联系人邮箱")
    private String contactEmail;

    @Excel(name = "开票状态", readConverterExp = "0=未对账,1=已对账,2=已开票")
    private Long invoiceStatusCode;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 项目类型（label） */
    @Excel(name = "项目类型")
    private String projectTypeLabel;

    /** 专家所属公司 */
    @Excel(name = "专家所属公司")
    private String expertCompanyName;

}
