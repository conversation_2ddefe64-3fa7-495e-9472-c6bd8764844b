package com.ruoyi.pmrsch.task.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 专家咨询账单统计对象
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
public class ExpertConsultationBilling extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 用户姓名 */
    @Excel(name = "姓名")
    private String userName;

    /** 营业额 */
    @Excel(name = "营业额(RMB)")
    private BigDecimal revenue;

    /** 渠道成本 */
    @Excel(name = "渠道成本(RMB)")
    private BigDecimal channelCost;

    /** 渠道/费用比 */
    @Excel(name = "渠道/费用比(%)")
    private BigDecimal channelCostRatio;

    /** 查询范围开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /** 查询范围结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

}