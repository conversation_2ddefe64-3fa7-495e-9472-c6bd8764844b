package com.ruoyi.pmrsch.task.entity;

import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;

/**
 * <AUTHOR>
 * @Date 2025/1/31
 */
public enum ApplyStatus implements EnumIntegerInterface {
    待提审(0),
    提审(1),
    审核(2),
    取消(-1),
    拒绝(-2);

    private Integer value;

    ApplyStatus(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static ApplyStatus getByValue(int value) {
        return EnumIntegerInterface.getByValue(value, ApplyStatus.class);
    }

    public String formatName() {
        switch (this){
            case 待提审:
                return "待提审";
            case 提审:
                return "待审核";
            case 审核:
                return "已通过";
            case 取消:
                return "已取消";
            case 拒绝:
                return "已拒绝";

            default:
                throw new IllegalStateException("Unexpected value: " + this);
        }
    }
}