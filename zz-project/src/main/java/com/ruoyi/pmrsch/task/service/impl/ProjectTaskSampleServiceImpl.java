package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.pmrsch.task.domain.ProjectTaskSample;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskSampleMapper;
import com.ruoyi.pmrsch.task.service.IProjectTaskSampleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目任务样本Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskSampleServiceImpl implements IProjectTaskSampleService {
    @Autowired
    private ProjectTaskSampleMapper projectTaskSampleMapper;

    /**
     * 查询项目任务样本
     * 
     * @param id 项目任务样本主键
     * @return 项目任务样本
     */
    @Override
    public ProjectTaskSample selectProjectTaskSampleById(String id){
        return projectTaskSampleMapper.selectProjectTaskSampleById(id);
    }

    /**
     * 查询项目任务样本列表
     * 
     * @param projectTaskSample 项目任务样本
     * @return 项目任务样本
     */
    @Override
    public List<ProjectTaskSample> selectProjectTaskSampleList(ProjectTaskSample projectTaskSample){
        return projectTaskSampleMapper.selectProjectTaskSampleList(projectTaskSample);
    }

    /**
     * 新增项目任务样本
     * 
     * @param projectTaskSample 项目任务样本
     * @return 结果
     */
    @Override
    public int insertProjectTaskSample(ProjectTaskSample projectTaskSample){
        return projectTaskSampleMapper.insertProjectTaskSample(projectTaskSample);
    }

    /**
     * 修改项目任务样本
     * 
     * @param projectTaskSample 项目任务样本
     * @return 结果
     */
    @Override
    public int updateProjectTaskSample(ProjectTaskSample projectTaskSample){
        return projectTaskSampleMapper.updateProjectTaskSample(projectTaskSample);
    }

    /**
     * 批量删除项目任务样本
     * 
     * @param ids 需要删除的项目任务样本主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskSampleByIds(String ids){
        return projectTaskSampleMapper.deleteProjectTaskSampleByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务样本信息
     * 
     * @param id 项目任务样本主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskSampleById(String id){
        return projectTaskSampleMapper.deleteProjectTaskSampleById(id);
    }

    public void delByTaskId(String taskId) {
        ProjectTaskSample query = new ProjectTaskSample();
        query.setProjectTaskId(taskId);
        List<ProjectTaskSample> samples = projectTaskSampleMapper.selectProjectTaskSampleList(query);
        
        if (samples != null && !samples.isEmpty()) {
            List<String> ids = samples.stream().map(ProjectTaskSample::getId).collect(Collectors.toList());
            String idsStr = String.join(",", ids);
            projectTaskSampleMapper.deleteProjectTaskSampleByIds(Convert.toStrArray(idsStr));
        }
    }
}
