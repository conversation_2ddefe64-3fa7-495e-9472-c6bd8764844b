package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.task.domain.ProjectTaskLabor;
import com.ruoyi.pmrsch.task.service.IProjectTaskLaborService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 项目任务劳务费Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/labor")
public class LaborController extends BaseController{
    private String prefix = "task/labor";

    @Autowired
    private IProjectTaskLaborService projectTaskLaborService;

    @RequiresPermissions("task:labor:view")
    @GetMapping()
    public String labor(String projectTaskId,Integer isView,ModelMap mmap){
        mmap.put("projectTaskId", projectTaskId);
        if(Objects.equals(isView,1)){
            return prefix + "/laborView";
        }else {
            return prefix + "/labor";
        }
    }

    /**
     * 劳务费查询页面
     */
    @RequiresPermissions("task:labor:query")
    @GetMapping("/query")
    public String query(){
        return prefix + "/query";
    }

    /**
     * 查询项目任务劳务费列表
     */
    @RequiresPermissions("task:labor:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectTaskLabor projectTaskLabor){
        startPage();
        List<ProjectTaskLabor> list = projectTaskLaborService.selectProjectTaskLaborList(projectTaskLabor);
        TableDataInfo dataTable = getDataTable(list);
        // 添加统计信息
        Map<String, Object> total = projectTaskLaborService.selectProjectTaskLaborTotal(projectTaskLabor);
        dataTable.setOtherData(total);
        return dataTable;
    }

    /**
     * 导出项目任务劳务费列表
     */
    @RequiresPermissions("task:labor:export")
    @Log(title = "项目任务劳务费", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectTaskLabor projectTaskLabor){
        List<ProjectTaskLabor> list = projectTaskLaborService.selectProjectTaskLaborList(projectTaskLabor);
        ExcelUtil<ProjectTaskLabor> util = new ExcelUtil<ProjectTaskLabor>(ProjectTaskLabor.class);
        return util.exportExcel(list, "项目任务劳务费数据");
    }
    
    /**
     * 下载导入模板
     */
    @RequiresPermissions("task:labor:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<ProjectTaskLabor> util = new ExcelUtil<ProjectTaskLabor>(ProjectTaskLabor.class);
        return util.importTemplateExcel("劳务费数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("task:labor:import")
    @Log(title = "项目任务劳务费", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport, String projectTaskId) throws Exception
    {
        ExcelUtil<ProjectTaskLabor> util = new ExcelUtil<ProjectTaskLabor>(ProjectTaskLabor.class);
        List<ProjectTaskLabor> laborList = util.importExcel(file.getInputStream());
        String message = importLabor(laborList, updateSupport, projectTaskId);
        return AjaxResult.success(message);
    }
    
    /**
     * 导入劳务费数据
     * 
     * @param laborList 劳务费数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param projectTaskId 项目任务ID
     * @return 结果
     */
    public String importLabor(List<ProjectTaskLabor> laborList, Boolean isUpdateSupport, String projectTaskId)
    {
        if (StringUtils.isNull(laborList) || laborList.size() == 0)
        {
            throw new ServiceException("导入劳务费数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProjectTaskLabor labor : laborList)
        {
            try
            {
                // 设置项目任务ID
                labor.setProjectTaskId(projectTaskId);

                labor.setId(RandomStringUtil.gen18UUID());
                projectTaskLaborService.insertProjectTaskLabor(labor);
                successNum++;
                successMsg.append("<br/>" + successNum + "、劳务费 " + labor.getName() + " 导入成功");

                /* 验证是否存在这条劳务费记录
                ProjectTaskLabor queryLabor = new ProjectTaskLabor();
                queryLabor.setProjectTaskId(projectTaskId);
                queryLabor.setName(labor.getName());
                queryLabor.setContactPhone(labor.getContactPhone());
                List<ProjectTaskLabor> existLaborList = projectTaskLaborService.selectProjectTaskLaborList(queryLabor);
                
                if (existLaborList == null || existLaborList.isEmpty())
                {
                    labor.setId(RandomStringUtil.gen18UUID());
                    projectTaskLaborService.insertProjectTaskLabor(labor);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、劳务费 " + labor.getName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    ProjectTaskLabor existLabor = existLaborList.get(0);
                    labor.setId(existLabor.getId());
                    projectTaskLaborService.updateProjectTaskLabor(labor);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、劳务费 " + labor.getName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、劳务费 " + labor.getName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、劳务费 " + labor.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增项目任务劳务费
     */
    @GetMapping("/add")
    public String add(String projectTaskId,ModelMap mmap){
        mmap.put("projectTaskId", projectTaskId);
        return prefix + "/add";
    }

    /**
     * 新增保存项目任务劳务费
     */
    @RequiresPermissions("task:labor:add")
    @Log(title = "项目任务劳务费", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectTaskLabor projectTaskLabor){
        projectTaskLabor.setId(RandomStringUtil.gen18UUID());
        return toAjax(projectTaskLaborService.insertProjectTaskLabor(projectTaskLabor));
    }

    /**
     * 修改项目任务劳务费
     */
    @RequiresPermissions("task:labor:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskLabor projectTaskLabor = projectTaskLaborService.selectProjectTaskLaborById(id);
        mmap.put("projectTaskLabor", projectTaskLabor);
        return prefix + "/edit";
    }

    /**
     * 修改保存项目任务劳务费
     */
    @RequiresPermissions("task:labor:edit")
    @Log(title = "项目任务劳务费", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectTaskLabor projectTaskLabor){
        return toAjax(projectTaskLaborService.updateProjectTaskLabor(projectTaskLabor));
    }

    /**
     * 删除项目任务劳务费
     */
    @RequiresPermissions("task:labor:remove")
    @Log(title = "项目任务劳务费", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(projectTaskLaborService.deleteProjectTaskLaborByIds(ids));
    }

    /**
     * 付款页面
     */
    @RequiresPermissions("task:labor:pay")
    @GetMapping("/pay")
    public String pay(String id, ModelMap mmap){
        ProjectTaskLabor projectTaskLabor = projectTaskLaborService.selectProjectTaskLaborById(id);
        mmap.put("projectTaskLabor", projectTaskLabor);
        return prefix + "/pay";
    }

    /**
     * 付款保存
     */
    @RequiresPermissions("task:labor:pay")
    @Log(title = "项目任务劳务费", businessType = BusinessType.UPDATE)
    @PostMapping("/pay")
    @ResponseBody
    public AjaxResult paySave(ProjectTaskLabor projectTaskLabor){
        return toAjax(projectTaskLaborService.updateProjectTaskLaborPayment(projectTaskLabor));
    }
}
