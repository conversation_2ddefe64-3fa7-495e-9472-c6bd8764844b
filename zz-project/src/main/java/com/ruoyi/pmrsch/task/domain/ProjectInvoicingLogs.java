package com.ruoyi.pmrsch.task.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 开票记录对象 zz_project_invoicing_logs
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectInvoicingLogs extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    @Excel(name = "项目任务 ID")
    private String projectTaskId;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    private String involveTitle;

    /** 开票金额 */
    @Excel(name = "开票金额")
    private Double amount;

    /** 开票时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "开票时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date invoicesTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String projectNo;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    // 兼容旧代码的方法
    public void setProjectInfoId(String projectInfoId) {
        this.projectTaskId = projectInfoId;
    }

    public String getProjectInfoId() {
        return projectTaskId;
    }

    public void setInvolveTitle(String involveTitle) {
        this.involveTitle = involveTitle;
    }

    public String getInvolveTitle() {
        return involveTitle;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getAmount() {
        return amount;
    }

    public void setInvoicesTime(Date invoicesTime) {
        this.invoicesTime = invoicesTime;
    }

    public Date getInvoicesTime() {
        return invoicesTime;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("involveTitle", getInvolveTitle())
            .append("amount", getAmount())
            .append("invoicesTime", getInvoicesTime())
            .append("remarks", getRemarks())
            .append("projectNo", getProjectNo())
            .append("projectName", getProjectName())
            .append("taskName", getTaskName())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
