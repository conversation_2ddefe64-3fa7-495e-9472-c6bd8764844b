package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.SupervisorStatInfo;
import com.ruoyi.pmrsch.task.entity.TaskCountInfo;

import java.util.List;

/**
 * 项目阶段任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface StatisticalMapper {
    List<TaskCountInfo> getTaskCountInfos(TaskCountInfo taskCountInfo);
    
    /**
     * 获取督导统计信息
     * 
     * @param supervisorStatInfo 督导统计信息查询参数
     * @return 督导统计信息列表
     */
    List<SupervisorStatInfo> getSupervisorStatInfos(SupervisorStatInfo supervisorStatInfo);
}
