package com.ruoyi.pmrsch.task.budget.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 项目任务预算详情对象 zz_project_task_budget_detail
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectTaskBudgetDetail extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    @Excel(name = "项目任务 ID")
    private String projectTaskId;

    /** 项目任务预算 ID */
    @Excel(name = "项目任务预算 ID")
    private String projectTaskBudgetId;

    /** 费用主类ID */
    @Excel(name = "费用主类ID")
    private String costCategorieMainCode;

    /** 费用子类ID */
    @Excel(name = "费用子类ID")
    private String costCategorieSubCode;

    /** 预算单价 */
    @Excel(name = "预算单价")
    private Double priceBudget;

    /** 预算数量 */
    @Excel(name = "预算数量")
    private Double quantityBudget;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    public Double getSubtotal(){
        if(quantityBudget == null || priceBudget == null){
            return 0.0;
        }

        return quantityBudget * priceBudget;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setProjectTaskBudgetId(String projectTaskBudgetId) {
        this.projectTaskBudgetId = projectTaskBudgetId;
    }

    public String getProjectTaskBudgetId() {
        return projectTaskBudgetId;
    }

    public void setCostCategorieMainCode(String costCategorieMainCode) {
        this.costCategorieMainCode = costCategorieMainCode;
    }

    public String getCostCategorieMainCode() {
        return costCategorieMainCode;
    }

    public void setCostCategorieSubCode(String costCategorieSubCode) {
        this.costCategorieSubCode = costCategorieSubCode;
    }

    public String getCostCategorieSubCode() {
        return costCategorieSubCode;
    }

    public void setPriceBudget(Double priceBudget) {
        this.priceBudget = priceBudget;
    }

    public Double getPriceBudget() {
        return priceBudget;
    }

    public void setQuantityBudget(Double quantityBudget) {
        this.quantityBudget = quantityBudget;
    }

    public Double getQuantityBudget() {
        return quantityBudget;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("projectTaskBudgetId", getProjectTaskBudgetId())
            .append("costCategorieMainCode", getCostCategorieMainCode())
            .append("costCategorieSubCode", getCostCategorieSubCode())
            .append("priceBudget", getPriceBudget())
            .append("quantityBudget", getQuantityBudget())
            .append("createTime", getCreateTime())
            .append("remarks", getRemarks())
            .toString();
    }
}
