package com.ruoyi.pmrsch.task.budget.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskBudgetDetail;
import com.ruoyi.pmrsch.task.budget.service.IProjectTaskBudgetDetailService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目任务预算详情Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/budget/budgetdetail")
public class BudgetDetailController extends BaseController{
    private String prefix = "budget/budgetdetail";

    @Autowired
    private IProjectTaskBudgetDetailService projectTaskBudgetDetailService;

    @RequiresPermissions("budget:budgetdetail:view")
    @GetMapping()
    public String budgetdetail(){
        return prefix + "/budgetdetail";
    }

    /**
     * 查询项目任务预算详情列表
     */
    @RequiresPermissions("budget:budgetdetail:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        startPage();
        List<ProjectTaskBudgetDetail> list = projectTaskBudgetDetailService.selectProjectTaskBudgetDetailList(projectTaskBudgetDetail);
        return getDataTable(list);
    }

    /**
     * 导出项目任务预算详情列表
     */
    @RequiresPermissions("budget:budgetdetail:export")
    @Log(title = "项目任务预算详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        List<ProjectTaskBudgetDetail> list = projectTaskBudgetDetailService.selectProjectTaskBudgetDetailList(projectTaskBudgetDetail);
        ExcelUtil<ProjectTaskBudgetDetail> util = new ExcelUtil<ProjectTaskBudgetDetail>(ProjectTaskBudgetDetail.class);
        return util.exportExcel(list, "项目任务预算详情数据");
    }

    /**
     * 新增项目任务预算详情
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/add";
    }

    /**
     * 新增保存项目任务预算详情
     */
    @RequiresPermissions("budget:budgetdetail:add")
    @Log(title = "项目任务预算详情", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        return toAjax(projectTaskBudgetDetailService.insertProjectTaskBudgetDetail(projectTaskBudgetDetail));
    }

    /**
     * 修改项目任务预算详情
     */
    @RequiresPermissions("budget:budgetdetail:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskBudgetDetail projectTaskBudgetDetail = projectTaskBudgetDetailService.selectProjectTaskBudgetDetailById(id);
        mmap.put("projectTaskBudgetDetail", projectTaskBudgetDetail);
        return prefix + "/edit";
    }

    /**
     * 修改保存项目任务预算详情
     */
    @RequiresPermissions("budget:budgetdetail:edit")
    @Log(title = "项目任务预算详情", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        return toAjax(projectTaskBudgetDetailService.updateProjectTaskBudgetDetail(projectTaskBudgetDetail));
    }

    /**
     * 删除项目任务预算详情
     */
    @RequiresPermissions("budget:budgetdetail:remove")
    @Log(title = "项目任务预算详情", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(projectTaskBudgetDetailService.deleteProjectTaskBudgetDetailByIds(ids));
    }
}
