package com.ruoyi.pmrsch.task.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 审批记录对象 zz_approval_logs
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ApprovalLogs extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 审核类型 */
    @Excel(name = "审核类型")
    private Integer businessType;

    /** 业务 ID */
    @Excel(name = "业务 ID")
    private String businessId;

    /** 审核人 */
    @Excel(name = "审核人")
    private String reviewer;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reviewTime;

    /** 是否通过 */
    @Excel(name = "是否通过")
    private Integer ifApproved;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setIfApproved(Integer ifApproved) {
        this.ifApproved = ifApproved;
    }

    public Integer getIfApproved() {
        return ifApproved;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessType", getBusinessType())
            .append("businessId", getBusinessId())
            .append("reviewer", getReviewer())
            .append("reviewTime", getReviewTime())
            .append("ifApproved", getIfApproved())
            .append("remarks", getRemarks())
            .toString();
    }
}
