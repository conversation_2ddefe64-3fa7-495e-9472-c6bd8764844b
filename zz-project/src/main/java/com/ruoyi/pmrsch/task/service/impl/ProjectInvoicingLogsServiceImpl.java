package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs;
import com.ruoyi.pmrsch.task.mapper.ProjectInvoicingLogsMapper;
import com.ruoyi.pmrsch.task.service.IProjectInvoicingLogsService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 开票记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectInvoicingLogsServiceImpl implements IProjectInvoicingLogsService {
    @Autowired
    private ProjectInvoicingLogsMapper projectInvoicingLogsMapper;

    /**
     * 查询开票记录
     * 
     * @param id 开票记录主键
     * @return 开票记录
     */
    @Override
    public ProjectInvoicingLogs selectProjectInvoicingLogsById(String id){
        return projectInvoicingLogsMapper.selectProjectInvoicingLogsById(id);
    }

    /**
     * 查询开票记录列表
     * 
     * @param projectInvoicingLogs 开票记录
     * @return 开票记录
     */
    @Override
    public List<ProjectInvoicingLogs> selectProjectInvoicingLogsList(ProjectInvoicingLogs projectInvoicingLogs){
        return projectInvoicingLogsMapper.selectProjectInvoicingLogsList(projectInvoicingLogs);
    }

    /**
     * 新增开票记录
     * 
     * @param projectInvoicingLogs 开票记录
     * @return 结果
     */
    @Override
    public int insertProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs){
        projectInvoicingLogs.setId(RandomStringUtil.gen18UUID());
        projectInvoicingLogs.setCreatedAt(new Date());
        
        return projectInvoicingLogsMapper.insertProjectInvoicingLogs(projectInvoicingLogs);
    }

    /**
     * 修改开票记录
     * 
     * @param projectInvoicingLogs 开票记录
     * @return 结果
     */
    @Override
    public int updateProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs){
        return projectInvoicingLogsMapper.updateProjectInvoicingLogs(projectInvoicingLogs);
    }

    /**
     * 批量删除开票记录
     * 
     * @param ids 需要删除的开票记录主键
     * @return 结果
     */
    @Override
    public int deleteProjectInvoicingLogsByIds(String ids){
        return projectInvoicingLogsMapper.deleteProjectInvoicingLogsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除开票记录信息
     * 
     * @param id 开票记录主键
     * @return 结果
     */
    @Override
    public int deleteProjectInvoicingLogsById(String id){
        return projectInvoicingLogsMapper.deleteProjectInvoicingLogsById(id);
    }
}
