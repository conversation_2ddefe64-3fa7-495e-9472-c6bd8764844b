package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.RequiresAnyPermissions;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;
import com.ruoyi.pmrsch.task.budget.service.IProjectTaskLoansService;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.service.impl.ProjectTaskBudgetServiceImpl;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 项目任务借款Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/loans")
public class ProcessLoansController extends BaseController{
    private String prefix = "task/loans";

    @Autowired
    private IProjectTaskLoansService projectTaskLoansService;
    @Autowired
    private ProjectTaskBudgetServiceImpl taskBudgetService;

    /**
     * 申请列表页
     * @return
     */
    @RequiresPermissions("task:loans:apply:view")
    @GetMapping("apply")
    public String appyList(String projectTaskBudgetId,ModelMap mmap){
        ProjectTaskBudget taskBudget = taskBudgetService.selectProjectTaskBudgetById(projectTaskBudgetId);
        mmap.put("taskBudget", taskBudget);
        mmap.put("review",ReviewStatus.提审.getValue());
        return prefix + "/apply/loans";
    }

    /**
     * 申请列表
     */
    @RequiresPermissions("task:loans:apply:list")
    @PostMapping("apply")
    @ResponseBody
    public TableDataInfo appyList(ProjectTaskLoans projectTaskLoans){
        projectTaskLoans.setApplicant(ShiroUtils.getUserId()+"");
        startPage();
        List<ProjectTaskLoans> list = projectTaskLoansService.selectProjectTaskLoansList(projectTaskLoans);
        return getDataTable(list);
    }

    /**
     * 新增申请页
     */
    @GetMapping("apply/add")
    public String applyAdd(String projectTaskBudgetId,ModelMap mmap){
        ProjectTaskBudget taskBudget = taskBudgetService.selectProjectTaskBudgetById(projectTaskBudgetId);
        mmap.put("taskBudget", taskBudget);
        return prefix + "/apply/add";
    }

    /**
     * 新增申请
     */
    @RequiresPermissions("task:loans:apply:add")
    @Log(title = "项目阶段任务", businessType = BusinessType.INSERT)
    @PostMapping("apply/add")
    @ResponseBody
    public AjaxResult applyAdd(ProjectTaskLoans projectTaskLoans){
        return toAjax(projectTaskLoansService.applyAdd(projectTaskLoans));
    }

    /**
     * 修改申请页
     */
    @RequiresPermissions("task:loans:apply:edit")
    @GetMapping("apply/edit/{id}")
    public String appyEdit(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskLoans projectTaskLoans = projectTaskLoansService.selectProjectTaskLoansById(id);
        mmap.put("projectTaskLoans", projectTaskLoans);
        return prefix + "/apply/edit";
    }

    /**
     * 修改申请
     */
    @RequiresPermissions("task:loans:apply:edit")
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("apply/edit")
    @ResponseBody
    public AjaxResult applyEdit(ProjectTaskLoans projectTaskLoans){
        return toAjax(projectTaskLoansService.applyEdit(projectTaskLoans));
    }

    /**
     * 删除申请
     */
    @RequiresPermissions("task:loans:apply:remove")
    @Log(title = "项目阶段任务", businessType = BusinessType.DELETE)
    @PostMapping( "apply/remove")
    @ResponseBody
    public AjaxResult appyRemove(String ids){
        return toAjax(projectTaskLoansService.deleteProjectTaskLoansByIds(ids));
    }


    /**
     * 导出申请列表
     */
    @RequiresPermissions("task:loans:apply:export")
    @Log(title = "项目阶段任务", businessType = BusinessType.EXPORT)
    @PostMapping("apply/export")
    @ResponseBody
    public AjaxResult applyExport(ProjectTaskLoans projectTaskLoans){
        List<ProjectTaskLoans> list = projectTaskLoansService.selectProjectTaskLoansList(projectTaskLoans);
        ExcelUtil<ProjectTaskLoans> util = new ExcelUtil<ProjectTaskLoans>(ProjectTaskLoans.class);
        return util.exportExcel(list, "项目阶段任务数据");
    }

    /**
     * 申请审批(主管)列表页
     * @return
     */
    @RequiresPermissions("task:loans:review:manager")
    @GetMapping("review/manager")
    public String reviewManagerList(ModelMap mmap){
        mmap.put("review",ReviewStatus.主管审核.getValue());
        mmap.put("filterStatus", ReviewStatus.提审.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 申请审批(财务)列表页
     * @return
     */
    @RequiresPermissions("task:loans:review:finance")
    @GetMapping("review/finance")
    public String reviewFinanceList(ModelMap mmap){
        mmap.put("review",ReviewStatus.财务审核.getValue());
        mmap.put("filterStatus",ReviewStatus.主管审核.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 申请审批(老板)列表页
     * @return
     */
    @RequiresPermissions("task:loans:review:ceo")
    @GetMapping("review/ceo")
    public String reviewCeoList(ModelMap mmap){
        mmap.put("review",ReviewStatus.老板审核.getValue());
        mmap.put("filterStatus",ReviewStatus.财务审核.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 审批列表数据
     */
    @RequiresAnyPermissions({"task:loans:review:manager","task:loans:review:finance","task:loans:review:ceo"})
    @PostMapping("review")
    @ResponseBody
    public TableDataInfo reviewList(ProjectTaskLoans projectTaskLoans){
        startPage();
        List<ProjectTaskLoans> list = projectTaskLoansService.selectProjectTaskLoansList(projectTaskLoans);
        return getDataTable(list);
    }

    /**
     * 审批操作
     */
    @RequiresAnyPermissions({"task:loans:review:manager","task:loans:review:finance","task:loans:review:ceo"})
    @GetMapping("{id}/review")
    public String review(@PathVariable("id") String id,@RequestParam String review,ModelMap mmap){
        ProjectTaskLoans projectTaskLoans = projectTaskLoansService.selectProjectTaskLoansById(id);
        mmap.put("projectTaskLoans", projectTaskLoans);
        mmap.put("review",review);
        return prefix + "/review/review";
    }

    /**
     * 审批操作
     */
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("{id}/review")
    @ResponseBody
    public AjaxResult review(@PathVariable("id") String id,Integer review,String remark){
        Subject subject = ShiroUtils.getSubject();

        ReviewStatus reviewStatus = ReviewStatus.getByValue(review);
        boolean hasPrivilege = false;
        switch (reviewStatus){
            case 提审:
                hasPrivilege = subject.isPermitted("task:loans:apply:edit");
                break;
            case 主管审核:
                hasPrivilege = subject.isPermitted("task:loans:review:manager");
                break;
            case 财务审核:
                hasPrivilege = subject.isPermitted("task:loans:review:finance");
                break;
            case 老板审核:
                hasPrivilege = subject.isPermitted("task:loans:review:ceo");
                break;
            case 取消:
                hasPrivilege = subject.isPermitted("task:loans:apply:edit");
                break;
            case 拒绝:
                hasPrivilege = checkRejectPrivilege(subject,id);
                break;
        }

        if(!hasPrivilege){
            throw new ApiException("没有操作权限",ApiException.AUTH_DENIED);
        }

        return toAjax(projectTaskLoansService.review(reviewStatus,id,ShiroUtils.getUserId()+"",remark));
    }

    private boolean checkRejectPrivilege(Subject subject, String loansId) {
        ProjectTaskLoans projectTaskLoans = ParamUtil.requireNotNull(projectTaskLoansService.selectProjectTaskLoansById(loansId),"找不到预算信息.loansId:"+loansId);
        ReviewStatus curStatus = ReviewStatus.getByValue(Optional.ofNullable(projectTaskLoans.getLoansStatusCode()).orElse(ReviewStatus.待提审.getValue()));
        switch (curStatus){
            case 提审:
                return subject.isPermitted("task:loans:review:manager");
            case 主管审核:
                return subject.isPermitted("task:loans:review:finance");
            case 财务审核:
                return subject.isPermitted("task:loans:review:ceo");
            default:
                throw new IllegalStateException("Unexpected value: " + curStatus);
        }
    }
    
    /**
     * 待打款列表页
     * @return
     */
    @RequiresPermissions("task:loans:payment:view")
    @GetMapping("payment")
    public String paymentList(ModelMap mmap){
        return prefix + "/payment/paymentList";
    }
    
    /**
     * 待打款列表数据
     */
    @RequiresPermissions("task:loans:payment:list")
    @PostMapping("payment")
    @ResponseBody
    public TableDataInfo paymentList(ProjectTaskLoans projectTaskLoans){
        // 设置查询条件：老板已审核通过且未打款
        projectTaskLoans.setLoansStatusCode(ReviewStatus.老板审核.getValue());
        projectTaskLoans.setIsPayment(false);
        startPage();
        List<ProjectTaskLoans> list = projectTaskLoansService.selectProjectTaskLoansList(projectTaskLoans);
        return getDataTable(list);
    }
    
    /**
     * 打款操作页面
     */
    @RequiresPermissions("task:loans:payment:edit")
    @GetMapping("{id}/payment")
    public String payment(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskLoans projectTaskLoans = projectTaskLoansService.selectProjectTaskLoansById(id);
        mmap.put("projectTaskLoans", projectTaskLoans);
        return prefix + "/payment/payment";
    }
    
    /**
     * 执行打款操作
     */
    @RequiresPermissions("task:loans:payment:edit")
    @Log(title = "借款打款", businessType = BusinessType.UPDATE)
    @PostMapping("{id}/payment")
    @ResponseBody
    public AjaxResult makePayment(@PathVariable("id") String id, Date paymentDate, String paymentRemarks){
        ProjectTaskLoans loans = new ProjectTaskLoans();
        loans.setId(id);
        loans.setIsPayment(true);
        loans.setPaymentDate(paymentDate);
        loans.setPaymentRemarks(paymentRemarks);
        
        return toAjax(projectTaskLoansService.applyEdit(loans));
    }
}
