package com.ruoyi.pmrsch.task.service;

import java.util.List;

import com.ruoyi.pmrsch.task.domain.ProjectPayCollectionLogs;

/**
 * 收款记录 服务层
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPaymentService {
    /**
     * 查询收款记录信息
     * 
     * @param id 收款记录ID
     * @return 收款记录信息
     */
    ProjectPayCollectionLogs selectProjectPayCollectionLogsById(String id);

    /**
     * 查询收款记录列表
     * 
     * @param projectPayCollectionLogs 收款记录信息
     * @return 收款记录集合
     */
    List<ProjectPayCollectionLogs> selectProjectPayCollectionLogsList(ProjectPayCollectionLogs projectPayCollectionLogs);

    /**
     * 新增收款记录
     * 
     * @param projectPayCollectionLogs 收款记录信息
     * @return 结果
     */
    int insertProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs);

    /**
     * 修改收款记录
     * 
     * @param projectPayCollectionLogs 收款记录信息
     * @return 结果
     */
    int updateProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs);

    /**
     * 删除收款记录信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteProjectPayCollectionLogsByIds(String ids);
} 