package com.ruoyi.pmrsch.task.manager;/**
 * <AUTHOR>
 * @Date 2025/2/11
 */

import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.task.domain.ApprovalLogs;
import com.ruoyi.pmrsch.task.entity.ApplyStatus;
import com.ruoyi.pmrsch.task.entity.ReviewBType;
import com.ruoyi.pmrsch.task.mapper.ApprovalLogsMapper;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 描述
 * @className ReviewLogsManager
 * <AUTHOR>
 * @date 2025年02月11日 20:56
 */
@Component
public class ReviewLogsManager {
    @Autowired
    private ApprovalLogsMapper approvalLogsMapper;

    public void inserLog(ReviewBType reviewBType,String businessId, String reviewer, Integer reviewStatus,String remark) {
        ApprovalLogs logs = new ApprovalLogs();
        logs.setId(RandomStringUtil.gen18UUID());
        logs.setBusinessType(reviewBType.getValue());
        logs.setBusinessId(businessId);
        logs.setReviewer(reviewer);
        logs.setReviewTime(TimeUtil.getCurrentDate());

        boolean ifApproved = !Arrays.asList(ApplyStatus.取消,ApplyStatus.拒绝).contains(reviewStatus);
        logs.setIfApproved(BooleanUtils.toInteger(ifApproved));
        logs.setRemarks(remark);

        approvalLogsMapper.insertApprovalLogs(logs);
    }

    public void delByBizId(ReviewBType reviewBType, String businessId) {
        approvalLogsMapper.deleteApprovalLogsByBusinessTypeAndId(reviewBType.getValue(), businessId);
    }
}
