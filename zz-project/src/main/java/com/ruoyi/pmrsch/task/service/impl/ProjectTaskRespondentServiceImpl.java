package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.domain.ProjectTaskRespondent;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskRespondentMapper;
import com.ruoyi.pmrsch.task.mapper.ProjectTasksMapper;
import com.ruoyi.pmrsch.task.service.IProjectTaskRespondentService;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目任务受访者Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskRespondentServiceImpl implements IProjectTaskRespondentService {
    @Autowired
    private ProjectTaskRespondentMapper projectTaskRespondentMapper;
    
    @Autowired
    private ProjectTasksMapper projectTasksMapper;
    
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询项目任务受访者
     * 
     * @param id 项目任务受访者主键
     * @return 项目任务受访者
     */
    @Override
    public ProjectTaskRespondent selectProjectTaskRespondentById(String id){
        ProjectTaskRespondent respondent = projectTaskRespondentMapper.selectProjectTaskRespondentById(id);
        if (respondent != null) {
            supplementInfo(respondent);
        }
        return respondent;
    }

    /**
     * 查询项目任务受访者列表
     * 
     * @param projectTaskRespondent 项目任务受访者
     * @return 项目任务受访者
     */
    @Override
    public List<ProjectTaskRespondent> selectProjectTaskRespondentList(ProjectTaskRespondent projectTaskRespondent){
        List<ProjectTaskRespondent> list = projectTaskRespondentMapper.selectProjectTaskRespondentList(projectTaskRespondent);
        for (ProjectTaskRespondent respondent : list) {
            supplementInfo(respondent);
        }
        return list;
    }

    /**
     * 补充项目任务受访者关联信息
     * 
     * @param respondent 项目任务受访者
     */
    private void supplementInfo(ProjectTaskRespondent respondent) {
        if (respondent == null || StringUtils.isEmpty(respondent.getProjectTaskId())) {
            return;
        }
        
        // 查询任务信息
        ProjectTasks task = projectTasksMapper.selectProjectTasksById(respondent.getProjectTaskId());
        if (task != null) {
            respondent.setProjectTask(task);
            
            // 查询项目信息
            if (StringUtils.isNotEmpty(task.getProjectInfoId())) {
                ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(task.getProjectInfoId());
                respondent.setProjectInfo(projectInfo);
            }
            
            // 查询督导信息
            if (task.getUserId() != null) {
                SysUser supervisor = sysUserMapper.selectUserById(task.getUserId());
                respondent.setSupervisor(supervisor);
            }
        }
    }

    /**
     * 新增项目任务受访者
     * 
     * @param projectTaskRespondent 项目任务受访者
     * @return 结果
     */
    @Override
    public int insertProjectTaskRespondent(ProjectTaskRespondent projectTaskRespondent){
        return projectTaskRespondentMapper.insertProjectTaskRespondent(projectTaskRespondent);
    }

    /**
     * 修改项目任务受访者
     * 
     * @param projectTaskRespondent 项目任务受访者
     * @return 结果
     */
    @Override
    public int updateProjectTaskRespondent(ProjectTaskRespondent projectTaskRespondent){
        return projectTaskRespondentMapper.updateProjectTaskRespondent(projectTaskRespondent);
    }

    /**
     * 批量删除项目任务受访者
     * 
     * @param ids 需要删除的项目任务受访者主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskRespondentByIds(String ids){
        return projectTaskRespondentMapper.deleteProjectTaskRespondentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务受访者信息
     * 
     * @param id 项目任务受访者主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskRespondentById(String id){
        return projectTaskRespondentMapper.deleteProjectTaskRespondentById(id);
    }

    public void delByTaskId(String taskId) {
        ProjectTaskRespondent query = new ProjectTaskRespondent();
        query.setProjectTaskId(taskId);
        List<ProjectTaskRespondent> respondents = projectTaskRespondentMapper.selectProjectTaskRespondentList(query);
        
        if (respondents != null && !respondents.isEmpty()) {
            List<String> ids = respondents.stream().map(ProjectTaskRespondent::getId).collect(Collectors.toList());
            String idsStr = String.join(",", ids);
            projectTaskRespondentMapper.deleteProjectTaskRespondentByIds(Convert.toStrArray(idsStr));
        }
    }
}
