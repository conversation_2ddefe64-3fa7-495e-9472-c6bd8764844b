package com.ruoyi.pmrsch.task.budget.mapper;

import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;

import java.util.List;

/**
 * 项目任务借款Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskLoansMapper {
    /**
     * 查询项目任务借款
     * 
     * @param id 项目任务借款主键
     * @return 项目任务借款
     */
    public ProjectTaskLoans selectProjectTaskLoansById(String id);

    /**
     * 查询项目任务借款列表
     * 
     * @param projectTaskLoans 项目任务借款
     * @return 项目任务借款集合
     */
    public List<ProjectTaskLoans> selectProjectTaskLoansList(ProjectTaskLoans projectTaskLoans);

    /**
     * 新增项目任务借款
     * 
     * @param projectTaskLoans 项目任务借款
     * @return 结果
     */
    public int insertProjectTaskLoans(ProjectTaskLoans projectTaskLoans);

    /**
     * 修改项目任务借款
     * 
     * @param projectTaskLoans 项目任务借款
     * @return 结果
     */
    public int updateProjectTaskLoans(ProjectTaskLoans projectTaskLoans);

    /**
     * 删除项目任务借款
     * 
     * @param id 项目任务借款主键
     * @return 结果
     */
    public int deleteProjectTaskLoansById(String id);

    /**
     * 批量删除项目任务借款
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskLoansByIds(String[] ids);
}
