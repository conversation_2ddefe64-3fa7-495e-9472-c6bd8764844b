package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.RequiresAnyPermissions;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.service.IProjectTaskBudgetService;
import com.ruoyi.pmrsch.task.service.impl.ProjectTasksServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 项目任务预算Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/budget")
public class ProcessBudgetController extends BaseController{
    private String prefix = "task/budget";

    @Autowired
    private IProjectTaskBudgetService budgetService;
    @Autowired
    private ProjectTasksServiceImpl tasksService;

    /**
     * 申请列表页
     * @return
     */
    @RequiresPermissions("task:budget:apply:view")
    @GetMapping("apply")
    public String appyList(String projectTaskId,ModelMap mmap){
        ProjectTasks projectTasks = tasksService.selectProjectTasksById(projectTaskId);
        mmap.put("projectTasks", projectTasks);
        mmap.put("review",ReviewStatus.提审.getValue());
        return prefix + "/apply/budget";
    }

    /**
     * 申请列表
     */
    @RequiresPermissions("task:budget:apply:list")
    @PostMapping("apply")
    @ResponseBody
    public TableDataInfo appyList(ProjectTaskBudget projectTaskBudget){
        projectTaskBudget.setApplicant(ShiroUtils.getUserId()+"");
        startPage();
        List<ProjectTaskBudget> list = budgetService.selectProjectTaskBudgetList(projectTaskBudget);
        return getDataTable(list);
    }

    /**
     * 新增申请页
     */
    @GetMapping("apply/add")
    public String applyAdd(String projectTaskId,ModelMap mmap){
        ProjectTasks projectTasks = tasksService.selectProjectTasksById(projectTaskId);
        mmap.put("projectTasks", projectTasks);
        return prefix + "/apply/add";
    }

    /**
     * 新增申请
     */
    @RequiresPermissions("task:budget:apply:add")
    @Log(title = "项目阶段任务", businessType = BusinessType.INSERT)
    @PostMapping("apply/add")
    @ResponseBody
    public AjaxResult applyAdd(ProjectTaskBudget projectTaskBudget){
        return toAjax(budgetService.apply(projectTaskBudget));
    }

    /**
     * 修改申请页
     */
    @RequiresPermissions("task:budget:apply:edit")
    @GetMapping("apply/edit/{id}")
    public String appyEdit(@PathVariable("id") String id,String fragment, ModelMap mmap){
        ProjectTaskBudget projectTaskBudget = budgetService.selectProjectTaskBudgetById(id);
        mmap.put("projectTaskBudget", projectTaskBudget);
        return StringUtils.isBlank(fragment) ? prefix + "/apply/edit" : "fragment/project::"+fragment;
    }

    /**
     * 修改申请
     */
    @RequiresPermissions("task:budget:apply:edit")
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("apply/edit")
    @ResponseBody
    public AjaxResult applyEdit(ProjectTaskBudget projectTaskBudget){
        return toAjax(budgetService.applyEdit(projectTaskBudget));
    }

    /**
     * 删除申请
     */
    @RequiresPermissions("task:budget:apply:remove")
    @Log(title = "项目阶段任务", businessType = BusinessType.DELETE)
    @PostMapping( "apply/remove")
    @ResponseBody
    public AjaxResult appyRemove(String ids){
        return toAjax(budgetService.deleteProjectTaskBudgetByIds(ids));
    }


    /**
     * 导出申请列表
     */
    @RequiresPermissions("task:budget:apply:export")
    @Log(title = "项目阶段任务", businessType = BusinessType.EXPORT)
    @PostMapping("apply/export")
    @ResponseBody
    public AjaxResult applyExport(ProjectTaskBudget projectTaskBudget){
        List<ProjectTaskBudget> list = budgetService.selectProjectTaskBudgetList(projectTaskBudget);
        ExcelUtil<ProjectTaskBudget> util = new ExcelUtil<ProjectTaskBudget>(ProjectTaskBudget.class);
        return util.exportExcel(list, "项目阶段任务数据");
    }

    /**
     * 申请审批(主管)列表页
     * @return
     */
    @RequiresPermissions("task:budget:review:manager")
    @GetMapping("review/manager")
    public String reviewList(ModelMap mmap){
        mmap.put("review",ReviewStatus.主管审核.getValue());
        mmap.put("filterStatus",ReviewStatus.提审.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 申请审批(财务)列表页
     * @return
     */
    @RequiresPermissions("task:budget:review:finance")
    @GetMapping("review/finance")
    public String reviewFinanceList(ModelMap mmap){
        mmap.put("review",ReviewStatus.财务审核.getValue());
        mmap.put("filterStatus",ReviewStatus.主管审核.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 申请审批(老板)列表页
     * @return
     */
    @RequiresPermissions("task:budget:review:ceo")
    @GetMapping("review/ceo")
    public String reviewCeoList(ModelMap mmap){
        mmap.put("review",ReviewStatus.老板审核.getValue());
        mmap.put("filterStatus",ReviewStatus.财务审核.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 审批列表数据
     */
    @RequiresAnyPermissions({"task:budget:review:manager","task:budget:review:finance","task:budget:review:ceo"})
    @PostMapping("review")
    @ResponseBody
    public TableDataInfo reviewList(ProjectTaskBudget projectTaskBudget){
        startPage();
        List<ProjectTaskBudget> list = budgetService.selectProjectTaskBudgetList(projectTaskBudget);
        return getDataTable(list);
    }

    /**
     * 审批操作
     */
    @RequiresAnyPermissions({"task:budget:review:manager","task:budget:review:finance","task:budget:review:ceo"})
    @GetMapping("{id}/review")
    public String review(@PathVariable("id") String id,@RequestParam String review, ModelMap mmap){
        ProjectTaskBudget taskBudget = budgetService.selectProjectTaskBudgetById(id);
        mmap.put("projectTaskBudget", taskBudget);
        mmap.put("review",review);
        return prefix + "/review/review";
    }

    /**
     * 审批操作
     */
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("{id}/review")
    @ResponseBody
    public AjaxResult review(@PathVariable("id") String id,Integer review,String remark){
        Subject subject = ShiroUtils.getSubject();

        ReviewStatus reviewStatus = ReviewStatus.getByValue(review);
        boolean hasPrivilege = false;
        switch (reviewStatus){
            case 提审:
                hasPrivilege = subject.isPermitted("task:budget:apply:edit");
                break;
            case 主管审核:
                hasPrivilege = subject.isPermitted("task:budget:review:manager");
                break;
            case 财务审核:
                hasPrivilege = subject.isPermitted("task:budget:review:finance");
                break;
            case 老板审核:
                hasPrivilege = subject.isPermitted("task:budget:review:ceo");
                break;
            case 取消:
                hasPrivilege = subject.isPermitted("task:budget:apply:edit");
                break;
            case 拒绝:
                hasPrivilege = checkRejectPrivilege(subject,id);
                break;
        }

        if(!hasPrivilege){
            throw new ApiException("没有操作权限",ApiException.AUTH_DENIED);
        }

        return toAjax(budgetService.review(reviewStatus,id,ShiroUtils.getUserId()+"",remark));
    }

    private boolean checkRejectPrivilege(Subject subject, String budgetId) {
        ProjectTaskBudget taskBudget = ParamUtil.requireNotNull(budgetService.selectProjectTaskBudgetById(budgetId),"找不到预算信息.budgetId:"+budgetId);
        ReviewStatus curStatus = ReviewStatus.getByValue(Optional.ofNullable(taskBudget.getBudgetStatusCode()).orElse(ReviewStatus.待提审.getValue()));
        switch (curStatus){
            case 提审:
                return subject.isPermitted("task:budget:review:manager");
            case 主管审核:
                return subject.isPermitted("task:budget:review:finance");
            case 财务审核:
                return subject.isPermitted("task:budget:review:ceo");
            default:
                throw new IllegalStateException("Unexpected value: " + curStatus);
        }
    }
}
