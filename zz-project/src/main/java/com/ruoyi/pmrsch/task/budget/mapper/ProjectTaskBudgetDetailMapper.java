package com.ruoyi.pmrsch.task.budget.mapper;

import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskBudgetDetail;

import java.util.List;

/**
 * 项目任务预算详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskBudgetDetailMapper {
    /**
     * 查询项目任务预算详情
     * 
     * @param id 项目任务预算详情主键
     * @return 项目任务预算详情
     */
    public ProjectTaskBudgetDetail selectProjectTaskBudgetDetailById(String id);

    /**
     * 查询项目任务预算详情列表
     * 
     * @param projectTaskBudgetDetail 项目任务预算详情
     * @return 项目任务预算详情集合
     */
    public List<ProjectTaskBudgetDetail> selectProjectTaskBudgetDetailList(ProjectTaskBudgetDetail projectTaskBudgetDetail);

    /**
     * 新增项目任务预算详情
     * 
     * @param projectTaskBudgetDetail 项目任务预算详情
     * @return 结果
     */
    public int insertProjectTaskBudgetDetail(ProjectTaskBudgetDetail projectTaskBudgetDetail);

    /**
     * 修改项目任务预算详情
     * 
     * @param projectTaskBudgetDetail 项目任务预算详情
     * @return 结果
     */
    public int updateProjectTaskBudgetDetail(ProjectTaskBudgetDetail projectTaskBudgetDetail);

    /**
     * 删除项目任务预算详情
     * 
     * @param id 项目任务预算详情主键
     * @return 结果
     */
    public int deleteProjectTaskBudgetDetailById(String id);

    /**
     * 批量删除项目任务预算详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskBudgetDetailByIds(String[] ids);
}
