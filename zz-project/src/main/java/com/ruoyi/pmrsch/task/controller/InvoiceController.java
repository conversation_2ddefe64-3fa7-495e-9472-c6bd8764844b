package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs;
import com.ruoyi.pmrsch.task.service.IInvoiceService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 开票记录 Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/statistical/invoice")
public class InvoiceController extends BaseController {
    private String prefix = "task/statistical/invoice";

    @Autowired
    private IInvoiceService invoiceService;

    @RequiresPermissions("pmdata:invoice:view")
    @GetMapping()
    public String invoice(@RequestParam("taskId") String taskId, ModelMap mmap) {
        mmap.put("taskId", taskId);
        return prefix + "/invoice";
    }

    /**
     * 查询开票记录列表
     */
    @RequiresPermissions("pmdata:invoice:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectInvoicingLogs projectInvoicingLogs) {
        startPage();
        List<ProjectInvoicingLogs> list = invoiceService.selectProjectInvoicingLogsList(projectInvoicingLogs);
        return getDataTable(list);
    }

    /**
     * 导出开票记录列表
     */
    @RequiresPermissions("pmdata:invoice:export")
    @Log(title = "开票记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectInvoicingLogs projectInvoicingLogs) {
        List<ProjectInvoicingLogs> list = invoiceService.selectProjectInvoicingLogsList(projectInvoicingLogs);
        ExcelUtil<ProjectInvoicingLogs> util = new ExcelUtil<>(ProjectInvoicingLogs.class);
        return util.exportExcel(list, "开票记录数据");
    }

    /**
     * 新增开票记录
     */
    @GetMapping("/add")
    public String add(@RequestParam("taskId") String taskId, ModelMap mmap) {
        mmap.put("taskId", taskId);
        return prefix + "/add";
    }

    /**
     * 新增保存开票记录
     */
    @RequiresPermissions("pmdata:invoice:add")
    @Log(title = "开票记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectInvoicingLogs projectInvoicingLogs) {
        return toAjax(invoiceService.insertProjectInvoicingLogs(projectInvoicingLogs));
    }

    /**
     * 修改开票记录
     */
    @RequiresPermissions("pmdata:invoice:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        ProjectInvoicingLogs projectInvoicingLogs = invoiceService.selectProjectInvoicingLogsById(id);
        mmap.put("projectInvoicingLogs", projectInvoicingLogs);
        return prefix + "/edit";
    }

    /**
     * 修改保存开票记录
     */
    @RequiresPermissions("pmdata:invoice:edit")
    @Log(title = "开票记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectInvoicingLogs projectInvoicingLogs) {
        return toAjax(invoiceService.updateProjectInvoicingLogs(projectInvoicingLogs));
    }

    /**
     * 删除开票记录
     */
    @RequiresPermissions("pmdata:invoice:remove")
    @Log(title = "开票记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(invoiceService.deleteProjectInvoicingLogsByIds(ids));
    }
} 