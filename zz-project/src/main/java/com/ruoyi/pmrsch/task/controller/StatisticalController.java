package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.SupervisorStatInfo;
import com.ruoyi.pmrsch.task.entity.TaskCountInfo;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import com.ruoyi.pmrsch.task.service.StatisticalService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单Controller
 * <AUTHOR>
 * @version 2025-01-18
 */
@Controller
@RequestMapping(value = "/task/statistical")
public class StatisticalController extends BaseController {
	private String prefix = "task/statistical";

	@Autowired
	private StatisticalService statisticalService;
	
	@Autowired
	private IProjectTasksService projectTasksService;

	/**
	 * 项目统计查询
	 * @return
	 */
	@RequiresPermissions("task:statistical:task-count-infos")
	@GetMapping("task-count-infos")
	public String appyList(){
		return prefix + "/task-count-infos";
	}

	/**
	 * 项目统计查询
	 */
	@RequiresPermissions("task:statistical:task-count-infos")
	@PostMapping("task-count-infos")
	@ResponseBody
	public TableDataInfo projecInfos(TaskCountInfo taskCountInfo){
		startPage();
		List<TaskCountInfo> list = statisticalService.getTaskCountInfos(taskCountInfo);
		return getDataTable(list);
	}
	
	/**
	 * 导出项目统计信息
	 */
	@RequiresPermissions("task:statistical:task-count-infos:export")
	@Log(title = "项目统计信息", businessType = BusinessType.EXPORT)
	@PostMapping("/task-count-infos/export")
	@ResponseBody
	public AjaxResult export(TaskCountInfo taskCountInfo) {
		List<TaskCountInfo> list = statisticalService.getTaskCountInfos(taskCountInfo);
		ExcelUtil<TaskCountInfo> util = new ExcelUtil<TaskCountInfo>(TaskCountInfo.class);
		return util.exportExcel(list, "项目统计信息");
	}
	
	/**
	 * 项目详情页
	 */
	@RequiresPermissions("task:statistical:task-count-infos:detail")
	@GetMapping("/detail")
	public String detail(@RequestParam("taskId") String taskId, Model model) {
	    ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(taskId);
	    model.addAttribute("projectTasks", projectTasks);
	    model.addAttribute("review", 1); // 默认成功状态，仅用于查看
	    return "task/statistical/detail";
	}

	/**
     * 督导统计列表页
     */
    @RequiresPermissions("task:statistical:supervisor-stat")
    @GetMapping("/supervisor/stat")
    public String supervisorStat() {
        return prefix + "/supervisor-stat";
    }

    /**
     * 查询督导统计数据
     */
    @RequiresPermissions("task:statistical:supervisor-stat")
    @PostMapping("/supervisor/stat/list")
    @ResponseBody
    public TableDataInfo supervisorStatList(SupervisorStatInfo supervisorStatInfo) {
        startPage();
        List<SupervisorStatInfo> list = statisticalService.getSupervisorStatInfos(supervisorStatInfo);
        return getDataTable(list);
    }

    /**
     * 导出督导统计数据
     */
    @RequiresPermissions("task:statistical:supervisor-stat")
    @Log(title = "督导统计", businessType = BusinessType.EXPORT)
    @PostMapping("/supervisor/stat/export")
    @ResponseBody
    public AjaxResult supervisorStatExport(SupervisorStatInfo supervisorStatInfo) {
        List<SupervisorStatInfo> list = statisticalService.getSupervisorStatInfos(supervisorStatInfo);
        ExcelUtil<SupervisorStatInfo> util = new ExcelUtil<>(SupervisorStatInfo.class);
        return util.exportExcel(list, "督导统计数据");
    }

    /**
     * 财务修改页面
     */
    @RequiresPermissions("task:statistical:finance")
    @GetMapping("/finance")
    public String finance(@RequestParam("taskId") String taskId, Model model) {
        ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(taskId);
        model.addAttribute("projectTasks", projectTasks);
        return "task/statistical/finance";
    }

    /**
     * 财务修改保存
     */
    @RequiresPermissions("task:statistical:finance")
    @Log(title = "财务修改", businessType = BusinessType.UPDATE)
    @PostMapping("/finance/edit")
    @ResponseBody
    public AjaxResult editFinance(ProjectTasks projectTasks) {
        projectTasks.setDeductionAmount(projectTasks.getDeductionAmount() == null ? 0 : projectTasks.getDeductionAmount());
        projectTasks.setOtherCost(projectTasks.getOtherCost() == null ? 0 : projectTasks.getOtherCost());
        projectTasks.setPrepaidCost(projectTasks.getPrepaidCost() == null ? 0 : projectTasks.getPrepaidCost());
        projectTasks.setProjectRebate(projectTasks.getProjectRebate() == null ? 0 : projectTasks.getProjectRebate());
        projectTasks.setCustomerComment(projectTasks.getCustomerComment() == null ? "" : projectTasks.getCustomerComment());
        
        return toAjax(projectTasksService.updateProjectTasks(projectTasks));
    }
}