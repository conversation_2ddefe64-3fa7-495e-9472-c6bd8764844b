package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.domain.ProjectTaskLabor;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskLaborMapper;
import com.ruoyi.pmrsch.task.mapper.ProjectTasksMapper;
import com.ruoyi.pmrsch.task.service.IProjectTaskLaborService;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;

/**
 * 项目任务劳务费Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskLaborServiceImpl implements IProjectTaskLaborService {
    @Autowired
    private ProjectTaskLaborMapper projectTaskLaborMapper;
    
    @Autowired
    private ProjectTasksMapper projectTasksMapper;
    
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询项目任务劳务费
     * 
     * @param id 项目任务劳务费主键
     * @return 项目任务劳务费
     */
    @Override
    public ProjectTaskLabor selectProjectTaskLaborById(String id){
        ProjectTaskLabor labor = projectTaskLaborMapper.selectProjectTaskLaborById(id);
        if (labor != null) {
            supplementInfo(labor);
        }
        return labor;
    }

    /**
     * 查询项目任务劳务费列表
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 项目任务劳务费
     */
    @Override
    public List<ProjectTaskLabor> selectProjectTaskLaborList(ProjectTaskLabor projectTaskLabor){
        // 处理未完成付款查询条件
        if (projectTaskLabor != null && projectTaskLabor.getParams() != null 
                && "true".equals(projectTaskLabor.getParams().get("unpaidOnly"))) {
            projectTaskLabor.getParams().put("hasUnpaidAmount", "true");
        }
        
        List<ProjectTaskLabor> list = projectTaskLaborMapper.selectProjectTaskLaborList(projectTaskLabor);
        for (ProjectTaskLabor labor : list) {
            supplementInfo(labor);
        }
        return list;
    }

    /**
     * 查询项目任务劳务费统计信息
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectProjectTaskLaborTotal(ProjectTaskLabor projectTaskLabor) {
        return projectTaskLaborMapper.selectProjectTaskLaborTotal(projectTaskLabor);
    }

    /**
     * 补充项目任务劳务费关联信息
     * 
     * @param labor 项目任务劳务费
     */
    private void supplementInfo(ProjectTaskLabor labor) {
        if (labor == null || StringUtils.isEmpty(labor.getProjectTaskId())) {
            return;
        }
        
        // 查询任务信息
        ProjectTasks task = projectTasksMapper.selectProjectTasksById(labor.getProjectTaskId());
        if (task != null) {
            labor.setProjectTask(task);
            
            // 查询项目信息
            if (StringUtils.isNotEmpty(task.getProjectInfoId())) {
                ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(task.getProjectInfoId());
                labor.setProjectInfo(projectInfo);
            }
            
            // 查询督导信息
            if (task.getUserId() != null) {
                SysUser supervisor = sysUserMapper.selectUserById(task.getUserId());
                labor.setSupervisor(supervisor);
            }
        }
    }

    /**
     * 新增项目任务劳务费
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 结果
     */
    @Override
    public int insertProjectTaskLabor(ProjectTaskLabor projectTaskLabor){
        // 计算应发金额 = 工作天数 * 单价 + 加班补贴
        Double workDays = projectTaskLabor.getWorkDays() != null ? projectTaskLabor.getWorkDays() : 0.0;
        Double laborPrice = projectTaskLabor.getLaborPrice() != null ? projectTaskLabor.getLaborPrice() : 0.0;
        Double overtimeAmount = projectTaskLabor.getOvertimeAmount() != null ? projectTaskLabor.getOvertimeAmount() : 0.0;
        Double taxTate = projectTaskLabor.getTaxTate() != null ? projectTaskLabor.getTaxTate() : 0.0;
        
        Double amountPayable = workDays * laborPrice + overtimeAmount;
        projectTaskLabor.setAmountPayable(amountPayable);
        
        // 计算实发金额 = 应发金额 - 应发金额 * 税点
        Double amountPaid = amountPayable - (amountPayable * taxTate);
        projectTaskLabor.setAmountPaid(amountPaid);
        
        return projectTaskLaborMapper.insertProjectTaskLabor(projectTaskLabor);
    }

    /**
     * 修改项目任务劳务费
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 结果
     */
    @Override
    public int updateProjectTaskLabor(ProjectTaskLabor projectTaskLabor){
        return projectTaskLaborMapper.updateProjectTaskLabor(projectTaskLabor);
    }

    /**
     * 批量删除项目任务劳务费
     * 
     * @param ids 需要删除的项目任务劳务费主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskLaborByIds(String ids){
        return projectTaskLaborMapper.deleteProjectTaskLaborByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务劳务费信息
     * 
     * @param id 项目任务劳务费主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskLaborById(String id){
        return projectTaskLaborMapper.deleteProjectTaskLaborById(id);
    }

    /**
     * 更新项目任务劳务费付款信息
     * 
     * @param projectTaskLabor 项目任务劳务费
     * @return 结果
     */
    @Override
    public int updateProjectTaskLaborPayment(ProjectTaskLabor projectTaskLabor){
        return projectTaskLaborMapper.updateProjectTaskLaborPayment(projectTaskLabor);
    }

    public void delByTaskId(String taskId) {
        ProjectTaskLabor query = new ProjectTaskLabor();
        query.setProjectTaskId(taskId);
        List<ProjectTaskLabor> labors = projectTaskLaborMapper.selectProjectTaskLaborList(query);
        
        if (labors != null && !labors.isEmpty()) {
            List<String> ids = labors.stream().map(ProjectTaskLabor::getId).collect(Collectors.toList());
            String idsStr = String.join(",", ids);
            projectTaskLaborMapper.deleteProjectTaskLaborByIds(Convert.toStrArray(idsStr));
        }
    }
}
