package com.ruoyi.pmrsch.task.service;/**
 * <AUTHOR>
 * @Date 2025/3/11
 */

import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.task.controller.StatisticalController;
import com.ruoyi.pmrsch.task.entity.SupervisorStatInfo;
import com.ruoyi.pmrsch.task.entity.TaskCountInfo;
import com.ruoyi.pmrsch.task.mapper.StatisticalMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 描述
 * @className StatisticalService
 * <AUTHOR>
 * @date 2025年03月11日 13:17
 */
@Service
public class StatisticalService {
    @Autowired
    private StatisticalMapper statisticalMapper;

    public List<TaskCountInfo> getTaskCountInfos(TaskCountInfo taskCountInfo) {
        return statisticalMapper.getTaskCountInfos(taskCountInfo);
    }
    
    /**
     * 获取督导统计信息
     * 
     * @param supervisorStatInfo 督导统计信息查询参数
     * @return 督导统计信息列表
     */
    public List<SupervisorStatInfo> getSupervisorStatInfos(SupervisorStatInfo supervisorStatInfo) {
        return statisticalMapper.getSupervisorStatInfos(supervisorStatInfo);
    }
}
