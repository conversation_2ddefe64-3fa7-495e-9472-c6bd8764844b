package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs;
import com.ruoyi.pmrsch.task.mapper.ProjectInvoicingLogsMapper;
import com.ruoyi.pmrsch.task.service.IInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 开票记录 服务层实现
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class InvoiceServiceImpl implements IInvoiceService {
    @Autowired
    private ProjectInvoicingLogsMapper projectInvoicingLogsMapper;

    /**
     * 查询开票记录信息
     * 
     * @param id 开票记录ID
     * @return 开票记录信息
     */
    @Override
    public ProjectInvoicingLogs selectProjectInvoicingLogsById(String id) {
        return projectInvoicingLogsMapper.selectProjectInvoicingLogsById(id);
    }

    /**
     * 查询开票记录列表
     * 
     * @param projectInvoicingLogs 开票记录信息
     * @return 开票记录集合
     */
    @Override
    public List<ProjectInvoicingLogs> selectProjectInvoicingLogsList(ProjectInvoicingLogs projectInvoicingLogs) {
        return projectInvoicingLogsMapper.selectProjectInvoicingLogsList(projectInvoicingLogs);
    }

    /**
     * 新增开票记录
     * 
     * @param projectInvoicingLogs 开票记录信息
     * @return 结果
     */
    @Override
    public int insertProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs) {
        if (StringUtils.isEmpty(projectInvoicingLogs.getId())) {
            projectInvoicingLogs.setId(IdUtils.fastSimpleUUID());
        }
        projectInvoicingLogs.setCreateTime(DateUtils.getNowDate());
        return projectInvoicingLogsMapper.insertProjectInvoicingLogs(projectInvoicingLogs);
    }

    /**
     * 修改开票记录
     * 
     * @param projectInvoicingLogs 开票记录信息
     * @return 结果
     */
    @Override
    public int updateProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs) {
        projectInvoicingLogs.setUpdateTime(DateUtils.getNowDate());
        return projectInvoicingLogsMapper.updateProjectInvoicingLogs(projectInvoicingLogs);
    }

    /**
     * 删除开票记录对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteProjectInvoicingLogsByIds(String ids) {
        return projectInvoicingLogsMapper.deleteProjectInvoicingLogsByIds(Convert.toStrArray(ids));
    }
} 