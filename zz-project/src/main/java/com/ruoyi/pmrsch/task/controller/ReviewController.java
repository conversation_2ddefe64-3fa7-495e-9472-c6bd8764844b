package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.task.domain.ApprovalLogs;
import com.ruoyi.pmrsch.task.service.IApprovalLogsService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审批记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/review")
public class ReviewController extends BaseController{
    private String prefix = "task/reviewlogs";

    @Autowired
    private IApprovalLogsService approvalLogsService;

    @RequiresPermissions("task:reviewlogs:view")
    @GetMapping()
    public String reviewlogs(){
        return prefix + "/reviewlogs";
    }

    /**
     * 查询审批记录列表
     */
    @RequiresPermissions("task:reviewlogs:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ApprovalLogs approvalLogs){
        startPage();
        List<ApprovalLogs> list = approvalLogsService.selectApprovalLogsList(approvalLogs);
        return getDataTable(list);
    }

    /**
     * 导出审批记录列表
     */
    @RequiresPermissions("task:reviewlogs:export")
    @Log(title = "审批记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ApprovalLogs approvalLogs){
        List<ApprovalLogs> list = approvalLogsService.selectApprovalLogsList(approvalLogs);
        ExcelUtil<ApprovalLogs> util = new ExcelUtil<ApprovalLogs>(ApprovalLogs.class);
        return util.exportExcel(list, "审批记录数据");
    }

    /**
     * 新增审批记录
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/add";
    }

    /**
     * 新增保存审批记录
     */
    @RequiresPermissions("task:reviewlogs:add")
    @Log(title = "审批记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ApprovalLogs approvalLogs){
        return toAjax(approvalLogsService.insertApprovalLogs(approvalLogs));
    }

    /**
     * 修改审批记录
     */
    @RequiresPermissions("task:reviewlogs:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        ApprovalLogs approvalLogs = approvalLogsService.selectApprovalLogsById(id);
        mmap.put("approvalLogs", approvalLogs);
        return prefix + "/edit";
    }

    /**
     * 修改保存审批记录
     */
    @RequiresPermissions("task:reviewlogs:edit")
    @Log(title = "审批记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ApprovalLogs approvalLogs){
        return toAjax(approvalLogsService.updateApprovalLogs(approvalLogs));
    }

    /**
     * 删除审批记录
     */
    @RequiresPermissions("task:reviewlogs:remove")
    @Log(title = "审批记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(approvalLogsService.deleteApprovalLogsByIds(ids));
    }
}
