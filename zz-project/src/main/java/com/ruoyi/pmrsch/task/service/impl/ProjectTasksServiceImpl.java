package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.SetUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.file.UploadFilesUtil;
import com.ruoyi.pmrsch.file.dao.model.UploadFiles;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.mapper.CustomerMapper;
import com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper;
import com.ruoyi.pmrsch.task.budget.service.impl.ProjectTaskLoansServiceImpl;
import com.ruoyi.pmrsch.task.domain.*;
import com.ruoyi.pmrsch.task.entity.ApplyStatus;
import com.ruoyi.pmrsch.task.entity.ReviewBType;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.manager.ReviewLogsManager;
import com.ruoyi.pmrsch.task.mapper.*;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目阶段任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTasksServiceImpl implements IProjectTasksService {
    @Autowired
    private ProjectTasksMapper projectTasksMapper;
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private ReviewLogsManager reviewLogsManager;
    @Autowired
    private ProjectTaskSampleMapper taskSampleMapper;
    @Autowired
    private ProjectTaskSettleMapper taskSettleMapper;
    @Autowired
    private ProjectTaskBudgetServiceImpl taskBudgetService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ProjectTaskLaborMapper taskLaborMapper;
    @Autowired
    private ProjectTaskRespondentMapper taskRespondentMapper;
    @Autowired
    private ProjectTaskLoansServiceImpl projectTaskLoansService;
    @Autowired
    private ProjectTaskLaborServiceImpl taskLaborService;
    @Autowired
    private ProjectTaskSettleServiceImpl taskSettleService;
    @Autowired
    private ProjectTaskSampleServiceImpl taskSampleService;
    @Autowired
    private ProjectTaskRespondentServiceImpl taskRespondentService;

    /**
     * 查询项目阶段任务
     * 
     * @param id 项目阶段任务主键
     * @return 项目阶段任务
     */
    @Override
    public ProjectTasks selectProjectTasksById(String id){
        ProjectTasks projectTasks = projectTasksMapper.selectProjectTasksById(id);
        supplement(projectTasks);
        return projectTasks;
    }

    /**
     * 查询项目阶段任务列表
     * 
     * @param projectTasks 项目阶段任务
     * @return 项目阶段任务
     */
    @DataScope(deptAlias = "t", userAlias = "t")
    @Override
    public List<ProjectTasks> selectProjectTasksList(ProjectTasks projectTasks){
        List<ProjectTasks> projectTasksList = projectTasksMapper.selectProjectTasksList(projectTasks);
        supplement(projectTasksList);
        return projectTasksList;
    }

    /**
     * 新增项目阶段任务
     * 
     * @param projectTasks 项目阶段任务
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int apply(ProjectTasks projectTasks){
        projectTasks.setId(RandomStringUtil.gen18UUID());
        projectTasks.setApplyStatusCode(ApplyStatus.待提审.getValue());
        projectTasks.setSettleStatusCode(ReviewStatus.待提审.getValue());
        projectTasks.setSettleApplicant(ShiroUtils.getUserId()+"");
        projectTasks.setApplyTime(TimeUtil.getCurrentDate());
        projectTasks.setCreateTime(TimeUtil.getCurrentDate());
        projectTasks.setUserId(ShiroUtils.getUserId());
        projectTasks.setDeptId(ShiroUtils.getSysUser().getDeptId());

        int num = projectTasksMapper.insertProjectTasks(projectTasks);

        List<ProjectTaskSample> taskSamples = projectTasks.getSampleList();
        if(taskSamples != null && !taskSamples.isEmpty()) {
            for(ProjectTaskSample s : taskSamples){
                s.setId(RandomStringUtil.gen18UUID());
                s.setProjectTaskId(projectTasks.getId());
                s.setIsSupplement(0);
                taskSampleMapper.insertProjectTaskSample(s);
            }
        }

        return num;
    }

    /**
     * 修改项目阶段任务
     * 
     * @param projectTasks 项目阶段任务
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int applyEdit(ProjectTasks projectTasks){
        ParamUtil.checkObjParam(projectTasks,"projectTasks");
        ProjectTasks oTaskInfo = ParamUtil.requireNotNull(selectProjectTasksById(projectTasks.getId()),"找不到任务信息.id:"+projectTasks.getId());

        List<ApplyStatus> allowStatus = Arrays.asList(ApplyStatus.待提审,ApplyStatus.拒绝);
        ApplyStatus curStatus = ApplyStatus.getByValue(oTaskInfo.getApplyStatusCode());
        if(!allowStatus.contains(curStatus)){
            throw new ApiException(curStatus.name()+"状态不允许修改",ApiException.BAD_REQUEST);
        }

        List<ProjectTaskSample> taskSamples = projectTasks.getSampleList();
        updtSamples(oTaskInfo,taskSamples,false);

        projectTasks.setUpdateBy(ShiroUtils.getUserId()+"");
        projectTasks.setUpdateTime(TimeUtil.getCurrentDate());
        return projectTasksMapper.updateProjectTasks(projectTasks);
    }

    public void updtSamples(ProjectTasks oTaskInfo, List<ProjectTaskSample> taskSamples,boolean isAttach) {
        List<ProjectTaskSample> newSamples = getNewSamples(oTaskInfo,taskSamples);
        for(ProjectTaskSample s:newSamples){
            s.setId(RandomStringUtil.gen18UUID());
            s.setProjectTaskId(oTaskInfo.getId());
            s.setIsSupplement(isAttach ? 1 : 0);
            taskSampleMapper.insertProjectTaskSample(s);
        }

        List<ProjectTaskSample> updSamples = getUpdSamples(oTaskInfo,taskSamples);
        for(ProjectTaskSample s:updSamples){
            taskSampleMapper.updateProjectTaskSample(s);
        }

        List<ProjectTaskSample> delSamples = getDelSamples(oTaskInfo,taskSamples);
        for(ProjectTaskSample s:delSamples){
            taskSampleMapper.deleteProjectTaskSampleById(s.getId());
        }
    }

    private List<ProjectTaskSample> getUpdSamples(ProjectTasks oTaskInfo, List<ProjectTaskSample> taskSamples) {
        if(taskSamples == null || taskSamples.isEmpty()){
            return new ArrayList<>();
        }

        List<ProjectTaskSample> oTaskSamples = oTaskInfo != null ? oTaskInfo.getSampleAllList() : null;

        if(oTaskSamples == null || oTaskSamples.isEmpty()){
            return new ArrayList<>();
        }

        List<String> oList = oTaskSamples.stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskSamples.stream().map(c -> c.getId()).collect(Collectors.toList());

        List<String> intersectionList = SetUtil.intersection(oList,cList);
        return taskSamples.stream().filter(c -> StringUtils.isNotBlank(c.getId()) && intersectionList.contains(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskSample> getNewSamples(ProjectTasks oTaskInfo, List<ProjectTaskSample> taskSamples) {
        if(taskSamples == null || taskSamples.isEmpty()){
            return new ArrayList<>();
        }

        if(oTaskInfo == null){
            return taskSamples;
        }

        List<ProjectTaskSample> oTaskSamples = oTaskInfo.getSampleAllList();
        if(oTaskSamples == null || oTaskSamples.isEmpty()){
            return taskSamples;
        }

        return taskSamples.stream().filter(c -> StringUtils.isBlank(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskSample> getDelSamples(ProjectTasks oTaskInfo, List<ProjectTaskSample> taskSamples) {
        List<ProjectTaskSample> oTaskSamples = oTaskInfo != null ? oTaskInfo.getSampleAllList() : null;

        boolean oSamplesIsEmpty = oTaskSamples == null || oTaskSamples.isEmpty();
        boolean samplesIsEmpty = taskSamples == null || taskSamples.isEmpty();

        if(oSamplesIsEmpty){
            return new ArrayList<>();
        }

        if(samplesIsEmpty){
            return oTaskInfo.getSampleList();
        }

        List<String> oList = oTaskInfo.getSampleList().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskSamples.stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> dList = SetUtil.difference(cList,oList);

        return oTaskInfo.getSampleList().stream().filter(c -> dList.contains(c.getId())).collect(Collectors.toList());
    }

    /**
     * 批量删除项目阶段任务
     * 
     * @param ids 需要删除的项目阶段任务主键
     * @return 结果
     */
    @Override
    public int deleteProjectTasksByIds(String ids){
        return projectTasksMapper.deleteProjectTasksByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目阶段任务信息
     * 
     * @param id 项目阶段任务主键
     * @return 结果
     */
    @Override
    public int deleteProjectTasksById(String id){
        ProjectTasks projectTasks = ParamUtil.requireNotNull(this.selectProjectTasksById(id),"找不到任务信息.id:"+id);
        List<ApplyStatus> allowDeleteStatus = Arrays.asList(ApplyStatus.待提审,ApplyStatus.拒绝);
        if(!allowDeleteStatus.contains(ApplyStatus.getByValue(projectTasks.getApplyStatusCode()))){
            throw new ApiException("当前状态不允许删除",ApiException.BAD_REQUEST);
        }

        reviewLogsManager.delByBizId(ReviewBType.任务,id);
        projectTaskLoansService.delByTaskId(id);
        taskBudgetService.delByTaskId(id);
        taskLaborService.delByTaskId(id);
        taskRespondentService.delByTaskId(id);
        taskSettleService.delByTaskId(id);
        taskSampleService.delByTaskId(id);

        return projectTasksMapper.deleteProjectTasksById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int review(ApplyStatus reviewStatus,String taskId, String userId, String remark) {
        ParamUtil.checkObjParam(reviewStatus,"applyStatus");
        ParamUtil.checkStrParam(taskId,"taskId");
        ParamUtil.checkStrParam(userId,"userId");

        ProjectTasks projectTasks = ParamUtil.requireNotNull(this.selectProjectTasksById(taskId),"找不到任务信息.taskId:"+taskId);
        ApplyStatus curStatus = ApplyStatus.getByValue(Optional.ofNullable(projectTasks.getApplyStatusCode()).orElse(ApplyStatus.待提审.getValue()));

        List<ApplyStatus> operatableStatus = null;
        switch (reviewStatus){
            case 提审:
                operatableStatus = Arrays.asList(ApplyStatus.待提审,ApplyStatus.拒绝);
                break;
            case 审核:
                operatableStatus = Arrays.asList(ApplyStatus.提审);
                break;
            case 取消:
                operatableStatus = Arrays.asList(ApplyStatus.待提审);
                break;
            case 拒绝:
                operatableStatus = Arrays.asList(ApplyStatus.提审);
                break;
            default:
                throw new ApiException("操作状态不正确",ApiException.BAD_REQUEST);
        }

        if(!operatableStatus.contains(curStatus)){
            throw new ApiException("当前状态不可进行"+reviewStatus.name()+"操作",ApiException.BAD_REQUEST);
        }

        projectTasks.setApplyStatusCode(reviewStatus.getValue());
        if(ApplyStatus.提审.equals(reviewStatus)) {
            ParamUtil.checkApiArrParam(projectTasks.getSampleList(),"缺少样本信息");
            //ParamUtil.checkApiArrParam(projectTasks.getTaskCostStartList(),"费用信息");
            projectTasks.setApplyTime(TimeUtil.getCurrentDate());
        }

        reviewLogsManager.inserLog(ReviewBType.任务,taskId,userId,reviewStatus.getValue(),remark);

        return updateProjectTasks(projectTasks);
    }

    @Override
    public int updateProjectTasks(ProjectTasks projectTasks) {
        return projectTasksMapper.updateProjectTasks(projectTasks);
    }

    private void supplement(ProjectTasks projectTasks) {
        if(projectTasks == null){
            return;
        }

        if(StringUtils.isNotBlank(projectTasks.getSampleCostFileId())){
            UploadFiles fileInfo = UploadFilesUtil.getFileInfo(projectTasks.getSampleCostFileId());
            if(fileInfo != null){
                projectTasks.setSampleCostFileName(fileInfo.getFilename());
                projectTasks.setSampleCostLink(fileInfo.getFileUrl());
            }
        }

        ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(projectTasks.getProjectInfoId());
        projectTasks.setProjectInfo(projectInfo);

        if(projectInfo != null){
            Customer customer = customerMapper.selectCustomerById(projectInfo.getCustomerId());
            projectTasks.setCustomer(customer);
        }

        projectTasks.setApplicantUser(projectTasks.getUserId() != null ? userService.selectUserById(projectTasks.getUserId()) : null);
        projectTasks.setSettleApplicantUser(StringUtils.isNotBlank(projectTasks.getSettleApplicant()) ? userService.selectUserById(Long.parseLong(projectTasks.getSettleApplicant())) : null);

        ProjectTaskSample q = new ProjectTaskSample();
        q.setProjectTaskId(projectTasks.getId());
        projectTasks.setSampleAllList(taskSampleMapper.selectProjectTaskSampleList(q));

        q.setIsSupplement(0);
        projectTasks.setSampleList(taskSampleMapper.selectProjectTaskSampleList(q));

        ProjectTaskSettle q2 = new ProjectTaskSettle();
        q2.setProjectTaskId(projectTasks.getId());
        projectTasks.setTaskSettles(taskSettleMapper.selectProjectTaskSettleList(q2));

        ProjectTaskBudget q3 = new ProjectTaskBudget();
        q3.setProjectTaskId(projectTasks.getId());
        projectTasks.setTaskBudgets(taskBudgetService.selectProjectTaskBudgetList(projectTasks,q3));

        ProjectTaskLabor q4 = new ProjectTaskLabor();
        q4.setProjectTaskId(projectTasks.getId());
        projectTasks.setTaskLabors(taskLaborMapper.selectProjectTaskLaborList(q4));

        ProjectTaskRespondent q5 = new ProjectTaskRespondent();
        q5.setProjectTaskId(projectTasks.getId());
        projectTasks.setTaskRespondents(taskRespondentMapper.selectProjectTaskRespondentList(q5));
    }

    private void supplement(List<ProjectTasks> projectTasksList) {
        if(projectTasksList == null || projectTasksList.isEmpty()){
            return;
        }

        for(ProjectTasks tasks:projectTasksList){
            supplement(tasks);
        }
    }

    /**
     * 获取项目任务下拉列表
     * 
     * @param keyword 搜索关键词
     * @return 项目任务列表
     */
    @Override
    public List<Map<String, Object>> suggestList(String keyword) {
        return projectTasksMapper.suggestList(keyword);
    }
}
