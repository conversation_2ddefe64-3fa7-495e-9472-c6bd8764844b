package com.ruoyi.pmrsch.task.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 督导统计信息对象
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@Data
public class SupervisorStatInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 督导姓名 */
    @Excel(name = "督导姓名")
    private String supervisorName;

    /** 项目数量 */
    @Excel(name = "项目数量")
    private Integer projectCount;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private Double contractAmount;

    /** 最终合同额 */
    @Excel(name = "最终合同额")
    private Double finalContractAmount;

    /** 营业额 */
    @Excel(name = "营业额")
    private Double businessAmount;

    /** 预算总额 */
    @Excel(name = "预算总额")
    private Double budgetTotal;

    /** 借款总额 */
    @Excel(name = "借款总额")
    private Double loansTotal;

    /** 决算总额 */
    @Excel(name = "决算总额")
    private Double settleTotal;

    /** 利润率 */
    @Excel(name = "利润率")
    private Double finalProfitRatio;

    /** 查询参数：开始日期 */
    private String beginDate;
    
    /** 查询参数：结束日期 */
    private String endDate;
} 