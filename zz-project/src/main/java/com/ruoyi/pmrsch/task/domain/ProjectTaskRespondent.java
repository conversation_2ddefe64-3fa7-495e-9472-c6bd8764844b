package com.ruoyi.pmrsch.task.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 项目任务受访者对象 zz_project_task_respondent
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectTaskRespondent extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    private String projectTaskId;

    /** 项目编号(查询字段) */
    @Excel(name = "项目编号")
    private String projectNo;

    /** 项目任务名称(查询字段) */
    @Excel(name = "项目[任务]")
    private String projectTaskName;

     /** 督导姓名(查询字段) */
     @Excel(name = "督导")
     private String supervisorName;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idNumber;

    /** 年龄 */
    @Excel(name = "年龄")
    private String age;

    /** 所在城市 */
    @Excel(name = "所在城市")
    private String city;

    /** 婚姻情况 */
    @Excel(name = "婚姻情况")
    private String marital;

    /** 小孩情况 */
    @Excel(name = "小孩情况")
    private String kids;

    /** 家庭收入 */
    @Excel(name = "家庭收入")
    private String householdIncome;

    /** 行业+职业 */
    @Excel(name = "行业+职业")
    private String occupation;

    /** 项目类型 */
    @Excel(name = "项目类型")
    private String projectType;

    /** 联络员 */
    @Excel(name = "联络员")
    private String contactPerson;

    /** 项目涉及品类 */
    @Excel(name = "项目涉及品类")
    private String involvedCategories;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }
    
    public String getProjectTaskName() {
        return projectTaskName;
    }

    public void setProjectTaskName(String projectTaskName) {
        this.projectTaskName = projectTaskName;
    }
    
    public String getSupervisorName() {
        return supervisorName;
    }

    public void setSupervisorName(String supervisorName) {
        this.supervisorName = supervisorName;
    }


    /** 关联的项目信息 */
    private ProjectInfo projectInfo;
    
    /** 关联的任务信息 */
    private ProjectTasks projectTask;
    
    /** 督导人员 */
    private SysUser supervisor;

    /** 决算状态(查询字段) */
    private Integer settleStatusCode;

    public Integer getSettleStatusCode() {
        return settleStatusCode;
    }

    public void setSettleStatusCode(Integer settleStatusCode) {
        this.settleStatusCode = settleStatusCode;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getAge() {
        return age;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCity() {
        return city;
    }

    public void setMarital(String marital) {
        this.marital = marital;
    }

    public String getMarital() {
        return marital;
    }

    public void setKids(String kids) {
        this.kids = kids;
    }

    public String getKids() {
        return kids;
    }

    public void setHouseholdIncome(String householdIncome) {
        this.householdIncome = householdIncome;
    }

    public String getHouseholdIncome() {
        return householdIncome;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setInvolvedCategories(String involvedCategories) {
        this.involvedCategories = involvedCategories;
    }

    public String getInvolvedCategories() {
        return involvedCategories;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }
    
    public ProjectInfo getProjectInfo() {
        return projectInfo;
    }

    public void setProjectInfo(ProjectInfo projectInfo) {
        this.projectInfo = projectInfo;
    }

    public ProjectTasks getProjectTask() {
        return projectTask;
    }

    public void setProjectTask(ProjectTasks projectTask) {
        this.projectTask = projectTask;
    }

    public SysUser getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(SysUser supervisor) {
        this.supervisor = supervisor;
    }
    

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("name", getName())
            .append("phone", getPhone())
            .append("idNumber", getIdNumber())
            .append("age", getAge())
            .append("city", getCity())
            .append("marital", getMarital())
            .append("kids", getKids())
            .append("householdIncome", getHouseholdIncome())
            .append("occupation", getOccupation())
            .append("projectType", getProjectType())
            .append("contactPerson", getContactPerson())
            .append("involvedCategories", getInvolvedCategories())
            .append("remarks", getRemarks())
            .toString();
    }
}
