package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.service.impl.ProjectInfoServiceImpl;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;
import com.ruoyi.pmrsch.task.budget.service.IProjectTaskLoansService;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.entity.ApplyStatus;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import com.ruoyi.pmrsch.task.service.IProjectTaskBudgetService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目阶段任务Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task")
public class ProcessTasksController extends BaseController{
    private String prefix = "task";

    @Autowired
    private IProjectTasksService projectTasksService;
    @Autowired
    private ProjectInfoServiceImpl projectInfoService;
    @Autowired
    private IProjectTaskBudgetService budgetService;
    @Autowired
    private IProjectTaskLoansService projectTaskLoansService;

    /**
     * 申请列表页
     * @return
     */
    @RequiresPermissions("task:tasks:apply:view")
    @GetMapping("apply")
    public String appyList(String projectInfoId,ModelMap mmap){
        mmap.put("review",ApplyStatus.提审.getValue());
        ProjectInfo projectInfo = projectInfoService.selectProjectInfoById(projectInfoId);
        mmap.put("projectInfo", projectInfo);
        return prefix + "/apply/tasks";
    }

    /**
     * 申请列表
     */
    @RequiresPermissions("task:tasks:apply:list")
    @PostMapping("apply")
    @ResponseBody
    public TableDataInfo appyList(ProjectTasks projectTasks, HttpServletRequest request){
        //projectTasks.getParams().put("applyStatus", String.join(",",Arrays.asList(ApplyStatus.待提交.getValue()+"",ApplyStatus.)));
        startPage();
        List<ProjectTasks> list = projectTasksService.selectProjectTasksList(projectTasks);
        return getDataTable(list);
    }

    /**
     * 新增申请页
     */
    @GetMapping("apply/add")
    public String applyAdd(String projectInfoId,ModelMap mmap){
        ProjectInfo projectInfo = projectInfoService.selectProjectInfoById(projectInfoId);
        mmap.put("projectInfo", projectInfo);
        return prefix + "/apply/add";
    }

    /**
     * 新增申请
     */
    @RequiresPermissions("task:tasks:apply:add")
    @Log(title = "项目阶段任务", businessType = BusinessType.INSERT)
    @PostMapping("apply/add")
    @ResponseBody
    public AjaxResult applyAdd(ProjectTasks projectTasks){
        return toAjax(projectTasksService.apply(projectTasks));
    }

    /**
     * 修改申请页
     */
    @RequiresPermissions("task:tasks:apply:edit")
    @GetMapping("apply/edit/{id}")
    public String appyEdit(@PathVariable("id") String id,String fragment, ModelMap mmap){
        ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(id);
        mmap.put("projectTasks", projectTasks);
        return StringUtils.isBlank(fragment) ? prefix + "/apply/edit" : "fragment/project::"+fragment;
    }

    /**
     * 修改申请
     */
    @RequiresPermissions("task:tasks:apply:edit")
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("apply/edit")
    @ResponseBody
    public AjaxResult applyEdit(ProjectTasks projectTasks){
        return toAjax(projectTasksService.applyEdit(projectTasks));
    }

    /**
     * 删除申请
     */
    @RequiresPermissions("task:tasks:apply:remove")
    @Log(title = "项目阶段任务", businessType = BusinessType.DELETE)
    @PostMapping( "apply/remove")
    @ResponseBody
    public AjaxResult appyRemove(String ids){
        return toAjax(projectTasksService.deleteProjectTasksById(ids));
    }


    /**
     * 导出申请列表
     */
    @RequiresPermissions("task:tasks:apply:export")
    @Log(title = "项目阶段任务", businessType = BusinessType.EXPORT)
    @PostMapping("apply/export")
    @ResponseBody
    public AjaxResult applyExport(ProjectTasks projectTasks){
        List<ProjectTasks> list = projectTasksService.selectProjectTasksList(projectTasks);
        ExcelUtil<ProjectTasks> util = new ExcelUtil<ProjectTasks>(ProjectTasks.class);
        return util.exportExcel(list, "项目阶段任务数据");
    }

    /**
     * 申请审批(主管)列表页
     * @return
     */
    @RequiresPermissions("task:tasks:review:manager")
    @GetMapping("review/manager")
    public String reviewManagerList(ModelMap mmap){
        mmap.put("review",ApplyStatus.审核.getValue());
        return prefix + "/review/manager";
    }

    /**
     * 申请审批(主管)列表
     */
    @RequiresPermissions("task:tasks:review:manager")
    @PostMapping("review/manager")
    @ResponseBody
    public TableDataInfo reviewManagerList(ProjectTasks projectTasks){
        projectTasks.getParams().put("applyStatus", String.join(",",Arrays.asList(ApplyStatus.提审.getValue()+"")));
        startPage();
        List<ProjectTasks> list = projectTasksService.selectProjectTasksList(projectTasks);
        return getDataTable(list);
    }

    /**
     * 审批操作
     */
    @RequiresPermissions({"task:tasks:review:manager"})
    @GetMapping("{id}/review")
    public String review(@PathVariable("id") String id,@RequestParam String review, ModelMap mmap){
        ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(id);
        mmap.put("projectTasks", projectTasks);
        mmap.put("review",review);
        return prefix + "/review/review";
    }

    /**
     * 审批操作
     */
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("{id}/review")
    @ResponseBody
    public AjaxResult review(@PathVariable("id") String id,Integer review,String remark){
        Subject subject = ShiroUtils.getSubject();

        ApplyStatus reviewStatus = ApplyStatus.getByValue(review);
        boolean hasPrivilege = false;
        switch (reviewStatus){
            case 提审:
                hasPrivilege = subject.isPermitted("task:tasks:apply:edit");
                break;
            case 审核:
                hasPrivilege = subject.isPermitted("task:tasks:review:manager");
                break;
            case 取消:
                hasPrivilege = subject.isPermitted("task:tasks:apply:edit");
                break;
            case 拒绝:
                hasPrivilege = checkRejectPrivilege(subject,id);
                break;
        }

        if(!hasPrivilege){
            throw new ApiException("没有操作权限",ApiException.AUTH_DENIED);
        }

        return toAjax(projectTasksService.review(reviewStatus,id,ShiroUtils.getUserId()+"",remark));
    }

    private boolean checkRejectPrivilege(Subject subject, String taskId) {
        ProjectTasks projectTasks = ParamUtil.requireNotNull(projectTasksService.selectProjectTasksById(taskId),"找不到任务信息.taskId:"+taskId);
        ApplyStatus curStatus = ApplyStatus.getByValue(Optional.ofNullable(projectTasks.getApplyStatusCode()).orElse(ApplyStatus.待提审.getValue()));
        switch (curStatus){
            case 提审:
                return subject.isPermitted("task:tasks:review:manager");
            default:
                throw new IllegalStateException("Unexpected value: " + curStatus);
        }
    }

    /**
     * 查看统计
     */
    @GetMapping("/{id}/total")
    public String taskTotal(@PathVariable("projectId") String projectId, ModelMap mmap){
        mmap.put("projectId", projectId);
        return "fragment/project::taskTotal";
    }

    /**
     * 获取待处理业务数
     */
    @GetMapping("/pending-count")
    @ResponseBody
    public AjaxResult getPendingCount(@RequestParam String menuName) {
        int count = 0;
        
        switch(menuName) {
            case "任务待审(主管)":
                ProjectTasks taskQuery = new ProjectTasks();
                taskQuery.setApplyStatusCode(ApplyStatus.提审.getValue());
                count = projectTasksService.selectProjectTasksList(taskQuery).size();
                break;
                
            case "预算待审(主管)":
                ProjectTaskBudget budgetQuery = new ProjectTaskBudget();
                budgetQuery.setBudgetStatusCode(ReviewStatus.提审.getValue());
                count = budgetService.selectProjectTaskBudgetList(budgetQuery).size();
                break;
                
            case "预算待审(财务)":
                ProjectTaskBudget budgetQuery2 = new ProjectTaskBudget();
                budgetQuery2.setBudgetStatusCode(ReviewStatus.主管审核.getValue());
                count = budgetService.selectProjectTaskBudgetList(budgetQuery2).size();
                break;
                
            case "预算待审(老板)":
                ProjectTaskBudget budgetQuery3 = new ProjectTaskBudget();
                budgetQuery3.setBudgetStatusCode(ReviewStatus.财务审核.getValue());
                count = budgetService.selectProjectTaskBudgetList(budgetQuery3).size();
                break;
                
            case "借款待审(主管)":
                ProjectTaskLoans loansQuery = new ProjectTaskLoans();
                loansQuery.setLoansStatusCode(ReviewStatus.提审.getValue());
                count = projectTaskLoansService.selectProjectTaskLoansList(loansQuery).size();
                break;
                
            case "借款待审(财务)":
                ProjectTaskLoans loansQuery2 = new ProjectTaskLoans();
                loansQuery2.setLoansStatusCode(ReviewStatus.主管审核.getValue());
                count = projectTaskLoansService.selectProjectTaskLoansList(loansQuery2).size();
                break;

            case "借款打款":
                ProjectTaskLoans projectTaskLoans = new ProjectTaskLoans();
                projectTaskLoans.setLoansStatusCode(ReviewStatus.老板审核.getValue());
                projectTaskLoans.setIsPayment(false);
                count = projectTaskLoansService.selectProjectTaskLoansList(projectTaskLoans).size();
                break;    
                
            case "借款待审(老板)":
                ProjectTaskLoans loansQuery4 = new ProjectTaskLoans();
                loansQuery4.setLoansStatusCode(ReviewStatus.财务审核.getValue());
                count = projectTaskLoansService.selectProjectTaskLoansList(loansQuery4).size();
                break;
                
            case "决算待审(主管)":
                ProjectTasks settleQuery = new ProjectTasks();
                settleQuery.setSettleStatusCode(ReviewStatus.提审.getValue());
                count = projectTasksService.selectProjectTasksList(settleQuery).size();
                break;
                
            case "决算待审(财务)":
                ProjectTasks settleQuery2 = new ProjectTasks();
                settleQuery2.setSettleStatusCode(ReviewStatus.主管审核.getValue());
                count = projectTasksService.selectProjectTasksList(settleQuery2).size();
                break;
                
            case "决算待审(老板)":
                ProjectTasks settleQuery3 = new ProjectTasks();
                settleQuery3.setSettleStatusCode(ReviewStatus.财务审核.getValue());
                count = projectTasksService.selectProjectTasksList(settleQuery3).size();
                break;
        }
        
        return AjaxResult.success(count);
    }

    /**
     * 获取项目任务下拉列表
     */
    @GetMapping("projecttask/suggestList")
    @ResponseBody
    public Map suggestList(@RequestParam(required = false) String keyword) {
        List<Map<String, Object>> list = projectTasksService.suggestList(keyword);
        Map data = new HashMap<>();

        data.put("value",list.stream().map(e -> {
            Map einfo = new HashMap<>();
            einfo.put("id",e.get("id"));
            einfo.put("projectNo",e.get("projectNo"));
            einfo.put("taskName",e.get("taskName"));
            einfo.put("projectName",e.get("projectName"));
            return einfo;
        }).collect(Collectors.toList()));

        return data;
    }
}
