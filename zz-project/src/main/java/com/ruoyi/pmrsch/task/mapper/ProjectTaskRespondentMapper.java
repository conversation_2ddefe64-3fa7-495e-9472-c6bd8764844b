package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.task.domain.ProjectTaskRespondent;

import java.util.List;

/**
 * 项目任务受访者Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskRespondentMapper {
    /**
     * 查询项目任务受访者
     * 
     * @param id 项目任务受访者主键
     * @return 项目任务受访者
     */
    public ProjectTaskRespondent selectProjectTaskRespondentById(String id);

    /**
     * 查询项目任务受访者列表
     * 
     * @param projectTaskRespondent 项目任务受访者
     * @return 项目任务受访者集合
     */
    public List<ProjectTaskRespondent> selectProjectTaskRespondentList(ProjectTaskRespondent projectTaskRespondent);

    /**
     * 新增项目任务受访者
     * 
     * @param projectTaskRespondent 项目任务受访者
     * @return 结果
     */
    public int insertProjectTaskRespondent(ProjectTaskRespondent projectTaskRespondent);

    /**
     * 修改项目任务受访者
     * 
     * @param projectTaskRespondent 项目任务受访者
     * @return 结果
     */
    public int updateProjectTaskRespondent(ProjectTaskRespondent projectTaskRespondent);

    /**
     * 删除项目任务受访者
     * 
     * @param id 项目任务受访者主键
     * @return 结果
     */
    public int deleteProjectTaskRespondentById(String id);

    /**
     * 批量删除项目任务受访者
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskRespondentByIds(String[] ids);
}
