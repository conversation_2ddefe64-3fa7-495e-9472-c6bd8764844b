package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.task.domain.ApprovalLogs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 审批记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ApprovalLogsMapper {
    /**
     * 查询审批记录
     * 
     * @param id 审批记录主键
     * @return 审批记录
     */
    public ApprovalLogs selectApprovalLogsById(String id);

    /**
     * 查询审批记录列表
     * 
     * @param approvalLogs 审批记录
     * @return 审批记录集合
     */
    public List<ApprovalLogs> selectApprovalLogsList(ApprovalLogs approvalLogs);

    /**
     * 新增审批记录
     * 
     * @param approvalLogs 审批记录
     * @return 结果
     */
    public int insertApprovalLogs(ApprovalLogs approvalLogs);

    /**
     * 修改审批记录
     * 
     * @param approvalLogs 审批记录
     * @return 结果
     */
    public int updateApprovalLogs(ApprovalLogs approvalLogs);

    /**
     * 删除审批记录
     * 
     * @param id 审批记录主键
     * @return 结果
     */
    public int deleteApprovalLogsById(String id);

    /**
     * 批量删除审批记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApprovalLogsByIds(String[] ids);
    
    /**
     * 根据业务类型和业务ID删除审批记录
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 结果
     */
    public int deleteApprovalLogsByBusinessTypeAndId(@Param("businessType") Integer businessType, @Param("businessId") String businessId);
}
