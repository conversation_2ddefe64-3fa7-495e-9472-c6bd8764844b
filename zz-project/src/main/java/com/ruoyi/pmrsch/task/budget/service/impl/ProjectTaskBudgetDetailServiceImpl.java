package com.ruoyi.pmrsch.task.budget.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskBudgetDetail;
import com.ruoyi.pmrsch.task.budget.mapper.ProjectTaskBudgetDetailMapper;
import com.ruoyi.pmrsch.task.budget.service.IProjectTaskBudgetDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目任务预算详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskBudgetDetailServiceImpl implements IProjectTaskBudgetDetailService {
    @Autowired
    private ProjectTaskBudgetDetailMapper projectTaskBudgetDetailMapper;

    /**
     * 查询项目任务预算详情
     * 
     * @param id 项目任务预算详情主键
     * @return 项目任务预算详情
     */
    @Override
    public ProjectTaskBudgetDetail selectProjectTaskBudgetDetailById(String id){
        return projectTaskBudgetDetailMapper.selectProjectTaskBudgetDetailById(id);
    }

    /**
     * 查询项目任务预算详情列表
     * 
     * @param projectTaskBudgetDetail 项目任务预算详情
     * @return 项目任务预算详情
     */
    @Override
    public List<ProjectTaskBudgetDetail> selectProjectTaskBudgetDetailList(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        return projectTaskBudgetDetailMapper.selectProjectTaskBudgetDetailList(projectTaskBudgetDetail);
    }

    /**
     * 新增项目任务预算详情
     * 
     * @param projectTaskBudgetDetail 项目任务预算详情
     * @return 结果
     */
    @Override
    public int insertProjectTaskBudgetDetail(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        projectTaskBudgetDetail.setCreateTime(DateUtils.getNowDate());
        return projectTaskBudgetDetailMapper.insertProjectTaskBudgetDetail(projectTaskBudgetDetail);
    }

    /**
     * 修改项目任务预算详情
     * 
     * @param projectTaskBudgetDetail 项目任务预算详情
     * @return 结果
     */
    @Override
    public int updateProjectTaskBudgetDetail(ProjectTaskBudgetDetail projectTaskBudgetDetail){
        return projectTaskBudgetDetailMapper.updateProjectTaskBudgetDetail(projectTaskBudgetDetail);
    }

    /**
     * 批量删除项目任务预算详情
     * 
     * @param ids 需要删除的项目任务预算详情主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskBudgetDetailByIds(String ids){
        return projectTaskBudgetDetailMapper.deleteProjectTaskBudgetDetailByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务预算详情信息
     * 
     * @param id 项目任务预算详情主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskBudgetDetailById(String id){
        return projectTaskBudgetDetailMapper.deleteProjectTaskBudgetDetailById(id);
    }
}
