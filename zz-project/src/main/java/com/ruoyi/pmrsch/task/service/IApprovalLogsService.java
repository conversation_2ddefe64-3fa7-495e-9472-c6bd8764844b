package com.ruoyi.pmrsch.task.service;

import com.ruoyi.pmrsch.task.domain.ApprovalLogs;

import java.util.List;

/**
 * 审批记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IApprovalLogsService {
    /**
     * 查询审批记录
     * 
     * @param id 审批记录主键
     * @return 审批记录
     */
    public ApprovalLogs selectApprovalLogsById(String id);

    /**
     * 查询审批记录列表
     * 
     * @param approvalLogs 审批记录
     * @return 审批记录集合
     */
    public List<ApprovalLogs> selectApprovalLogsList(ApprovalLogs approvalLogs);

    /**
     * 新增审批记录
     * 
     * @param approvalLogs 审批记录
     * @return 结果
     */
    public int insertApprovalLogs(ApprovalLogs approvalLogs);

    /**
     * 修改审批记录
     * 
     * @param approvalLogs 审批记录
     * @return 结果
     */
    public int updateApprovalLogs(ApprovalLogs approvalLogs);

    /**
     * 批量删除审批记录
     * 
     * @param ids 需要删除的审批记录主键集合
     * @return 结果
     */
    public int deleteApprovalLogsByIds(String ids);

    /**
     * 删除审批记录信息
     * 
     * @param id 审批记录主键
     * @return 结果
     */
    public int deleteApprovalLogsById(String id);
}
