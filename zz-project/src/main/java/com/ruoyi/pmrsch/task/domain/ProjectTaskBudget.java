package com.ruoyi.pmrsch.task.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskBudgetDetail;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目任务预算对象 zz_project_task_budget
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class ProjectTaskBudget extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    private String budgetName;

    /** 项目任务 ID */
    @Excel(name = "项目任务 ID")
    private String projectTaskId;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicant;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private Integer budgetStatusCode;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;
    private List<ProjectTaskBudgetDetail> budgetDetails;
    private List<ProjectTaskLoans> taskLoans;
    private ProjectTasks projectTasks;
    private SysUser applicantUser;

    public Double getTotalBudget() {
        if(budgetDetails == null || budgetDetails.isEmpty()){
            return 0.0;
        }

        return budgetDetails.stream().collect(Collectors.summarizingDouble(ProjectTaskBudgetDetail::getSubtotal)).getSum();
    }

    public Double getTotalLoans() {
        if(taskLoans == null || taskLoans.isEmpty()){
            return 0.0;
        }

        List<ReviewStatus> totalStatus = Arrays.asList(ReviewStatus.提审,ReviewStatus.主管审核,ReviewStatus.财务审核,ReviewStatus.老板审核);
        return taskLoans.stream().filter(t -> totalStatus.contains(ReviewStatus.getByValue(t.getLoansStatusCode()))).collect(Collectors.summarizingDouble(ProjectTaskLoans::getAmount)).getSum();
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setBudgetStatusCode(Integer budgetStatusCode) {
        this.budgetStatusCode = budgetStatusCode;
    }

    public Integer getBudgetStatusCode() {
        return budgetStatusCode;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("createTime", getCreateTime())
            .append("applicant", getApplicant())
            .append("applyTime", getApplyTime())
            .append("budgetStatusCode", getBudgetStatusCode())
            .append("remarks", getRemarks())
            .toString();
    }

    public List<ProjectTaskBudgetDetail> getBudgetDetails() {
        return budgetDetails;
    }

    public void setBudgetDetails(List<ProjectTaskBudgetDetail> budgetDetails) {
        this.budgetDetails = budgetDetails;
    }

    public ProjectTasks getProjectTasks() {
        return projectTasks;
    }

    public void setProjectTasks(ProjectTasks projectTasks) {
        this.projectTasks = projectTasks;
    }

    public SysUser getApplicantUser() {
        return applicantUser;
    }

    public void setApplicantUser(SysUser applicantUser) {
        this.applicantUser = applicantUser;
    }

    public List<ProjectTaskLoans> getTaskLoans() {
        return taskLoans;
    }

    public void setTaskLoans(List<ProjectTaskLoans> taskLoans) {
        this.taskLoans = taskLoans;
    }

    public String getBudgetName() {
        return budgetName;
    }

    public void setBudgetName(String budgetName) {
        this.budgetName = budgetName;
    }
}
