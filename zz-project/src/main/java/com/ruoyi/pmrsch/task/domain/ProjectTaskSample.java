package com.ruoyi.pmrsch.task.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 项目任务样本对象 zz_project_task_sample
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectTaskSample extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    @Excel(name = "项目任务 ID")
    private String projectTaskId;

    /** 费用类型 code */
    @Excel(name = "费用类型 code")
    private String sampleTypeCode;

    /** 样本名称 */
    @Excel(name = "费用名称")
    private String sampleName;

    /** 单价 */
    @Excel(name = "单价")
    private Double samplePrice;

    /** 数量 */
    @Excel(name = "数量")
    private Double sampleQuantity;

    /** 样本完成量 */
    @Excel(name = "样本完成量")
    private Double sampleQuantityFinish;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 是否补充 */
    @Excel(name = "是否补充")
    private Integer isSupplement;

    public Double getSubtotal(){
        if(sampleQuantity == null || samplePrice == null){
            return 0.0;
        }

        return sampleQuantity * samplePrice;
    }

    public Double getFinishSubtotal(){
        if(sampleQuantityFinish == null || samplePrice == null){
            return 0.0;
        }

        return sampleQuantityFinish * samplePrice;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setSampleTypeCode(String sampleTypeCode) {
        this.sampleTypeCode = sampleTypeCode;
    }

    public String getSampleTypeCode() {
        return sampleTypeCode;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSamplePrice(Double samplePrice) {
        this.samplePrice = samplePrice;
    }

    public Double getSamplePrice() {
        return samplePrice;
    }

    public void setSampleQuantity(Double sampleQuantity) {
        this.sampleQuantity = sampleQuantity;
    }

    public Double getSampleQuantity() {
        return sampleQuantity;
    }

    public void setSampleQuantityFinish(Double sampleQuantityFinish) {
        this.sampleQuantityFinish = sampleQuantityFinish;
    }

    public Double getSampleQuantityFinish() {
        return sampleQuantityFinish;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setIsSupplement(Integer isSupplement) {
        this.isSupplement = isSupplement;
    }

    public Integer getIsSupplement() {
        return isSupplement;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("sampleTypeCode", getSampleTypeCode())
            .append("sampleName", getSampleName())
            .append("samplePrice", getSamplePrice())
            .append("sampleQuantity", getSampleQuantity())
            .append("sampleQuantityFinish", getSampleQuantityFinish())
            .append("remarks", getRemarks())
            .append("isSupplement", getIsSupplement())
            .toString();
    }
}
