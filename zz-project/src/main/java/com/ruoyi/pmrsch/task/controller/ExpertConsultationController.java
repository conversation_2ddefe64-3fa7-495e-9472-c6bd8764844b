package com.ruoyi.pmrsch.task.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.pmrsch.task.domain.ExpertConsultation;
import com.ruoyi.pmrsch.task.domain.ExpertConsultationBilling;
import com.ruoyi.pmrsch.task.service.IExpertConsultationService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import java.beans.PropertyEditorSupport;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.domain.ExpertInfo;
import com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper;
import com.ruoyi.pmrsch.pmdata.mapper.ExpertInfoMapper;
import com.ruoyi.pmrsch.pmdata.mapper.CustomerMapper;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.mapper.ProjectTasksMapper;
import com.ruoyi.common.utils.StringUtils;
import java.util.ArrayList;
import java.lang.StringBuilder;
import com.ruoyi.common.utils.ShiroUtils;

/**
 * 专家咨询记录Controller
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Controller
@RequestMapping("/task/consultation")
public class ExpertConsultationController extends BaseController
{
    private String prefix = "task/consultation";

    @Autowired
    private IExpertConsultationService expertConsultationService;

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private ProjectTasksMapper projectTasksMapper;

    @RequiresPermissions("task:consultation:view")
    @GetMapping()
    public String consultation()
    {
        return prefix + "/consultation";
    }

    /**
     * 账单统计视图
     */
    @RequiresPermissions("task:consultation:billing")
    @GetMapping("/billing")
    public String billing()
    {
        return prefix + "/billing";
    }

    /**
     * 查询专家咨询记录列表
     */
    @RequiresPermissions("task:consultation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExpertConsultation expertConsultation)
    {
        startPage();
        List<ExpertConsultation> list = expertConsultationService.selectExpertConsultationList(expertConsultation);
        TableDataInfo dataTable = getDataTable(list);

        // 添加统计信息
        Map<String, Object> footer = new HashMap<>();

        // 计算unitPrice总和
        BigDecimal unitPriceTotal = BigDecimal.ZERO;
        // 计算customerTotalCost总和
        BigDecimal customerTotalCostTotal = BigDecimal.ZERO;
        // 计算channelPrice总和
        BigDecimal channelPriceTotal = BigDecimal.ZERO;
        // 计算channelCost总和
        BigDecimal channelCostTotal = BigDecimal.ZERO;

        // 获取所有符合条件的数据（不分页）
        clearPage(); // 清除分页参数
        List<ExpertConsultation> allList = expertConsultationService.selectExpertConsultationList(expertConsultation);

        for (ExpertConsultation consultation : allList) {
            if (consultation.getUnitPrice() != null) {
                unitPriceTotal = unitPriceTotal.add(consultation.getUnitPrice());
            }

            if (consultation.getCustomerTotalCost() != null) {
                customerTotalCostTotal = customerTotalCostTotal.add(consultation.getCustomerTotalCost());
            }

            if (consultation.getChannelPrice() != null) {
                channelPriceTotal = channelPriceTotal.add(consultation.getChannelPrice());
            }

            if (consultation.getChannelCost() != null) {
                channelCostTotal = channelCostTotal.add(consultation.getChannelCost());
            }
        }

        footer.put("unitPriceTotal", unitPriceTotal);
        footer.put("customerTotalCostTotal", customerTotalCostTotal);
        footer.put("channelPriceTotal", channelPriceTotal);
        footer.put("channelCostTotal", channelCostTotal);

        Map<String, Object> otherData = new HashMap<>();
        otherData.put("footer", footer);
        dataTable.setOtherData(otherData);

        return dataTable;
    }

     /**
     * 导出专家咨询记录列表
     */
    @RequiresPermissions("task:consultation:export")
    @Log(title = "专家咨询记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ExpertConsultation expertConsultation)
    {
        List<ExpertConsultation> list = expertConsultationService.selectExpertConsultationList(expertConsultation);
        List<ExpertConsultationExportVO> exportList = new ArrayList<>();
        for (ExpertConsultation consultation : list) {
            ExpertConsultationExportVO exportVO = new ExpertConsultationExportVO();
            exportVO.setSupervisorName(consultation.getSupervisorName());
            exportVO.setCreatedAt(consultation.getCreatedAt());
            exportVO.setUpdatedAt(consultation.getUpdatedAt());
            exportVO.setInvoiceStatusCode(consultation.getInvoiceStatusCode());

            exportVO.setConsultationDate(consultation.getConsultationDate());
            exportVO.setConsultationTime(consultation.getConsultationTime());
            exportVO.setProjectNo(consultation.getProjectInfo() != null ? consultation.getProjectInfo().getpNo() : null);
            exportVO.setProjectName(consultation.getProjectInfo() != null ? consultation.getProjectInfo().getpName() : null);
            exportVO.setExpertName(consultation.getExpertInfo() != null ? consultation.getExpertInfo().getExpertName() : null);
            exportVO.setExpertPhone(consultation.getExpertInfo() != null ? consultation.getExpertInfo().getPhone() : null);

            // 获取客户名称，通过项目任务关联
            String customerName = null;
            if (consultation.getCustomer() != null) {
                customerName = consultation.getCustomer().getName();
            } else if (consultation.getProjectTask() != null &&
                      consultation.getProjectTask().getProjectInfo() != null &&
                      consultation.getProjectTask().getProjectInfo().getCustomer() != null) {
                customerName = consultation.getProjectTask().getProjectInfo().getCustomer().getName();
            }
            exportVO.setCustomerName(customerName);

            exportVO.setConsultationDuration(consultation.getConsultationDuration());
            exportVO.setUnitPrice(consultation.getUnitPrice());
            exportVO.setCustomerTotalCost(consultation.getCustomerTotalCost());
            exportVO.setChannel(consultation.getChannel());
            exportVO.setChannelPrice(consultation.getChannelPrice());
            exportVO.setChannelCost(consultation.getChannelCost());
            exportVO.setQuestion(consultation.getQuestion());
            exportVO.setDepartment(consultation.getDepartment());
            exportVO.setContactPerson(consultation.getContactPerson());
            exportVO.setContactEmail(consultation.getContactEmail());
            exportVO.setRemarks(consultation.getRemarks());
            exportVO.setProjectTypeLabel(consultation.getProjectTypeLabel());
            exportVO.setExpertCompanyName(consultation.getExpertCompanyName());
            exportList.add(exportVO);
        }
        ExcelUtil<ExpertConsultationExportVO> util = new ExcelUtil<>(ExpertConsultationExportVO.class);
        return util.exportExcel(exportList, "专家咨询记录数据");
    }

    /**
     * 查询专家咨询账单统计列表
     */
    @RequiresPermissions("task:consultation:billing")
    @PostMapping("/billingList")
    @ResponseBody
    public TableDataInfo billingList(ExpertConsultationBilling billing)
    {
        // 验证查询条件
        if (billing.getBeginTime() == null || billing.getEndTime() == null)
        {
            throw new ServiceException("开始日期和结束日期不能为空");
        }

        startPage();
        List<ExpertConsultationBilling> list = expertConsultationService.selectExpertConsultationBillingList(billing);
        return getDataTable(list);
    }

    /**
     * 导出专家咨询账单统计列表
     */
    @RequiresPermissions("task:consultation:billing")
    @Log(title = "专家咨询账单统计", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBilling")
    @ResponseBody
    public AjaxResult exportBilling(ExpertConsultationBilling billing)
    {
        // 验证查询条件
        if (billing.getBeginTime() == null || billing.getEndTime() == null)
        {
            throw new ServiceException("开始日期和结束日期不能为空");
        }

        List<ExpertConsultationBilling> list = expertConsultationService.selectExpertConsultationBillingList(billing);
        ExcelUtil<ExpertConsultationBilling> util = new ExcelUtil<ExpertConsultationBilling>(ExpertConsultationBilling.class);
        return util.exportExcel(list, "专家咨询账单统计数据");
    }

    /**
     * 新增专家咨询记录
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存专家咨询记录
     */
    @RequiresPermissions("task:consultation:add")
    @Log(title = "专家咨询记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ExpertConsultation expertConsultation)
    {
        return toAjax(expertConsultationService.insertExpertConsultation(expertConsultation));
    }

    /**
     * 修改专家咨询记录
     */
    @RequiresPermissions("task:consultation:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        ExpertConsultation expertConsultation = expertConsultationService.selectExpertConsultationById(id);
        mmap.put("expertConsultation", expertConsultation);
        return prefix + "/edit";
    }

    /**
     * 修改保存专家咨询记录
     */
    @RequiresPermissions("task:consultation:edit")
    @Log(title = "专家咨询记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ExpertConsultation expertConsultation)
    {
        return toAjax(expertConsultationService.updateExpertConsultation(expertConsultation));
    }

    /**
     * 删除专家咨询记录
     */
    @RequiresPermissions("task:consultation:remove")
    @Log(title = "专家咨询记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(expertConsultationService.deleteExpertConsultationByIds(ids));
    }

    /**
     * 下载导入模板
     */
    @RequiresPermissions("task:consultation:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<ExpertConsultation> util = new ExcelUtil<ExpertConsultation>(ExpertConsultation.class);
        return util.importTemplateExcel("专家咨询记录数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("task:consultation:import")
    @Log(title = "专家咨询记录", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ExpertConsultation> util = new ExcelUtil<ExpertConsultation>(ExpertConsultation.class);
        List<ExpertConsultation> expertConsultationList = util.importExcel(file.getInputStream());
        String message = importExpertConsultation(expertConsultationList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入专家咨询记录
     *
     * @param consultationList 专家咨询记录列表
     * @param isUpdateSupport 是否更新已存在记录
     * @return 结果
     */
    public String importExpertConsultation(List<ExpertConsultation> consultationList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(consultationList) || consultationList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ExpertConsultation consultation : consultationList)
        {
            try
            {
                // 验证专家信息
                if (StringUtils.isEmpty(consultation.getExpertName()) || StringUtils.isEmpty(consultation.getExpertPhone())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、专家名称、手机号不能为空");
                    continue;
                }

                ExpertInfo expertInfo = new ExpertInfo();
                expertInfo.setExpertName(consultation.getExpertName());
                expertInfo.setPhone(consultation.getExpertPhone());
                List<ExpertInfo> expertInfos = expertInfoMapper.selectExpertInfoList(expertInfo);
                if (expertInfos.size() > 0) {
                    consultation.setExpertInfoId(expertInfos.get(0).getId());
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、专家 " + consultation.getExpertName() + " 不存在");
                    continue;
                }

                // 验证项目信息
                if (StringUtils.isEmpty(consultation.getProjectNo())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、项目编号不能为空");
                    continue;
                }

                ProjectInfo projectInfo = new ProjectInfo();
                projectInfo.setpNo(consultation.getProjectNo());
                List<ProjectInfo> projectInfos = projectInfoMapper.selectProjectInfoList(projectInfo);
                if (projectInfos.size() > 0) {
                    consultation.setProjectInfoId(projectInfos.get(0).getId());

                    // 查找与项目关联的项目任务
                    ProjectTasks projectTask = new ProjectTasks();
                    projectTask.setProjectInfoId(projectInfos.get(0).getId());
                    List<ProjectTasks> projectTasks = projectTasksMapper.selectProjectTasksList(projectTask);
                    if (projectTasks.size() == 1) {
                        consultation.setProjectTaskId(projectTasks.get(0).getId());
                    } else if (projectTasks.size() > 1) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、项目 " + consultation.getProjectNo() + " 有多个关联的项目任务");
                        continue;
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、项目 " + consultation.getProjectNo() + " 没有关联的项目任务");
                        continue;
                    }
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、项目 " + consultation.getProjectNo() + " 不存在");
                    continue;
                }

                // 设置当前登录用户为记录所有者
                consultation.setUserId(ShiroUtils.getUserId());
                consultation.setDeptId(ShiroUtils.getSysUser().getDeptId());

                // 添加或更新记录
                if (StringUtils.isNotEmpty(consultation.getId()))
                {
                    if (isUpdateSupport)
                    {
                        ExpertConsultation existConsultation = expertConsultationService.selectExpertConsultationById(Long.parseLong(consultation.getId()));
                        if (existConsultation == null)
                        {
                            consultation.setId(null);
                            expertConsultationService.insertExpertConsultation(consultation);
                            successNum++;
                            successMsg.append("<br/>" + successNum + "、咨询记录添加成功");
                        }
                        else
                        {
                            expertConsultationService.updateExpertConsultation(consultation);
                            successNum++;
                            successMsg.append("<br/>" + successNum + "、咨询记录更新成功");
                        }
                    }
                    else
                    {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、咨询记录ID " + consultation.getId() + " 已存在，未更新");
                    }
                }
                else
                {
                    expertConsultationService.insertExpertConsultation(consultation);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、咨询记录添加成功");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、咨询记录 ";
                if (StringUtils.isNotEmpty(consultation.getExpertName()))
                {
                    msg += consultation.getExpertName();
                }
                msg += " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 自定义属性编辑器
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(false);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));

        // 时间格式
        binder.registerCustomEditor(Time.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                try {
                    if (text != null && !text.isEmpty()) {
                        setValue(Time.valueOf(text));
                    } else {
                        setValue(null);
                    }
                } catch (Exception e) {
                    setValue(null);
                }
            }
        });
    }
}
