package com.ruoyi.pmrsch.task.entity;

import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;

/**
 * <AUTHOR>
 * @Date 2025/2/11
 */
public enum CostType implements EnumIntegerInterface {
    报告(0),
    小结(1),
    场地(2),
    主持人(3),
    翻译(4),
    笔录(5),
    编程(6),
    数据清洗(7),
    出表(8),
    差旅费(9);

    private Integer value;

    CostType(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static CostType getByValue(int value) {
        return EnumIntegerInterface.getByValue(value, CostType.class);
    }
}