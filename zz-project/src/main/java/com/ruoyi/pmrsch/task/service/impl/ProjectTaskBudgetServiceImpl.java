package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.SetUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskBudgetDetail;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;
import com.ruoyi.pmrsch.task.budget.mapper.ProjectTaskBudgetDetailMapper;
import com.ruoyi.pmrsch.task.budget.mapper.ProjectTaskLoansMapper;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.domain.ProjectTaskSettle;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ReviewBType;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.manager.ReviewLogsManager;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskBudgetMapper;
import com.ruoyi.pmrsch.task.service.IProjectTaskBudgetService;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import com.ruoyi.system.service.ISysUserService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目任务预算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskBudgetServiceImpl implements IProjectTaskBudgetService {
    @Autowired
    private ProjectTaskBudgetMapper projectTaskBudgetMapper;
    @Autowired
    private ProjectTaskBudgetDetailMapper budgetDetailMapper;
    @Autowired
    private IProjectTasksService projectTasksService;
    @Autowired
    private ReviewLogsManager reviewLogsManager;
    @Autowired
    private ProjectTaskLoansMapper taskLoansMapper;
    @Autowired
    private ProjectTaskSettleServiceImpl taskSettleService;
    @Autowired
    private ISysUserService userService;

    /**
     * 查询项目任务预算
     * 
     * @param id 项目任务预算主键
     * @return 项目任务预算
     */
    @Override
    public ProjectTaskBudget selectProjectTaskBudgetById(String id){
        ProjectTaskBudget taskBudget = projectTaskBudgetMapper.selectProjectTaskBudgetById(id);
        supplement(taskBudget);
        return taskBudget;
    }

    /**
     * 查询项目任务预算列表
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 项目任务预算
     */
    @DataScope(deptAlias = "task", userAlias = "task")
    @Override
    public List<ProjectTaskBudget> selectProjectTaskBudgetList(ProjectTaskBudget projectTaskBudget){
        List<ProjectTaskBudget>  taskBudgets = projectTaskBudgetMapper.selectProjectTaskBudgetList(projectTaskBudget);
        supplement(taskBudgets);
        return taskBudgets;
    }

    public List<ProjectTaskBudget> selectProjectTaskBudgetList(ProjectTasks projectTasks,ProjectTaskBudget projectTaskBudget){
        List<ProjectTaskBudget>  taskBudgets = projectTaskBudgetMapper.selectProjectTaskBudgetList(projectTaskBudget);
        supplement(projectTasks,taskBudgets);
        return taskBudgets;
    }

    /**
     * 新增项目任务预算
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 结果
     */
    @Override
    public int apply(ProjectTaskBudget projectTaskBudget){
        ParamUtil.checkObjParam(projectTaskBudget,"projectTaskBudget");

        projectTaskBudget.setId(RandomStringUtil.gen18UUID());
        projectTaskBudget.setBudgetStatusCode(ReviewStatus.待提审.getValue());
        projectTaskBudget.setApplicant(ShiroUtils.getUserId()+"");
        projectTaskBudget.setApplyTime(TimeUtil.getCurrentDate());
        projectTaskBudget.setCreateTime(DateUtils.getNowDate());

        int num = projectTaskBudgetMapper.insertProjectTaskBudget(projectTaskBudget);
        List<ProjectTaskBudgetDetail> budgetDetails = projectTaskBudget.getBudgetDetails();
        if(budgetDetails != null && !budgetDetails.isEmpty()){
            for (ProjectTaskBudgetDetail budgetDetail : budgetDetails) {
                budgetDetail.setId(RandomStringUtil.gen18UUID());
                budgetDetail.setProjectTaskId(projectTaskBudget.getProjectTaskId());
                budgetDetail.setProjectTaskBudgetId(projectTaskBudget.getId());

                budgetDetailMapper.insertProjectTaskBudgetDetail(budgetDetail);
            }
        }

        return num;
    }

    /**
     * 修改项目任务预算
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 结果
     */
    @Override
    public int applyEdit(ProjectTaskBudget projectTaskBudget){
        ParamUtil.checkObjParam(projectTaskBudget,"projectTaskBudget");

        ProjectTaskBudget oTaskBudget = ParamUtil.requireNotNull(selectProjectTaskBudgetById(projectTaskBudget.getId()),"找不到预算信息。id:" + projectTaskBudget.getId());

        List<ReviewStatus> allowStatus = Arrays.asList(ReviewStatus.待提审,ReviewStatus.拒绝);
        ReviewStatus curStatus = ReviewStatus.getByValue(oTaskBudget.getBudgetStatusCode());
        if(!allowStatus.contains(curStatus)){
            throw new ApiException(curStatus.name()+"状态不允许修改",ApiException.BAD_REQUEST);
        }

        List<ProjectTaskBudgetDetail> budgetDetails = projectTaskBudget.getBudgetDetails();
        updateBudgets(oTaskBudget,budgetDetails);

        return projectTaskBudgetMapper.updateProjectTaskBudget(projectTaskBudget);
    }

    private void updateBudgets(ProjectTaskBudget oTaskBudget, List<ProjectTaskBudgetDetail> budgetDetails) {
        List<ProjectTaskBudgetDetail> newBudget = getNewBudget(oTaskBudget,budgetDetails);
        for(ProjectTaskBudgetDetail s:newBudget){
            s.setId(RandomStringUtil.gen18UUID());
            s.setProjectTaskId(oTaskBudget.getProjectTaskId());
            s.setProjectTaskBudgetId(oTaskBudget.getId());
            budgetDetailMapper.insertProjectTaskBudgetDetail(s);
        }

        List<ProjectTaskBudgetDetail> updBudget = getUpdBudget(oTaskBudget,budgetDetails);
        for(ProjectTaskBudgetDetail s:updBudget){
            budgetDetailMapper.updateProjectTaskBudgetDetail(s);
        }

        List<ProjectTaskBudgetDetail> delBudget = getDelBudget(oTaskBudget,budgetDetails);
        for(ProjectTaskBudgetDetail s:delBudget){
            budgetDetailMapper.deleteProjectTaskBudgetDetailById(s.getId());
        }
    }

    private List<ProjectTaskBudgetDetail> getDelBudget(ProjectTaskBudget oTaskBudget, List<ProjectTaskBudgetDetail> budgetDetails) {
        boolean oTaskBudgetIsEmpty = oTaskBudget == null || oTaskBudget.getBudgetDetails() == null || oTaskBudget.getBudgetDetails().isEmpty();
        boolean samplesIsEmpty = budgetDetails == null || budgetDetails.isEmpty();

        if(oTaskBudgetIsEmpty){
            return new ArrayList<>();
        }

        if(samplesIsEmpty){
            return oTaskBudget.getBudgetDetails();
        }

        List<String> oList = oTaskBudget.getBudgetDetails().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = budgetDetails.stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> dList = SetUtil.difference(cList,oList);

        return oTaskBudget.getBudgetDetails().stream().filter(c -> dList.contains(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskBudgetDetail> getUpdBudget(ProjectTaskBudget oTaskBudget, List<ProjectTaskBudgetDetail> budgetDetails) {
        if(budgetDetails == null || budgetDetails.isEmpty()){
            return new ArrayList<>();
        }

        if(oTaskBudget == null || oTaskBudget.getBudgetDetails() == null || oTaskBudget.getBudgetDetails().isEmpty()){
            return new ArrayList<>();
        }

        List<String> oList = oTaskBudget.getBudgetDetails().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = budgetDetails.stream().map(c -> c.getId()).collect(Collectors.toList());

        List<String> intersectionList = SetUtil.intersection(oList,cList);
        return budgetDetails.stream().filter(c -> StringUtils.isNotBlank(c.getId()) && intersectionList.contains(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskBudgetDetail> getNewBudget(ProjectTaskBudget oTaskBudget, List<ProjectTaskBudgetDetail> budgetDetails) {
        if(budgetDetails == null || budgetDetails.isEmpty()){
            return new ArrayList<>();
        }

        if(oTaskBudget == null || oTaskBudget.getBudgetDetails() == null || oTaskBudget.getBudgetDetails().isEmpty()){
            return budgetDetails;
        }

        return budgetDetails.stream().filter(c -> StringUtils.isBlank(c.getId())).collect(Collectors.toList());
    }

    /**
     * 批量删除项目任务预算
     * 
     * @param ids 需要删除的项目任务预算主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskBudgetByIds(String ids){
        return projectTaskBudgetMapper.deleteProjectTaskBudgetByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务预算信息
     * 
     * @param id 项目任务预算主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskBudgetById(String id){
        return projectTaskBudgetMapper.deleteProjectTaskBudgetById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int review(ReviewStatus reviewStatus, String budgetId, String userId, String remark) {
        ParamUtil.checkObjParam(reviewStatus,"applyStatus");
        ParamUtil.checkStrParam(budgetId,"budgetId");
        ParamUtil.checkStrParam(userId,"userId");

        ProjectTaskBudget taskBudget = ParamUtil.requireNotNull(this.selectProjectTaskBudgetById(budgetId),"找不到预算信息.budgetId:"+budgetId);
        ReviewStatus curStatus = ReviewStatus.getByValue(Optional.ofNullable(taskBudget.getBudgetStatusCode()).orElse(ReviewStatus.待提审.getValue()));

        List<ReviewStatus> allowStatus = null;
        switch (reviewStatus){
            case 提审:
                allowStatus = Arrays.asList(ReviewStatus.待提审,ReviewStatus.拒绝);
                break;
            case 主管审核:
                allowStatus = Arrays.asList(ReviewStatus.提审);
                break;
            case 财务审核:
                allowStatus = Arrays.asList(ReviewStatus.主管审核);
                break;
            case 老板审核:
                allowStatus = Arrays.asList(ReviewStatus.财务审核);
                break;
            case 取消:
                allowStatus = Arrays.asList(ReviewStatus.待提审);
                break;
            case 拒绝:
                allowStatus = Arrays.asList(ReviewStatus.提审,ReviewStatus.主管审核,ReviewStatus.财务审核);
                break;
            default:
                throw new ApiException("操作状态不正确",ApiException.BAD_REQUEST);
        }

        if(!allowStatus.contains(curStatus)){
            throw new ApiException("当前状态不可进行"+reviewStatus.name()+"操作",ApiException.BAD_REQUEST);
        }

        taskBudget.setBudgetStatusCode(reviewStatus.getValue());
        if(ReviewStatus.提审.equals(reviewStatus)) {
            ParamUtil.checkApiArrParam(taskBudget.getBudgetDetails(),"缺少预算详情信息");
            taskBudget.setApplyTime(TimeUtil.getCurrentDate());
        }

        if(ReviewStatus.老板审核.equals(reviewStatus)) {
            updateSettles(taskBudget);
        }

        reviewLogsManager.inserLog(ReviewBType.预算,budgetId,userId,reviewStatus.getValue(),remark);
        return projectTaskBudgetMapper.updateProjectTaskBudget(taskBudget);
    }

    private void updateSettles(ProjectTaskBudget taskBudget) {
        List<ProjectTaskBudgetDetail> taskBudgetDetails = taskBudget.getBudgetDetails();
        if(taskBudgetDetails == null || taskBudgetDetails.isEmpty()){
            return;
        }

        for (ProjectTaskBudgetDetail taskBudgetDetail : taskBudgetDetails) {
            ProjectTaskSettle settle = new ProjectTaskSettle();
            settle.setId(RandomStringUtil.gen18UUID());
            settle.setProjectTaskId(taskBudget.getProjectTaskId());
            settle.setCostCategorieMainCode(taskBudgetDetail.getCostCategorieMainCode());
            settle.setCostCategorieSubCode(taskBudgetDetail.getCostCategorieSubCode());
            settle.setPriceSettle(0.0);
            settle.setQuantitySettle(0.0);
            settle.setRemarks("");
            settle.setPriceBudget(taskBudgetDetail.getPriceBudget());
            settle.setQuantityBudget(taskBudgetDetail.getQuantityBudget());
            settle.setIsFromBudget(1);

            taskSettleService.insertProjectTaskSettle(settle);
        }

    }

    private void supplement(List<ProjectTaskBudget> taskBudgets) {
        supplement(null,taskBudgets);
    }
    private void supplement(ProjectTasks projectTasks,List<ProjectTaskBudget> taskBudgets) {
        if(taskBudgets == null || taskBudgets.isEmpty()){
            return;
        }

        for(ProjectTaskBudget budget:taskBudgets){
            supplement(projectTasks,budget);
        }
    }

    private void supplement(ProjectTaskBudget taskBudget) {
        supplement(null,taskBudget);
    }
    
    private void supplement(ProjectTasks projectTasks,ProjectTaskBudget taskBudget) {
        if(taskBudget == null){
            return;
        }

        if(projectTasks != null){
            taskBudget.setProjectTasks(projectTasks);
        }else{
            taskBudget.setProjectTasks(projectTasksService.selectProjectTasksById(taskBudget.getProjectTaskId()));

        }

        SysUser sysUser = userService.selectUserById(Long.parseLong(taskBudget.getApplicant()));
        taskBudget.setApplicantUser(sysUser);

        ProjectTaskBudgetDetail q = new ProjectTaskBudgetDetail();
        q.setProjectTaskBudgetId(taskBudget.getId());
        taskBudget.setBudgetDetails(budgetDetailMapper.selectProjectTaskBudgetDetailList(q));

        ProjectTaskLoans q1 = new ProjectTaskLoans();
        q1.setProjectTaskBudgetId(taskBudget.getId());
        taskBudget.setTaskLoans(taskLoansMapper.selectProjectTaskLoansList(q1));
    }

    public void delByTaskId(String taskId) {
        // 首先删除预算详情
        ProjectTaskBudget query = new ProjectTaskBudget();
        query.setProjectTaskId(taskId);
        List<ProjectTaskBudget> budgets = projectTaskBudgetMapper.selectProjectTaskBudgetList(query);
        
        if (budgets != null && !budgets.isEmpty()) {
            for (ProjectTaskBudget budget : budgets) {
                // 删除预算详情
                ProjectTaskBudgetDetail detailQuery = new ProjectTaskBudgetDetail();
                detailQuery.setProjectTaskBudgetId(budget.getId());
                List<ProjectTaskBudgetDetail> details = budgetDetailMapper.selectProjectTaskBudgetDetailList(detailQuery);
                if (details != null && !details.isEmpty()) {
                    List<String> detailIds = details.stream().map(ProjectTaskBudgetDetail::getId).collect(Collectors.toList());
                    String detailIdsStr = String.join(",", detailIds);
                    budgetDetailMapper.deleteProjectTaskBudgetDetailByIds(Convert.toStrArray(detailIdsStr));
                }
            }
            
            // 删除预算主表
            List<String> ids = budgets.stream().map(ProjectTaskBudget::getId).collect(Collectors.toList());
            String idsStr = String.join(",", ids);
            projectTaskBudgetMapper.deleteProjectTaskBudgetByIds(Convert.toStrArray(idsStr));
        }
    }
}
