package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;

import java.util.List;

/**
 * 项目任务预算Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskBudgetMapper {
    /**
     * 查询项目任务预算
     * 
     * @param id 项目任务预算主键
     * @return 项目任务预算
     */
    public ProjectTaskBudget selectProjectTaskBudgetById(String id);

    /**
     * 查询项目任务预算列表
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 项目任务预算集合
     */
    public List<ProjectTaskBudget> selectProjectTaskBudgetList(ProjectTaskBudget projectTaskBudget);

    /**
     * 新增项目任务预算
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 结果
     */
    public int insertProjectTaskBudget(ProjectTaskBudget projectTaskBudget);

    /**
     * 修改项目任务预算
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 结果
     */
    public int updateProjectTaskBudget(ProjectTaskBudget projectTaskBudget);

    /**
     * 删除项目任务预算
     * 
     * @param id 项目任务预算主键
     * @return 结果
     */
    public int deleteProjectTaskBudgetById(String id);

    /**
     * 批量删除项目任务预算
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskBudgetByIds(String[] ids);
}
