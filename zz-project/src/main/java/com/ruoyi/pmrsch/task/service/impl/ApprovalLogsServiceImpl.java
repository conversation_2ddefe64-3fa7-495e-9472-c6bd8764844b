package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.pmrsch.task.domain.ApprovalLogs;
import com.ruoyi.pmrsch.task.mapper.ApprovalLogsMapper;
import com.ruoyi.pmrsch.task.service.IApprovalLogsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审批记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ApprovalLogsServiceImpl implements IApprovalLogsService {
    @Autowired
    private ApprovalLogsMapper approvalLogsMapper;

    /**
     * 查询审批记录
     * 
     * @param id 审批记录主键
     * @return 审批记录
     */
    @Override
    public ApprovalLogs selectApprovalLogsById(String id){
        return approvalLogsMapper.selectApprovalLogsById(id);
    }

    /**
     * 查询审批记录列表
     * 
     * @param approvalLogs 审批记录
     * @return 审批记录
     */
    @Override
    public List<ApprovalLogs> selectApprovalLogsList(ApprovalLogs approvalLogs){
        return approvalLogsMapper.selectApprovalLogsList(approvalLogs);
    }

    /**
     * 新增审批记录
     * 
     * @param approvalLogs 审批记录
     * @return 结果
     */
    @Override
    public int insertApprovalLogs(ApprovalLogs approvalLogs){
        return approvalLogsMapper.insertApprovalLogs(approvalLogs);
    }

    /**
     * 修改审批记录
     * 
     * @param approvalLogs 审批记录
     * @return 结果
     */
    @Override
    public int updateApprovalLogs(ApprovalLogs approvalLogs){
        return approvalLogsMapper.updateApprovalLogs(approvalLogs);
    }

    /**
     * 批量删除审批记录
     * 
     * @param ids 需要删除的审批记录主键
     * @return 结果
     */
    @Override
    public int deleteApprovalLogsByIds(String ids){
        return approvalLogsMapper.deleteApprovalLogsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除审批记录信息
     * 
     * @param id 审批记录主键
     * @return 结果
     */
    @Override
    public int deleteApprovalLogsById(String id){
        return approvalLogsMapper.deleteApprovalLogsById(id);
    }
}
