package com.ruoyi.pmrsch.task.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目阶段任务对象 zz_project_tasks
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
@Data
public class ProjectTasks extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目信息 ID */
    @Excel(name = "项目信息 ID")
    private String projectInfoId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 客户联系人 */
    @Excel(name = "客户联系人")
    private String cusContactPerson;

    /** 客户联系电话 */
    @Excel(name = "客户联系电话")
    private String cusContactPhone;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /** 申请状态（待提交、待审核、已审核） */
    @Excel(name = "申请状态", readConverterExp = "待=提交、待审核、已审核")
    private Integer applyStatusCode;

    /** 决算状态（待提交、财务待审、经理待审、完成） */
    @Excel(name = "决算状态", readConverterExp = "待=提交、财务待审、经理待审、完成")
    private Integer settleStatusCode;

    /** 决算申请人 */
    @Excel(name = "决算申请人")
    private String settleApplicant;

    /** 决算申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "决算申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleApplyTime;

    /** 决算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "决算时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleTime;

    /** 结算备注 */
    @Excel(name = "决算备注")
    private String settleRemarks;

    /** 样本费用附件ID */
    private String sampleCostFileId;
    private String sampleCostLink;
    /** 样本费用附件文件名 */
    private String sampleCostFileName;

    /** 资料归档链接 */
    @Excel(name = "资料归档链接")
    private String archiveLink;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private Long deptId;
    
    /** 扣款 */
    @Excel(name = "扣款")
    private Double deductionAmount;
    
    /** 其它需要支付费用 */
    @Excel(name = "其它需要支付费用")
    private Double otherCost;
    
    /** 已收预付费 */
    @Excel(name = "已收预付费")
    private Double prepaidCost;
    
    /** 项目回扣 */
    @Excel(name = "项目回扣")
    private Double projectRebate;
    
    /** 客户评议 */
    @Excel(name = "客户评议")
    private String customerComment;
    
    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 实际开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "实际开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualStartTime;

    /** 实际结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "实际结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualEndTime;

    /** 执行城市 */
    @Excel(name = "执行城市")
    private String executionCity;

    /** 费用核对日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "费用核对日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date costCheckDate;

    private List<ProjectTaskSample> sampleList;
    private List<ProjectTaskSample> sampleAllList;
    private List<ProjectTaskSettle> taskSettles;
    private List<ProjectTaskBudget> taskBudgets;
    private List<ProjectTaskLabor> taskLabors;
    private List<ProjectTaskRespondent> taskRespondents;
    private ProjectInfo projectInfo;
    private Customer customer;
    private SysUser applicantUser;
    private SysUser settleApplicantUser;

    //劳务总额
    public Double getTotalLabors(){
        if(getTaskLabors() == null || getTaskLabors().isEmpty()){
            return 0.0;
        }

        return getTaskLabors().stream().collect(Collectors.summarizingDouble(ProjectTaskLabor::getAmountPaid)).getSum();
    }

    //合同总额(初始)
    public Double getContractTotal() {
        if(getSampleAllList() == null || getSampleAllList().isEmpty()){
            return 0.0;
        }

        return getSampleAllList().stream().collect(Collectors.summarizingDouble(ProjectTaskSample::getSubtotal)).getSum();
    }

    //合同总额(完成)
    public Double getContractFinishTotal() {
        if(getSampleAllList() == null || getSampleAllList().isEmpty()){
            return 0.0;
        }

        return getSampleAllList().stream().collect(Collectors.summarizingDouble(ProjectTaskSample::getFinishSubtotal)).getSum();
    }

    //预算总额
    public Double getTotalBudget(){
        if(taskBudgets == null || taskBudgets.isEmpty()){
            return 0.0;
        }

        return taskBudgets.stream().collect(Collectors.summarizingDouble(ProjectTaskBudget::getTotalBudget)).getSum();
    }

    //费用总额(与预算对应，实际成本支出额)
    public Double getTotalCost(){
        if(taskSettles == null || taskSettles.isEmpty()){
            return 0.0;
        }

        return taskSettles.stream().collect(Collectors.summarizingDouble(ProjectTaskSettle::getSubtotal)).getSum();
    }

    /*
    借款总额
     */
    public Double getTotalLoans(){
        if(taskBudgets == null || taskBudgets.isEmpty()){
            return 0.0;
        }

        return taskBudgets.stream().collect(Collectors.summarizingDouble(ProjectTaskBudget::getTotalLoans)).getSum();
    }

    //预算比例(预算总额/合同总额)
    public Double getBudgetRatio() {
        return getTotalBudget() / getContractTotal();
    }

    //成本比例(费用总额/合同总额(完成))
    public Double getCostRatio() {
        return getTotalCost() / getContractFinishTotal();
    }

    // 获取决算劳务费合计
    public Double getTotalSettleLabors(){
        if(getTaskSettles() == null || getTaskSettles().isEmpty()){
            return 0.0;
        }
        
        return getTaskSettles().stream()
                .filter(settle -> "1".equals(settle.getCostCategorieMainCode()) || "2".equals(settle.getCostCategorieMainCode())) // 筛选主分类为"1"(劳务费)的决算项
                .collect(Collectors.summarizingDouble(
                    settle -> {
                        Double quantity = settle.getQuantitySettle();
                        Double price = settle.getPriceSettle();
                        if(quantity == null || price == null){
                            return 0.0;
                        }
                        return quantity * price;
                    }
                )).getSum();
    }

}