package com.ruoyi.pmrsch.task.service;

import java.util.List;
import com.ruoyi.pmrsch.task.domain.ExpertConsultation;
import com.ruoyi.pmrsch.task.domain.ExpertConsultationBilling;

/**
 * 专家咨询记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
public interface IExpertConsultationService 
{
    /**
     * 查询专家咨询记录
     * 
     * @param id 专家咨询记录主键
     * @return 专家咨询记录
     */
    public ExpertConsultation selectExpertConsultationById(Long id);

    /**
     * 查询专家咨询记录列表
     * 
     * @param expertConsultation 专家咨询记录
     * @return 专家咨询记录集合
     */
    public List<ExpertConsultation> selectExpertConsultationList(ExpertConsultation expertConsultation);

    /**
     * 新增专家咨询记录
     * 
     * @param expertConsultation 专家咨询记录
     * @return 结果
     */
    public int insertExpertConsultation(ExpertConsultation expertConsultation);

    /**
     * 修改专家咨询记录
     * 
     * @param expertConsultation 专家咨询记录
     * @return 结果
     */
    public int updateExpertConsultation(ExpertConsultation expertConsultation);

    /**
     * 批量删除专家咨询记录
     * 
     * @param ids 需要删除的专家咨询记录主键集合
     * @return 结果
     */
    public int deleteExpertConsultationByIds(String ids);

    /**
     * 删除专家咨询记录信息
     * 
     * @param id 专家咨询记录主键
     * @return 结果
     */
    public int deleteExpertConsultationById(Long id);
    
    /**
     * 查询专家咨询账单统计列表
     * 
     * @param billing 统计参数
     * @return 账单统计结果集合
     */
    public List<ExpertConsultationBilling> selectExpertConsultationBillingList(ExpertConsultationBilling billing);
}
