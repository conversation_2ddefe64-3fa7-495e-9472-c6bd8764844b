package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.SetUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.task.domain.*;
import com.ruoyi.pmrsch.task.entity.ReviewBType;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.manager.ReviewLogsManager;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskLaborMapper;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskRespondentMapper;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskSampleMapper;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskSettleMapper;
import com.ruoyi.pmrsch.task.service.IProjectTaskSettleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目任务决算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskSettleServiceImpl implements IProjectTaskSettleService {
    @Autowired
    private ProjectTaskSettleMapper settleMapper;
    @Autowired
    private ReviewLogsManager reviewLogsManager;
    @Autowired
    private ProjectTasksServiceImpl tasksService;
    @Autowired
    private ProjectTaskLaborMapper taskLaborMapper;
    @Autowired
    private ProjectTaskRespondentMapper taskRespondentMapper;
    @Autowired
    private ProjectTaskSampleMapper taskSampleMapper;

    /**
     * 查询项目任务决算
     * 
     * @param id 项目任务决算主键
     * @return 项目任务决算
     */
    @Override
    public ProjectTaskSettle selectProjectTaskSettleById(String id) {
        return settleMapper.selectProjectTaskSettleById(id);
    }

    /**
     * 查询项目任务决算列表
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 项目任务决算
     */
    @Override
    public List<ProjectTaskSettle> selectProjectTaskSettleList(ProjectTaskSettle projectTaskSettle) {
        return settleMapper.selectProjectTaskSettleList(projectTaskSettle);
    }

    /**
     * 新增项目任务决算
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 结果
     */
    @Override
    public int insertProjectTaskSettle(ProjectTaskSettle projectTaskSettle) {
        projectTaskSettle.setCreateTime(DateUtils.getNowDate());
        return settleMapper.insertProjectTaskSettle(projectTaskSettle);
    }

    /**
     * 修改项目任务决算
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 结果
     */
    @Override
    public int updateProjectTaskSettle(ProjectTaskSettle projectTaskSettle) {
        return settleMapper.updateProjectTaskSettle(projectTaskSettle);
    }

    /**
     * 批量删除项目任务决算
     * 
     * @param ids 需要删除的项目任务决算主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskSettleByIds(String ids) {
        return settleMapper.deleteProjectTaskSettleByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务决算信息
     * 
     * @param id 项目任务决算主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskSettleById(String id) {
        return settleMapper.deleteProjectTaskSettleById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int review(ReviewStatus reviewStatus, String taskId, String userId, String remark) {
        ParamUtil.checkObjParam(reviewStatus, "applyStatus");
        ParamUtil.checkStrParam(taskId, "taskId");
        ParamUtil.checkStrParam(userId, "userId");

        ProjectTasks projectTasks = ParamUtil.requireNotNull(tasksService.selectProjectTasksById(taskId), "找不到任务信息.taskId:" + taskId);
        ReviewStatus curStatus = ReviewStatus.getByValue(Optional.ofNullable(projectTasks.getSettleStatusCode()).orElse(ReviewStatus.待提审.getValue()));

        List<ReviewStatus> allowStatus = null;
        switch (reviewStatus) {
            case 提审:
                allowStatus = Arrays.asList(ReviewStatus.待提审, ReviewStatus.拒绝);
                break;
            case 主管审核:
                allowStatus = Arrays.asList(ReviewStatus.提审);
                break;
            case 财务审核:
                allowStatus = Arrays.asList(ReviewStatus.主管审核);
                break;
            case 老板审核:
                allowStatus = Arrays.asList(ReviewStatus.财务审核);
                break;
            case 取消:
                allowStatus = Arrays.asList(ReviewStatus.待提审);
                break;
            case 拒绝:
                allowStatus = Arrays.asList(ReviewStatus.提审, ReviewStatus.主管审核, ReviewStatus.财务审核);
                break;
            default:
                throw new ApiException("操作状态不正确", ApiException.BAD_REQUEST);
        }

        if (!allowStatus.contains(curStatus)) {
            throw new ApiException("当前状态不可进行" + reviewStatus.name() + "操作", ApiException.BAD_REQUEST);
        }

        projectTasks.setSettleStatusCode(reviewStatus.getValue());
        if (ReviewStatus.提审.equals(reviewStatus)) {
            ParamUtil.checkApiArrParam(projectTasks.getTaskSettles(), "缺少决算详情信息");
            projectTasks.setSettleApplyTime(TimeUtil.getCurrentDate());

            // 检查劳务费与结算兼职费的差额
            Double totalSettleLabors = projectTasks.getTotalSettleLabors();
            Double totalLabors = projectTasks.getTotalLabors();

            double difference = totalSettleLabors - totalLabors;
            if (difference > 0) {
                throw new ApiException("劳务费录入不足，请补全", ApiException.BAD_REQUEST);
            } else if (difference < 0) {
                throw new ApiException("劳务费录入大于结算兼职费，请核对", ApiException.BAD_REQUEST);
            }
        }

        if (ReviewStatus.老板审核.equals(reviewStatus)) {
            projectTasks.setSettleTime(TimeUtil.getCurrentDate());
        }

        reviewLogsManager.inserLog(ReviewBType.决算, taskId, userId, reviewStatus.getValue(), remark);
        return tasksService.updateProjectTasks(projectTasks);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int applyEdit(ProjectTasks projectTasks) {
        ParamUtil.checkObjParam(projectTasks, "projectTasks");
        ParamUtil.checkStrParam(projectTasks.getId(), "taskId");
        ParamUtil.checkApiArrParam(projectTasks.getTaskSettles(), "缺少结算详情信息");

        ProjectTasks oTaskInfo = ParamUtil.requireNotNull(tasksService.selectProjectTasksById(projectTasks.getId()),
                "找不到任务信息:" + projectTasks.getId());

        List<ReviewStatus> allowStatus = Arrays.asList(ReviewStatus.待提审, ReviewStatus.拒绝);
        ReviewStatus curStatus = ReviewStatus.getByValue(oTaskInfo.getSettleStatusCode());
        if (!allowStatus.contains(curStatus)) {
            throw new ApiException(curStatus.name() + "状态不允许修改", ApiException.BAD_REQUEST);
        }

        List<ProjectTaskSettle> settles = projectTasks.getTaskSettles();
        updtSettles(oTaskInfo, settles);

        //List<ProjectTaskLabor> taskLabors = projectTasks.getTaskLabors();
        //updtTaskLabors(oTaskInfo, taskLabors);

        //List<ProjectTaskRespondent> taskRespondents = projectTasks.getTaskRespondents();
        //updtTaskRespondents(oTaskInfo, taskRespondents);

        List<ProjectTaskSample> taskSamples = projectTasks.getSampleAllList();
        tasksService.updtSamples(oTaskInfo, taskSamples,true);

        tasksService.updateProjectTasks(projectTasks);
        return 1;
    }

    private void updtTaskRespondents(ProjectTasks oTaskInfo, List<ProjectTaskRespondent> taskRespondents) {
        List<ProjectTaskRespondent> newTaskRespondents = getNewTaskRespondents(oTaskInfo, taskRespondents);
        for (ProjectTaskRespondent s : newTaskRespondents) {
            s.setId(RandomStringUtil.gen18UUID());
            s.setProjectTaskId(oTaskInfo.getId());
            taskRespondentMapper.insertProjectTaskRespondent(s);
        }

        List<ProjectTaskRespondent> updTaskRespondents = getUpdTaskRespondents(oTaskInfo, taskRespondents);
        for (ProjectTaskRespondent s : updTaskRespondents) {
            taskRespondentMapper.updateProjectTaskRespondent(s);
        }

        List<ProjectTaskRespondent> delTaskRespondents = getDelTaskRespondents(oTaskInfo, taskRespondents);
        for (ProjectTaskRespondent s : delTaskRespondents) {
            taskRespondentMapper.deleteProjectTaskRespondentById(s.getId());
        }
    }

    private List<ProjectTaskRespondent> getNewTaskRespondents(ProjectTasks oTaskInfo,
            List<ProjectTaskRespondent> taskRespondents) {
        if (taskRespondents == null || taskRespondents.isEmpty()) {
            return new ArrayList<>();
        }

        if (oTaskInfo == null || oTaskInfo.getTaskRespondents() == null || oTaskInfo.getTaskRespondents().isEmpty()) {
            return taskRespondents;
        }

        return taskRespondents.stream().filter(c -> StringUtils.isBlank(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskRespondent> getUpdTaskRespondents(ProjectTasks oTaskInfo,
            List<ProjectTaskRespondent> taskRespondents) {
        if (taskRespondents == null || taskRespondents.isEmpty()) {
            return new ArrayList<>();
        }

        if (oTaskInfo == null || oTaskInfo.getTaskRespondents() == null || oTaskInfo.getTaskRespondents().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> oList = oTaskInfo.getTaskRespondents().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskRespondents.stream().map(c -> c.getId()).collect(Collectors.toList());

        List<String> intersectionList = SetUtil.intersection(oList, cList);
        return taskRespondents.stream()
                .filter(c -> StringUtils.isNotBlank(c.getId()) && intersectionList.contains(c.getId()))
                .collect(Collectors.toList());
    }

    private List<ProjectTaskRespondent> getDelTaskRespondents(ProjectTasks oTaskInfo,
            List<ProjectTaskRespondent> taskRespondents) {
        boolean oSettlesIsEmpty = oTaskInfo == null || oTaskInfo.getTaskRespondents() == null
                || oTaskInfo.getTaskRespondents().isEmpty();
        boolean settlesIsEmpty = taskRespondents == null || taskRespondents.isEmpty();

        if (oSettlesIsEmpty) {
            return new ArrayList<>();
        }

        if (settlesIsEmpty) {
            return oTaskInfo.getTaskRespondents();
        }

        List<String> oList = oTaskInfo.getTaskRespondents().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskRespondents.stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> dList = SetUtil.difference(cList, oList);

        return oTaskInfo.getTaskRespondents().stream().filter(c -> dList.contains(c.getId()))
                .collect(Collectors.toList());
    }

    private void updtTaskLabors(ProjectTasks oTaskInfo, List<ProjectTaskLabor> taskLabors) {
        List<ProjectTaskLabor> newTaskLabors = getNewTaskLabors(oTaskInfo, taskLabors);
        for (ProjectTaskLabor s : newTaskLabors) {
            s.setId(RandomStringUtil.gen18UUID());
            s.setProjectTaskId(oTaskInfo.getId());
            taskLaborMapper.insertProjectTaskLabor(s);
        }

        List<ProjectTaskLabor> updTaskLabors = getUpdTaskLabors(oTaskInfo, taskLabors);
        for (ProjectTaskLabor s : updTaskLabors) {
            taskLaborMapper.updateProjectTaskLabor(s);
        }

        List<ProjectTaskLabor> delTaskLabors = getDelTaskLabors(oTaskInfo, taskLabors);
        for (ProjectTaskLabor s : delTaskLabors) {
            taskLaborMapper.deleteProjectTaskLaborById(s.getId());
        }
    }

    private List<ProjectTaskLabor> getNewTaskLabors(ProjectTasks oTaskInfo, List<ProjectTaskLabor> taskLabors) {
        if (taskLabors == null || taskLabors.isEmpty()) {
            return new ArrayList<>();
        }

        if (oTaskInfo == null || oTaskInfo.getTaskLabors() == null || oTaskInfo.getTaskLabors().isEmpty()) {
            return taskLabors;
        }

        return taskLabors.stream().filter(c -> StringUtils.isBlank(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskLabor> getUpdTaskLabors(ProjectTasks oTaskInfo, List<ProjectTaskLabor> taskLabors) {
        if (taskLabors == null || taskLabors.isEmpty()) {
            return new ArrayList<>();
        }

        if (oTaskInfo == null || oTaskInfo.getTaskLabors() == null || oTaskInfo.getTaskLabors().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> oList = oTaskInfo.getTaskLabors().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskLabors.stream().map(c -> c.getId()).collect(Collectors.toList());

        List<String> intersectionList = SetUtil.intersection(oList, cList);
        return taskLabors.stream()
                .filter(c -> StringUtils.isNotBlank(c.getId()) && intersectionList.contains(c.getId()))
                .collect(Collectors.toList());
    }

    private List<ProjectTaskLabor> getDelTaskLabors(ProjectTasks oTaskInfo, List<ProjectTaskLabor> taskLabors) {
        boolean oSettlesIsEmpty = oTaskInfo == null || oTaskInfo.getTaskLabors() == null
                || oTaskInfo.getTaskLabors().isEmpty();
        boolean settlesIsEmpty = taskLabors == null || taskLabors.isEmpty();

        if (oSettlesIsEmpty) {
            return new ArrayList<>();
        }

        if (settlesIsEmpty) {
            return oTaskInfo.getTaskLabors();
        }

        List<String> oList = oTaskInfo.getTaskLabors().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskLabors.stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> dList = SetUtil.difference(cList, oList);

        return oTaskInfo.getTaskLabors().stream().filter(c -> dList.contains(c.getId())).collect(Collectors.toList());
    }

    private void updtSettles(ProjectTasks oTaskInfo, List<ProjectTaskSettle> settles) {
        List<ProjectTaskSettle> newSettles = getNewSettles(oTaskInfo, settles);
        for (ProjectTaskSettle s : newSettles) {
            s.setId(RandomStringUtil.gen18UUID());
            s.setProjectTaskId(oTaskInfo.getId());
            s.setIsFromBudget(0);
            settleMapper.insertProjectTaskSettle(s);
        }

        List<ProjectTaskSettle> updSettles = getUpdSettles(oTaskInfo, settles);
        for (ProjectTaskSettle s : updSettles) {
            settleMapper.updateProjectTaskSettle(s);
        }

        List<ProjectTaskSettle> delSettles = getDelSettles(oTaskInfo, settles);
        for (ProjectTaskSettle s : delSettles) {
            settleMapper.deleteProjectTaskSettleById(s.getId());
        }
    }

    private List<ProjectTaskSettle> getDelSettles(ProjectTasks oTaskInfo, List<ProjectTaskSettle> taskSettles) {
        boolean oSettlesIsEmpty = oTaskInfo == null || oTaskInfo.getTaskSettles() == null
                || oTaskInfo.getTaskSettles().isEmpty();
        boolean settlesIsEmpty = taskSettles == null || taskSettles.isEmpty();

        if (oSettlesIsEmpty) {
            return new ArrayList<>();
        }

        if (settlesIsEmpty) {
            return oTaskInfo.getTaskSettles();
        }

        List<String> oList = oTaskInfo.getTaskSettles().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskSettles.stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> dList = SetUtil.difference(cList, oList);

        return oTaskInfo.getTaskSettles().stream().filter(c -> dList.contains(c.getId())).collect(Collectors.toList());
    }

    private List<ProjectTaskSettle> getUpdSettles(ProjectTasks oTaskInfo, List<ProjectTaskSettle> taskSettles) {
        if (taskSettles == null || taskSettles.isEmpty()) {
            return new ArrayList<>();
        }

        if (oTaskInfo == null || oTaskInfo.getTaskSettles() == null || oTaskInfo.getTaskSettles().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> oList = oTaskInfo.getTaskSettles().stream().map(c -> c.getId()).collect(Collectors.toList());
        List<String> cList = taskSettles.stream().map(c -> c.getId()).collect(Collectors.toList());

        List<String> intersectionList = SetUtil.intersection(oList, cList);
        return taskSettles.stream()
                .filter(c -> StringUtils.isNotBlank(c.getId()) && intersectionList.contains(c.getId()))
                .collect(Collectors.toList());
    }

    private List<ProjectTaskSettle> getNewSettles(ProjectTasks oTaskInfo, List<ProjectTaskSettle> taskSettles) {
        if (taskSettles == null || taskSettles.isEmpty()) {
            return new ArrayList<>();
        }

        if (oTaskInfo == null || oTaskInfo.getTaskSettles() == null || oTaskInfo.getTaskSettles().isEmpty()) {
            return taskSettles;
        }

        return taskSettles.stream().filter(c -> StringUtils.isBlank(c.getId())).collect(Collectors.toList());
    }

    public void delByTaskId(String taskId) {
        ProjectTaskSettle query = new ProjectTaskSettle();
        query.setProjectTaskId(taskId);
        List<ProjectTaskSettle> settles = settleMapper.selectProjectTaskSettleList(query);
        
        if (settles != null && !settles.isEmpty()) {
            List<String> ids = settles.stream().map(ProjectTaskSettle::getId).collect(Collectors.toList());
            String idsStr = String.join(",", ids);
            settleMapper.deleteProjectTaskSettleByIds(Convert.toStrArray(idsStr));
        }
    }

}
