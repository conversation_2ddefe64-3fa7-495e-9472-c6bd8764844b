package com.ruoyi.pmrsch.task.service;

import com.ruoyi.pmrsch.task.domain.ProjectTaskSettle;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;

import java.util.List;

/**
 * 项目任务决算Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IProjectTaskSettleService {
    /**
     * 查询项目任务决算
     * 
     * @param id 项目任务决算主键
     * @return 项目任务决算
     */
    public ProjectTaskSettle selectProjectTaskSettleById(String id);

    /**
     * 查询项目任务决算列表
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 项目任务决算集合
     */
    public List<ProjectTaskSettle> selectProjectTaskSettleList(ProjectTaskSettle projectTaskSettle);

    /**
     * 新增项目任务决算
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 结果
     */
    public int insertProjectTaskSettle(ProjectTaskSettle projectTaskSettle);

    /**
     * 修改项目任务决算
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 结果
     */
    public int updateProjectTaskSettle(ProjectTaskSettle projectTaskSettle);

    /**
     * 批量删除项目任务决算
     * 
     * @param ids 需要删除的项目任务决算主键集合
     * @return 结果
     */
    public int deleteProjectTaskSettleByIds(String ids);

    /**
     * 删除项目任务决算信息
     * 
     * @param id 项目任务决算主键
     * @return 结果
     */
    public int deleteProjectTaskSettleById(String id);

    int review(ReviewStatus reviewStatus,String taskId,String userId, String remark);

    int applyEdit(ProjectTasks projectTasks);
}
