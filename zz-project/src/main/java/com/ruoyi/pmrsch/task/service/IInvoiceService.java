package com.ruoyi.pmrsch.task.service;

import java.util.List;

import com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs;

/**
 * 开票记录 服务层
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IInvoiceService {
    /**
     * 查询开票记录信息
     * 
     * @param id 开票记录ID
     * @return 开票记录信息
     */
    ProjectInvoicingLogs selectProjectInvoicingLogsById(String id);

    /**
     * 查询开票记录列表
     * 
     * @param projectInvoicingLogs 开票记录信息
     * @return 开票记录集合
     */
    List<ProjectInvoicingLogs> selectProjectInvoicingLogsList(ProjectInvoicingLogs projectInvoicingLogs);

    /**
     * 新增开票记录
     * 
     * @param projectInvoicingLogs 开票记录信息
     * @return 结果
     */
    int insertProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs);

    /**
     * 修改开票记录
     * 
     * @param projectInvoicingLogs 开票记录信息
     * @return 结果
     */
    int updateProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs);

    /**
     * 删除开票记录信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteProjectInvoicingLogsByIds(String ids);
} 