package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.task.domain.ProjectTaskRespondent;
import com.ruoyi.pmrsch.task.service.IProjectTaskRespondentService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;

/**
 * 项目任务受访者Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/respondent")
public class RespondentController extends BaseController{
    private String prefix = "task/respondent";

    @Autowired
    private IProjectTaskRespondentService projectTaskRespondentService;

    @RequiresPermissions("task:respondent:view")
    @GetMapping()
    public String respondent(String projectTaskId,Integer isView,ModelMap mmap){
        mmap.put("projectTaskId", projectTaskId);
        if(Objects.equals(isView,1)){
            return prefix + "/respondentView";
        }else {
            return prefix + "/respondent";
        }
    }

    /**
     * 受访者查询页面
     */
    @RequiresPermissions("task:respondent:query")
    @GetMapping("/query")
    public String query(){
        return prefix + "/query";
    }

    /**
     * 查询项目任务受访者列表
     */
    @RequiresPermissions("task:respondent:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectTaskRespondent projectTaskRespondent){
        startPage();
        List<ProjectTaskRespondent> list = projectTaskRespondentService.selectProjectTaskRespondentList(projectTaskRespondent);
        return getDataTable(list);
    }

    /**
     * 导出项目任务受访者列表
     */
    @RequiresPermissions("task:respondent:export")
    @Log(title = "项目任务受访者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectTaskRespondent projectTaskRespondent){
        List<ProjectTaskRespondent> list = projectTaskRespondentService.selectProjectTaskRespondentList(projectTaskRespondent);
        ExcelUtil<ProjectTaskRespondent> util = new ExcelUtil<ProjectTaskRespondent>(ProjectTaskRespondent.class);
        return util.exportExcel(list, "项目任务受访者数据");
    }
    
    /**
     * 下载导入模板
     */
    @RequiresPermissions("task:respondent:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<ProjectTaskRespondent> util = new ExcelUtil<ProjectTaskRespondent>(ProjectTaskRespondent.class);
        return util.importTemplateExcel("受访者数据");
    }

    /**
     * 导入数据
     */
    @RequiresPermissions("task:respondent:import")
    @Log(title = "项目任务受访者", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport, String projectTaskId) throws Exception
    {
        ExcelUtil<ProjectTaskRespondent> util = new ExcelUtil<ProjectTaskRespondent>(ProjectTaskRespondent.class);
        List<ProjectTaskRespondent> respondentList = util.importExcel(file.getInputStream());
        String message = importRespondent(respondentList, updateSupport, projectTaskId);
        return AjaxResult.success(message);
    }
    
    /**
     * 导入受访者数据
     * 
     * @param respondentList 受访者数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param projectTaskId 项目任务ID
     * @return 结果
     */
    public String importRespondent(List<ProjectTaskRespondent> respondentList, Boolean isUpdateSupport, String projectTaskId)
    {
        if (StringUtils.isNull(respondentList) || respondentList.size() == 0)
        {
            throw new ServiceException("导入受访者数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProjectTaskRespondent respondent : respondentList)
        {
            try
            {
                // 设置项目任务ID
                respondent.setProjectTaskId(projectTaskId);
                
                // 验证是否存在这条受访者记录
                ProjectTaskRespondent queryRespondent = new ProjectTaskRespondent();
                queryRespondent.setName(respondent.getName());
                queryRespondent.setPhone(respondent.getPhone());
                List<ProjectTaskRespondent> existRespondentList = projectTaskRespondentService.selectProjectTaskRespondentList(queryRespondent);
                
                if (existRespondentList == null || existRespondentList.isEmpty())
                {
                    respondent.setId(RandomStringUtil.gen18UUID());
                    projectTaskRespondentService.insertProjectTaskRespondent(respondent);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、受访者 " + respondent.getName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    ProjectTaskRespondent existRespondent = existRespondentList.get(0);
                    respondent.setId(existRespondent.getId());
                    projectTaskRespondentService.updateProjectTaskRespondent(respondent);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、受访者 " + respondent.getName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、受访者 " + respondent.getName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、受访者 " + respondent.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 新增项目任务受访者
     */
    @GetMapping("/add")
    public String add(String projectTaskId,ModelMap mmap){
        mmap.put("projectTaskId", projectTaskId);
        return prefix + "/add";
    }

    /**
     * 新增保存项目任务受访者
     */
    @RequiresPermissions("task:respondent:add")
    @Log(title = "项目任务受访者", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectTaskRespondent projectTaskRespondent){
        projectTaskRespondent.setId(RandomStringUtil.gen18UUID());
        return toAjax(projectTaskRespondentService.insertProjectTaskRespondent(projectTaskRespondent));
    }

    /**
     * 修改项目任务受访者
     */
    @RequiresPermissions("task:respondent:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskRespondent projectTaskRespondent = projectTaskRespondentService.selectProjectTaskRespondentById(id);
        mmap.put("projectTaskRespondent", projectTaskRespondent);
        return prefix + "/edit";
    }

    /**
     * 修改保存项目任务受访者
     */
    @RequiresPermissions("task:respondent:edit")
    @Log(title = "项目任务受访者", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectTaskRespondent projectTaskRespondent){
        return toAjax(projectTaskRespondentService.updateProjectTaskRespondent(projectTaskRespondent));
    }

    /**
     * 删除项目任务受访者
     */
    @RequiresPermissions("task:respondent:remove")
    @Log(title = "项目任务受访者", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(projectTaskRespondentService.deleteProjectTaskRespondentByIds(ids));
    }
}
