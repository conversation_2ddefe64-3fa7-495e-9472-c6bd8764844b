package com.ruoyi.pmrsch.task.entity;/**
 * <AUTHOR>
 * @Date 2025/2/27
 */

import lombok.Data;

import java.util.List;

/**
 * 描述
 * @className TotalInfo
 * <AUTHOR>
 * @date 2025年02月27日 13:57
 */
@Data
public class TaskTotal {
    private String id;
    private String taskName;
    private String taskStatus;

    private Integer budgetNum;
    private long budgetNumWaitSubmit;
    private long budgetNumReviewing;
    private long budgetNumPass;
    private long budgetNumReject;

    private Integer loansNum;
    private long loansNumWaitSubmit;
    private long loansNumReviewing;
    private long loansNumPass;
    private long loansNumReject;

    private String totalLoans;
    private String contractTotal;
    private String budgetTotal;
    private String budgetRatio;
    private String costRatio;

    private Boolean btnBudget;
    private Boolean btnCommitTask;
    private Boolean btnEditTask;
    private Boolean btnDelTask;

    private Boolean btnApplySettle;
    private Boolean btnCommitSettle;
    private Boolean btnLabor;
    private Boolean btnRespondent;
    private Integer applyStatusCode;
    private Integer settleStatusCode;
    private Boolean isBudgetPass;

}
