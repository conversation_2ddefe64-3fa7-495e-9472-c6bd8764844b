package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.task.domain.ProjectTaskSample;

import java.util.List;

/**
 * 项目任务样本Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskSampleMapper {
    /**
     * 查询项目任务样本
     * 
     * @param id 项目任务样本主键
     * @return 项目任务样本
     */
    public ProjectTaskSample selectProjectTaskSampleById(String id);

    /**
     * 查询项目任务样本列表
     * 
     * @param projectTaskSample 项目任务样本
     * @return 项目任务样本集合
     */
    public List<ProjectTaskSample> selectProjectTaskSampleList(ProjectTaskSample projectTaskSample);

    /**
     * 新增项目任务样本
     * 
     * @param projectTaskSample 项目任务样本
     * @return 结果
     */
    public int insertProjectTaskSample(ProjectTaskSample projectTaskSample);

    /**
     * 修改项目任务样本
     * 
     * @param projectTaskSample 项目任务样本
     * @return 结果
     */
    public int updateProjectTaskSample(ProjectTaskSample projectTaskSample);

    /**
     * 删除项目任务样本
     * 
     * @param id 项目任务样本主键
     * @return 结果
     */
    public int deleteProjectTaskSampleById(String id);

    /**
     * 批量删除项目任务样本
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskSampleByIds(String[] ids);
}
