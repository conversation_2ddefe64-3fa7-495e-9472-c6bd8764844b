package com.ruoyi.pmrsch.task.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;


/**
 * 项目任务劳务费对象 zz_project_task_labor
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectTaskLabor extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    private String projectTaskId;

    /** 项目编号(查询字段) */
    @Excel(name = "项目编号")
    private String projectNo;

    /** 项目任务名称(查询字段) */
    @Excel(name = "项目[任务]")
    private String projectTaskName;
    
    /** 督导姓名(查询字段) */
    @Excel(name = "督导")
    private String supervisorName;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 工作职位 */
    @Excel(name = "工作职位")
    private String job;

    /** 工作天数(有效份数) */
    @Excel(name = "工作天数(有效份数)")
    private Double workDays;

    /** 收款人姓名 */
    @Excel(name = "收款人姓名")
    private String recipientName;

    /** 单价 */
    @Excel(name = "单价")
    private Double laborPrice;

    /** 加班补贴 */
    @Excel(name = "加班补贴")
    private Double overtimeAmount;

    /** 税点 */
    @Excel(name = "税点")
    private Double taxTate;

    /** 应发 */
    @Excel(name = "应发")
    private Double amountPayable;

    /** 实发 */
    @Excel(name = "实发")
    private Double amountPaid;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private Double paymentAmount;

    /** 支付时间 */
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paymentTime;

    /** 付款账号 */
    @Excel(name = "付款账号")
    private String paymentAccount;

    /** 付款说明 */
    @Excel(name = "付款说明")
    private String paymentDescription;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectTaskName() {
        return projectTaskName;
    }

    public void setProjectTaskName(String projectTaskName) {
        this.projectTaskName = projectTaskName;
    }

    public String getSupervisorName() {
        return supervisorName;
    }

    public void setSupervisorName(String supervisorName) {
        this.supervisorName = supervisorName;
    }
    
    /** 关联的项目信息 */
    private ProjectInfo projectInfo;
    
    /** 关联的任务信息 */
    private ProjectTasks projectTask;
    
    /** 督导人员 */
    private SysUser supervisor;

    /** 决算状态(查询字段) */
    private Integer settleStatusCode;

    public Integer getSettleStatusCode() {
        return settleStatusCode;
    }

    public void setSettleStatusCode(Integer settleStatusCode) {
        this.settleStatusCode = settleStatusCode;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getJob() {
        return job;
    }

    public void setWorkDays(Double workDays) {
        this.workDays = workDays;
    }

    public Double getWorkDays() {
        return workDays;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setLaborPrice(Double laborPrice) {
        this.laborPrice = laborPrice;
    }

    public Double getLaborPrice() {
        return laborPrice;
    }

    public void setOvertimeAmount(Double overtimeAmount) {
        this.overtimeAmount = overtimeAmount;
    }

    public Double getOvertimeAmount() {
        return overtimeAmount;
    }

    public void setTaxTate(Double taxTate) {
        this.taxTate = taxTate;
    }

    public Double getTaxTate() {
        return taxTate;
    }

    public void setAmountPayable(Double amountPayable) {
        this.amountPayable = amountPayable;
    }

    public Double getAmountPayable() {
        return amountPayable;
    }

    public void setAmountPaid(Double amountPaid) {
        this.amountPaid = amountPaid;
    }

    public Double getAmountPaid() {
        return amountPaid;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentTime(Date paymentTime) {
        this.paymentTime = paymentTime;
    }

    public Date getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentAccount(String paymentAccount) {
        this.paymentAccount = paymentAccount;
    }

    public String getPaymentAccount() {
        return paymentAccount;
    }

    public void setPaymentDescription(String paymentDescription) {
        this.paymentDescription = paymentDescription;
    }

    public String getPaymentDescription() {
        return paymentDescription;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }
    
    public ProjectInfo getProjectInfo() {
        return projectInfo;
    }

    public void setProjectInfo(ProjectInfo projectInfo) {
        this.projectInfo = projectInfo;
    }

    public ProjectTasks getProjectTask() {
        return projectTask;
    }

    public void setProjectTask(ProjectTasks projectTask) {
        this.projectTask = projectTask;
    }

    public SysUser getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(SysUser supervisor) {
        this.supervisor = supervisor;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("name", getName())
            .append("job", getJob())
            .append("workDays", getWorkDays())
            .append("recipientName", getRecipientName())
            .append("laborPrice", getLaborPrice())
            .append("overtimeAmount", getOvertimeAmount())
            .append("taxTate", getTaxTate())
            .append("amountPayable", getAmountPayable())
            .append("amountPaid", getAmountPaid())
            .append("paymentAmount", getPaymentAmount())
            .append("paymentTime", getPaymentTime())
            .append("paymentAccount", getPaymentAccount())
            .append("paymentDescription", getPaymentDescription())
            .append("contactPhone", getContactPhone())
            .append("remarks", getRemarks())
            .toString();
    }
}
