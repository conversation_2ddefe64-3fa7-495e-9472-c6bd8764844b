package com.ruoyi.pmrsch.task.budget.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 项目任务借款对象 zz_project_task_loans
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectTaskLoans extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    @Excel(name = "项目任务 ID")
    private String projectTaskId;

    /** 项目任务预算 ID */
    @Excel(name = "项目任务预算 ID")
    private String projectTaskBudgetId;

    /** 借款金额 */
    @Excel(name = "借款金额")
    private Double amount;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicant;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private Integer loansStatusCode;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;
    
    /** 附件文件ID */
    @Excel(name = "附件文件ID")
    private String fileId;
    
    /** 附件文件URL */
    private String fileUrl;
    
    /** 附件文件名称 */
    private String fileName;
    
    /** 是否打款 */
    @Excel(name = "是否打款")
    private Boolean isPayment;
    
    /** 打款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "打款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;
    
    /** 打款备注 */
    @Excel(name = "打款备注")
    private String paymentRemarks;

    private SysUser applicantUser;
    private ProjectTaskBudget taskBudget;
    private ProjectTasks projectTasks;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setProjectTaskBudgetId(String projectTaskBudgetId) {
        this.projectTaskBudgetId = projectTaskBudgetId;
    }

    public String getProjectTaskBudgetId() {
        return projectTaskBudgetId;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getAmount() {
        return amount;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setLoansStatusCode(Integer loansStatusCode) {
        this.loansStatusCode = loansStatusCode;
    }

    public Integer getLoansStatusCode() {
        return loansStatusCode;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }
    
    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }
    
    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Boolean getIsPayment() {
        return isPayment;
    }

    public void setIsPayment(Boolean isPayment) {
        this.isPayment = isPayment;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentRemarks() {
        return paymentRemarks;
    }

    public void setPaymentRemarks(String paymentRemarks) {
        this.paymentRemarks = paymentRemarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("projectTaskBudgetId", getProjectTaskBudgetId())
            .append("amount", getAmount())
            .append("createTime", getCreateTime())
            .append("applicant", getApplicant())
            .append("applyTime", getApplyTime())
            .append("loansStatusCode", getLoansStatusCode())
            .append("remarks", getRemarks())
            .append("fileId", getFileId())
            .append("fileUrl", getFileUrl())
            .append("fileName", getFileName())
            .append("isPayment", getIsPayment())
            .append("paymentDate", getPaymentDate())
            .append("paymentRemarks", getPaymentRemarks())
            .toString();
    }

    public SysUser getApplicantUser() {
        return applicantUser;
    }

    public void setApplicantUser(SysUser applicantUser) {
        this.applicantUser = applicantUser;
    }

    public ProjectTaskBudget getTaskBudget() {
        return taskBudget;
    }

    public void setTaskBudget(ProjectTaskBudget taskBudget) {
        this.taskBudget = taskBudget;
    }

    public ProjectTasks getProjectTasks() {
        return projectTasks;
    }

    public void setProjectTasks(ProjectTasks projectTasks) {
        this.projectTasks = projectTasks;
    }
}
