package com.ruoyi.pmrsch.task.service;

import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;

import java.util.List;

/**
 * 项目任务预算Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IProjectTaskBudgetService {
    /**
     * 查询项目任务预算
     * 
     * @param id 项目任务预算主键
     * @return 项目任务预算
     */
    public ProjectTaskBudget selectProjectTaskBudgetById(String id);

    /**
     * 查询项目任务预算列表
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 项目任务预算集合
     */
    public List<ProjectTaskBudget> selectProjectTaskBudgetList(ProjectTaskBudget projectTaskBudget);

    /**
     * 新增项目任务预算
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 结果
     */
    public int apply(ProjectTaskBudget projectTaskBudget);

    /**
     * 修改项目任务预算
     * 
     * @param projectTaskBudget 项目任务预算
     * @return 结果
     */
    public int applyEdit(ProjectTaskBudget projectTaskBudget);

    /**
     * 批量删除项目任务预算
     * 
     * @param ids 需要删除的项目任务预算主键集合
     * @return 结果
     */
    public int deleteProjectTaskBudgetByIds(String ids);

    /**
     * 删除项目任务预算信息
     * 
     * @param id 项目任务预算主键
     * @return 结果
     */
    public int deleteProjectTaskBudgetById(String id);

    int review(ReviewStatus reviewStatus, String budgetId, String userId, String remark);
}
