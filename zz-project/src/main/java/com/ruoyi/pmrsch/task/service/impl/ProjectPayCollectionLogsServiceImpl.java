package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.task.domain.ProjectPayCollectionLogs;
import com.ruoyi.pmrsch.task.mapper.ProjectPayCollectionLogsMapper;
import com.ruoyi.pmrsch.task.service.IProjectPayCollectionLogsService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 收款记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectPayCollectionLogsServiceImpl implements IProjectPayCollectionLogsService {
    @Autowired
    private ProjectPayCollectionLogsMapper projectPayCollectionLogsMapper;

    /**
     * 查询收款记录
     * 
     * @param id 收款记录主键
     * @return 收款记录
     */
    @Override
    public ProjectPayCollectionLogs selectProjectPayCollectionLogsById(String id){
        return projectPayCollectionLogsMapper.selectProjectPayCollectionLogsById(id);
    }

    /**
     * 查询收款记录列表
     * 
     * @param projectPayCollectionLogs 收款记录
     * @return 收款记录
     */
    @Override
    public List<ProjectPayCollectionLogs> selectProjectPayCollectionLogsList(ProjectPayCollectionLogs projectPayCollectionLogs){
        return projectPayCollectionLogsMapper.selectProjectPayCollectionLogsList(projectPayCollectionLogs);
    }

    /**
     * 新增收款记录
     * 
     * @param projectPayCollectionLogs 收款记录
     * @return 结果
     */
    @Override
    public int insertProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs){
        projectPayCollectionLogs.setId(RandomStringUtil.gen18UUID());
        projectPayCollectionLogs.setCreatedAt(new Date());
        return projectPayCollectionLogsMapper.insertProjectPayCollectionLogs(projectPayCollectionLogs);
    }

    /**
     * 修改收款记录
     * 
     * @param projectPayCollectionLogs 收款记录
     * @return 结果
     */
    @Override
    public int updateProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs){
        return projectPayCollectionLogsMapper.updateProjectPayCollectionLogs(projectPayCollectionLogs);
    }

    /**
     * 批量删除收款记录
     * 
     * @param ids 需要删除的收款记录主键
     * @return 结果
     */
    @Override
    public int deleteProjectPayCollectionLogsByIds(String ids){
        return projectPayCollectionLogsMapper.deleteProjectPayCollectionLogsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除收款记录信息
     * 
     * @param id 收款记录主键
     * @return 结果
     */
    @Override
    public int deleteProjectPayCollectionLogsById(String id){
        return projectPayCollectionLogsMapper.deleteProjectPayCollectionLogsById(id);
    }
}
