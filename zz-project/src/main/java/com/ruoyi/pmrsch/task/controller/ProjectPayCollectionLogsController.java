package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.task.domain.ProjectPayCollectionLogs;
import com.ruoyi.pmrsch.task.service.IProjectPayCollectionLogsService;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收款记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/paycollectlogs")
public class ProjectPayCollectionLogsController extends BaseController{
    private String prefix = "task/paycollectlogs";

    @Autowired
    private IProjectPayCollectionLogsService projectPayCollectionLogsService;

    @RequiresPermissions("task:paycollectlogs:view")
    @GetMapping()
    public String paycollectlogs(String projectTaskId,ModelMap mmap){
        mmap.put("projectTaskId", projectTaskId);
        return prefix + "/paycollectlogs";
    }

    /**
     * 查询收款记录列表
     */
    @RequiresPermissions("task:paycollectlogs:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectPayCollectionLogs projectPayCollectionLogs){
        startPage();
        List<ProjectPayCollectionLogs> list = projectPayCollectionLogsService.selectProjectPayCollectionLogsList(projectPayCollectionLogs);
        return getDataTable(list);
    }

    /**
     * 导出收款记录列表
     */
    @RequiresPermissions("task:paycollectlogs:export")
    @Log(title = "收款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectPayCollectionLogs projectPayCollectionLogs){
        List<ProjectPayCollectionLogs> list = projectPayCollectionLogsService.selectProjectPayCollectionLogsList(projectPayCollectionLogs);
        ExcelUtil<ProjectPayCollectionLogs> util = new ExcelUtil<ProjectPayCollectionLogs>(ProjectPayCollectionLogs.class);
        return util.exportExcel(list, "收款记录数据");
    }

    /**
     * 新增收款记录
     */
    @GetMapping("/add")
    public String add(String projectTaskId,ModelMap mmap){
        mmap.put("projectTaskId", projectTaskId);
        return prefix + "/add";
    }

    /**
     * 新增保存收款记录
     */
    @RequiresPermissions("task:paycollectlogs:add")
    @Log(title = "收款记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectPayCollectionLogs projectPayCollectionLogs){
        return toAjax(projectPayCollectionLogsService.insertProjectPayCollectionLogs(projectPayCollectionLogs));
    }

    /**
     * 修改收款记录
     */
    @RequiresPermissions("task:paycollectlogs:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        ProjectPayCollectionLogs projectPayCollectionLogs = projectPayCollectionLogsService.selectProjectPayCollectionLogsById(id);
        mmap.put("projectPayCollectionLogs", projectPayCollectionLogs);
        return prefix + "/edit";
    }

    /**
     * 修改保存收款记录
     */
    @RequiresPermissions("task:paycollectlogs:edit")
    @Log(title = "收款记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectPayCollectionLogs projectPayCollectionLogs){
        return toAjax(projectPayCollectionLogsService.updateProjectPayCollectionLogs(projectPayCollectionLogs));
    }

    /**
     * 删除收款记录
     */
    @RequiresPermissions("task:paycollectlogs:remove")
    @Log(title = "收款记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(projectPayCollectionLogsService.deleteProjectPayCollectionLogsByIds(ids));
    }
}
