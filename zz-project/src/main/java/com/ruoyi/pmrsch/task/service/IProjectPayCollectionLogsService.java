package com.ruoyi.pmrsch.task.service;

import java.util.List;

import com.ruoyi.pmrsch.task.domain.ProjectPayCollectionLogs;

/**
 * 收款记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IProjectPayCollectionLogsService {
    /**
     * 查询收款记录
     * 
     * @param id 收款记录主键
     * @return 收款记录
     */
    public ProjectPayCollectionLogs selectProjectPayCollectionLogsById(String id);

    /**
     * 查询收款记录列表
     * 
     * @param projectPayCollectionLogs 收款记录
     * @return 收款记录集合
     */
    public List<ProjectPayCollectionLogs> selectProjectPayCollectionLogsList(ProjectPayCollectionLogs projectPayCollectionLogs);

    /**
     * 新增收款记录
     * 
     * @param projectPayCollectionLogs 收款记录
     * @return 结果
     */
    public int insertProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs);

    /**
     * 修改收款记录
     * 
     * @param projectPayCollectionLogs 收款记录
     * @return 结果
     */
    public int updateProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs);

    /**
     * 批量删除收款记录
     * 
     * @param ids 需要删除的收款记录主键集合
     * @return 结果
     */
    public int deleteProjectPayCollectionLogsByIds(String ids);

    /**
     * 删除收款记录信息
     * 
     * @param id 收款记录主键
     * @return 结果
     */
    public int deleteProjectPayCollectionLogsById(String id);
}
