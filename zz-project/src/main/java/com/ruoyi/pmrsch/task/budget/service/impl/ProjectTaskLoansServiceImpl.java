package com.ruoyi.pmrsch.task.budget.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.common.tools.TimeUtil;
import com.ruoyi.pmrsch.file.UploadFilesUtil;
import com.ruoyi.pmrsch.file.dao.model.UploadFiles;
import com.ruoyi.pmrsch.task.budget.domain.ProjectTaskLoans;
import com.ruoyi.pmrsch.task.budget.mapper.ProjectTaskLoansMapper;
import com.ruoyi.pmrsch.task.budget.service.IProjectTaskLoansService;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.entity.ReviewBType;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.manager.ReviewLogsManager;
import com.ruoyi.pmrsch.task.mapper.ProjectTaskBudgetMapper;
import com.ruoyi.pmrsch.task.service.IProjectTaskBudgetService;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目任务借款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ProjectTaskLoansServiceImpl implements IProjectTaskLoansService {
    @Autowired
    private ProjectTaskLoansMapper projectTaskLoansMapper;
    @Autowired
    private ReviewLogsManager reviewLogsManager;
    @Autowired
    private ProjectTaskBudgetMapper budgetMapper;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IProjectTasksService projectTasksService;
    @Autowired
    private IProjectTaskBudgetService budgetService;

    /**
     * 查询项目任务借款
     * 
     * @param id 项目任务借款主键
     * @return 项目任务借款
     */
    @Override
    public ProjectTaskLoans selectProjectTaskLoansById(String id){
        ProjectTaskLoans taskLoans = projectTaskLoansMapper.selectProjectTaskLoansById(id);
        supplement(taskLoans);
        return taskLoans;
    }

    /**
     * 查询项目任务借款列表
     * 
     * @param projectTaskLoans 项目任务借款
     * @return 项目任务借款
     */
    @DataScope(deptAlias = "task", userAlias = "task")
    @Override
    public List<ProjectTaskLoans> selectProjectTaskLoansList(ProjectTaskLoans projectTaskLoans){
        List<ProjectTaskLoans> taskLoans = projectTaskLoansMapper.selectProjectTaskLoansList(projectTaskLoans);
        supplement(taskLoans);
        return taskLoans;
    }

    /**
     * 新增项目任务借款
     * 
     * @param projectTaskLoans 项目任务借款
     * @return 结果
     */
    @Override
    public int applyAdd(ProjectTaskLoans projectTaskLoans){
        ParamUtil.checkObjParam(projectTaskLoans,"projectTaskLoans");
        ParamUtil.checkStrParam(projectTaskLoans.getProjectTaskBudgetId(),"缺少预算信息");

        projectTaskLoans.setId(RandomStringUtil.gen18UUID());
        projectTaskLoans.setLoansStatusCode(ReviewStatus.待提审.getValue());
        projectTaskLoans.setApplicant(ShiroUtils.getUserId()+"");
        projectTaskLoans.setApplyTime(TimeUtil.getCurrentDate());
        projectTaskLoans.setCreateTime(DateUtils.getNowDate());

        ProjectTaskBudget budget = ParamUtil.requireNotNull(budgetMapper.selectProjectTaskBudgetById(projectTaskLoans.getProjectTaskBudgetId()),"找不到预算信息");
        projectTaskLoans.setProjectTaskId(budget.getProjectTaskId());

        return projectTaskLoansMapper.insertProjectTaskLoans(projectTaskLoans);
    }

    /**
     * 修改项目任务借款
     * 
     * @param projectTaskLoans 项目任务借款
     * @return 结果
     */
    @Override
    public int applyEdit(ProjectTaskLoans projectTaskLoans){

        ProjectTaskLoans oTaskLoans = ParamUtil.requireNotNull(selectProjectTaskLoansById(projectTaskLoans.getId()),"找不到借款信息。id:" + projectTaskLoans.getId());
        
        List<ReviewStatus> allowStatus = Arrays.asList(ReviewStatus.待提审,ReviewStatus.拒绝);
        ReviewStatus curStatus = ReviewStatus.getByValue(oTaskLoans.getLoansStatusCode());

        if(Boolean.TRUE.equals(oTaskLoans.getIsPayment())){
            throw new ApiException("已打款状态不允许修改",ApiException.BAD_REQUEST);
        }

        //允许编辑
        boolean isEditable = allowStatus.contains(curStatus);
        //打款操作
        boolean isPay = Boolean.TRUE.equals(projectTaskLoans.getIsPayment());
        //是否审核通过
        boolean isPass = ReviewStatus.老板审核.equals(curStatus);
        
        //打款操作检查
        if(isPay && !isPass){
            throw new ApiException("未审核通过状态不允许打款",ApiException.BAD_REQUEST);
        }

        //编辑操作检查
        if(!isPay && !isEditable){
            throw new ApiException(curStatus.name()+"状态不允许修改",ApiException.BAD_REQUEST);
        }

        return projectTaskLoansMapper.updateProjectTaskLoans(projectTaskLoans);
    }

    /**
     * 批量删除项目任务借款
     * 
     * @param ids 需要删除的项目任务借款主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskLoansByIds(String ids){
        return projectTaskLoansMapper.deleteProjectTaskLoansByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除项目任务借款信息
     * 
     * @param id 项目任务借款主键
     * @return 结果
     */
    @Override
    public int deleteProjectTaskLoansById(String id){
        return projectTaskLoansMapper.deleteProjectTaskLoansById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int review(ReviewStatus reviewStatus, String loansId, String userId, String remark) {
        ParamUtil.checkObjParam(reviewStatus,"applyStatus");
        ParamUtil.checkStrParam(loansId,"loansId");
        ParamUtil.checkStrParam(userId,"userId");

        ProjectTaskLoans projectTaskLoans = ParamUtil.requireNotNull(this.selectProjectTaskLoansById(loansId),"找不到预算信息.loansId:"+loansId);
        ReviewStatus curStatus = ReviewStatus.getByValue(Optional.ofNullable(projectTaskLoans.getLoansStatusCode()).orElse(ReviewStatus.待提审.getValue()));

        List<ReviewStatus> allowStatus = null;
        switch (reviewStatus){
            case 提审:
                allowStatus = Arrays.asList(ReviewStatus.待提审,ReviewStatus.拒绝);
                break;
            case 主管审核:
                allowStatus = Arrays.asList(ReviewStatus.提审);
                break;
            case 财务审核:
                allowStatus = Arrays.asList(ReviewStatus.主管审核);
                break;
            case 老板审核:
                allowStatus = Arrays.asList(ReviewStatus.财务审核);
                break;
            case 取消:
                allowStatus = Arrays.asList(ReviewStatus.待提审);
                break;
            case 拒绝:
                allowStatus = Arrays.asList(ReviewStatus.提审,ReviewStatus.主管审核,ReviewStatus.财务审核);
                break;
            default:
                throw new ApiException("操作状态不正确",ApiException.BAD_REQUEST);
        }

        if(!allowStatus.contains(curStatus)){
            throw new ApiException("当前状态不可进行"+reviewStatus.name()+"操作",ApiException.BAD_REQUEST);
        }
 
        projectTaskLoans.setLoansStatusCode(reviewStatus.getValue());
        if(ReviewStatus.提审.equals(reviewStatus)) {
            projectTaskLoans.setApplyTime(TimeUtil.getCurrentDate());
            Double totalBudget = projectTaskLoans.getTaskBudget().getTotalBudget();
            Double totalLoans = projectTaskLoans.getTaskBudget().getTotalLoans() + projectTaskLoans.getAmount();

            if(totalLoans > totalBudget){
                throw new ApiException(String.format("借款不能大于预算总额。预算总额:%s,借款总额:%s", totalBudget,totalLoans),ApiException.BAD_REQUEST);
            }
        }

        reviewLogsManager.inserLog(ReviewBType.预算,loansId,userId,reviewStatus.getValue(),remark);
        return projectTaskLoansMapper.updateProjectTaskLoans(projectTaskLoans);
    }

    private void supplement(ProjectTaskLoans taskLoans) {
        if(taskLoans == null){
            return;
        }

        if(StringUtils.isNotBlank(taskLoans.getFileId())){
            UploadFiles fileInfo = UploadFilesUtil.getFileInfo(taskLoans.getFileId());
            if(fileInfo != null){
                taskLoans.setFileUrl(UploadFilesUtil.getFileUrl(taskLoans.getFileId()));
                taskLoans.setFileName(fileInfo.getFilename());
            }
        }

        taskLoans.setApplicantUser(StringUtils.isNotBlank(taskLoans.getApplicant()) ? userService.selectUserById(Long.parseLong(taskLoans.getApplicant())) : null);
        taskLoans.setProjectTasks(projectTasksService.selectProjectTasksById(taskLoans.getProjectTaskId()));
        taskLoans.setTaskBudget(budgetService.selectProjectTaskBudgetById(taskLoans.getProjectTaskBudgetId()));
    }

    private void supplement(List<ProjectTaskLoans> taskLoansList) {
        if(taskLoansList == null || taskLoansList.isEmpty()){
            return;
        }

        for(ProjectTaskLoans tasks:taskLoansList){
            supplement(tasks);
        }
    }

    public void delByTaskId(String taskId) {
        ProjectTaskLoans query = new ProjectTaskLoans();
        query.setProjectTaskId(taskId);
        List<ProjectTaskLoans> loansList = projectTaskLoansMapper.selectProjectTaskLoansList(query);
        
        if (loansList != null && !loansList.isEmpty()) {
            List<String> ids = loansList.stream().map(ProjectTaskLoans::getId).collect(Collectors.toList());
            String idsStr = String.join(",", ids);
            projectTaskLoansMapper.deleteProjectTaskLoansByIds(Convert.toStrArray(idsStr));
        }
    }
}
