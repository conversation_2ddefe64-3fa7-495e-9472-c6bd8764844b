package com.ruoyi.pmrsch.task.mapper;

import com.ruoyi.pmrsch.task.domain.ProjectTaskSettle;

import java.util.List;

/**
 * 项目任务决算Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ProjectTaskSettleMapper {
    /**
     * 查询项目任务决算
     * 
     * @param id 项目任务决算主键
     * @return 项目任务决算
     */
    public ProjectTaskSettle selectProjectTaskSettleById(String id);

    /**
     * 查询项目任务决算列表
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 项目任务决算集合
     */
    public List<ProjectTaskSettle> selectProjectTaskSettleList(ProjectTaskSettle projectTaskSettle);

    /**
     * 新增项目任务决算
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 结果
     */
    public int insertProjectTaskSettle(ProjectTaskSettle projectTaskSettle);

    /**
     * 修改项目任务决算
     * 
     * @param projectTaskSettle 项目任务决算
     * @return 结果
     */
    public int updateProjectTaskSettle(ProjectTaskSettle projectTaskSettle);

    /**
     * 删除项目任务决算
     * 
     * @param id 项目任务决算主键
     * @return 结果
     */
    public int deleteProjectTaskSettleById(String id);

    /**
     * 批量删除项目任务决算
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectTaskSettleByIds(String[] ids);
}
