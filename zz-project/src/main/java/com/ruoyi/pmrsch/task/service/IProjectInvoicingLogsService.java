package com.ruoyi.pmrsch.task.service;

import java.util.List;

import com.ruoyi.pmrsch.task.domain.ProjectInvoicingLogs;

/**
 * 开票记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IProjectInvoicingLogsService {
    /**
     * 查询开票记录
     * 
     * @param id 开票记录主键
     * @return 开票记录
     */
    public ProjectInvoicingLogs selectProjectInvoicingLogsById(String id);

    /**
     * 查询开票记录列表
     * 
     * @param projectInvoicingLogs 开票记录
     * @return 开票记录集合
     */
    public List<ProjectInvoicingLogs> selectProjectInvoicingLogsList(ProjectInvoicingLogs projectInvoicingLogs);

    /**
     * 新增开票记录
     * 
     * @param projectInvoicingLogs 开票记录
     * @return 结果
     */
    public int insertProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs);

    /**
     * 修改开票记录
     * 
     * @param projectInvoicingLogs 开票记录
     * @return 结果
     */
    public int updateProjectInvoicingLogs(ProjectInvoicingLogs projectInvoicingLogs);

    /**
     * 批量删除开票记录
     * 
     * @param ids 需要删除的开票记录主键集合
     * @return 结果
     */
    public int deleteProjectInvoicingLogsByIds(String ids);

    /**
     * 删除开票记录信息
     * 
     * @param id 开票记录主键
     * @return 结果
     */
    public int deleteProjectInvoicingLogsById(String id);
}
