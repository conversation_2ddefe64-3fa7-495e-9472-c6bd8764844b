package com.ruoyi.pmrsch.task.domain;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.domain.ExpertInfo;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;

import lombok.Data;

/**
 * 专家咨询记录对象 zz_expert_consultation
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
public class ExpertConsultation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;

    /** 项目任务ID */
    private String projectTaskId;

    /** 咨询日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "咨询日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date consultationDate;

    /** 咨询时间 */
    @Excel(name = "咨询时间", width = 30)
    private Time consultationTime;

    /** 所属项目ID */
    private String projectInfoId;

    /** 专家信息ID */
    private String expertInfoId;

    /** 费用类型 code */
    @Excel(name = "费用类型 code")
    private String sampleTypeCode;

    /** Excel导入用-项目编号 */
    @Excel(name = "项目编号")
    private String projectNo;

    /** Excel导入用-专家姓名 */
    @Excel(name = "专家姓名")
    private String expertName;

    /** Excel导入用-专家手机号 */
    @Excel(name = "专家手机号")
    private String expertPhone;

    /** Excel导入用-客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 定量数量或访谈时长(min) */
    @Excel(name = "定量数量或访谈时长(min)")
    private Long consultationDuration;

    /** 单价(RMB) */
    @Excel(name = "单价(RMB)")
    private BigDecimal unitPrice;

    /** 渠道 */
    @Excel(name = "渠道")
    private String channel;

    /** 渠道报价(RMB) */
    @Excel(name = "渠道报价(RMB)")
    private BigDecimal channelPrice;

    /** 问题 */
    @Excel(name = "问题")
    private String question;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系人邮箱 */
    @Excel(name = "联系人邮箱")
    private String contactEmail;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 开票状态 */
    @Excel(name = "开票状态")
    private Long invoiceStatusCode;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private Long deptId;

    private ProjectInfo projectInfo;
    private Customer customer;
    private ExpertInfo expertInfo;
    private ProjectTasks projectTask;

    /** 督导名称 */
    private String supervisorName;

    /** 项目类型（label） */
    private String projectTypeLabel;

    /** 专家所属公司 */
    private String expertCompanyName;

    /**
     * 获取渠道成本
     */
    public BigDecimal getChannelCost() {
        if (channelPrice != null && consultationDuration != null) {
            if(Objects.equals(getSampleTypeCode(), "0")){
                return channelPrice.divide(BigDecimal.valueOf(60), 5, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(consultationDuration));
            }else{
                return channelPrice.multiply(BigDecimal.valueOf(consultationDuration));
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 定性需要/60,其它直接取
     * @return
     */
    public Double getQuantity() {
        if(consultationDuration == null){
            return 0.0;
        }

        if(Objects.equals(getSampleTypeCode(), "0")){
            return BigDecimal.valueOf(consultationDuration).divide(new BigDecimal(60)).doubleValue();
        }else{
            return BigDecimal.valueOf(consultationDuration).doubleValue();
        }
    }

    /**
     * 获取客户费用合计
     */
    public BigDecimal getCustomerTotalCost() {
        if (unitPrice != null && consultationDuration != null) {
            if(Objects.equals(getSampleTypeCode(), "0")){
                return unitPrice.divide(BigDecimal.valueOf(60), 5, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(consultationDuration));
            }else{
                return unitPrice.multiply(BigDecimal.valueOf(consultationDuration));
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取渠道/费用比(%)
     */
    public BigDecimal getChannelCostRatio() {
        BigDecimal channelCost = getChannelCost();
        BigDecimal customerTotalCost = getCustomerTotalCost();

        if (customerTotalCost != null && customerTotalCost.compareTo(BigDecimal.ZERO) != 0) {
            return channelCost.multiply(BigDecimal.valueOf(100))
                    .divide(customerTotalCost, 5, BigDecimal.ROUND_HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    // 为导入功能添加的临时字段 getter/setter
    public String getProjectNo()
    {
        return projectNo;
    }

    public void setProjectNo(String projectNo)
    {
        this.projectNo = projectNo;
    }

    public String getExpertName()
    {
        return expertName;
    }

    public void setExpertName(String expertName)
    {
        this.expertName = expertName;
    }

    public String getExpertPhone()
    {
        return expertPhone;
    }

    public void setExpertPhone(String expertPhone)
    {
        this.expertPhone = expertPhone;
    }

    public String getCustomerName()
    {
        return customerName;
    }

    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }

}
