package com.ruoyi.pmrsch.task.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 项目任务决算对象 zz_project_task_settle
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class ProjectTaskSettle extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 项目任务 ID */
    @Excel(name = "项目任务 ID")
    private String projectTaskId;

    /** 费用主类 */
    @Excel(name = "费用主类")
    private String costCategorieMainCode;

    /** 费用子类 */
    @Excel(name = "费用子类")
    private String costCategorieSubCode;

    /** 决算单价 */
    @Excel(name = "决算单价")
    private Double priceSettle;

    /** 决算数量 */
    @Excel(name = "决算数量")
    private Double quantitySettle;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;
    /** 预算单价 */
    @Excel(name = "预算单价")
    private Double priceBudget;

    /** 预算数量 */
    @Excel(name = "预算数量")
    private Double quantityBudget;

    /** 是否来自预算统计 */
    @Excel(name = "是否来自预算统计")
    private Integer isFromBudget;

    public Double getSubtotal(){
        if(quantitySettle == null || priceSettle == null){
            return 0.0;
        }

        return quantitySettle * priceSettle;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProjectTaskId(String projectTaskId) {
        this.projectTaskId = projectTaskId;
    }

    public String getProjectTaskId() {
        return projectTaskId;
    }

    public void setCostCategorieMainCode(String costCategorieMainCode) {
        this.costCategorieMainCode = costCategorieMainCode;
    }

    public String getCostCategorieMainCode() {
        return costCategorieMainCode;
    }

    public void setCostCategorieSubCode(String costCategorieSubCode) {
        this.costCategorieSubCode = costCategorieSubCode;
    }

    public String getCostCategorieSubCode() {
        return costCategorieSubCode;
    }

    public void setPriceSettle(Double priceSettle) {
        this.priceSettle = priceSettle;
    }

    public Double getPriceSettle() {
        return priceSettle;
    }

    public void setQuantitySettle(Double quantitySettle) {
        this.quantitySettle = quantitySettle;
    }

    public Double getQuantitySettle() {
        return quantitySettle;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectTaskId", getProjectTaskId())
            .append("costCategorieMainCode", getCostCategorieMainCode())
            .append("costCategorieSubCode", getCostCategorieSubCode())
            .append("priceSettle", getPriceSettle())
            .append("quantitySettle", getQuantitySettle())
            .append("createTime", getCreateTime())
            .append("remarks", getRemarks())
            .toString();
    }

    public Double getPriceBudget() {
        return priceBudget;
    }

    public void setPriceBudget(Double priceBudget) {
        this.priceBudget = priceBudget;
    }

    public Double getQuantityBudget() {
        return quantityBudget;
    }

    public void setQuantityBudget(Double quantityBudget) {
        this.quantityBudget = quantityBudget;
    }

    public Integer getIsFromBudget() {
        return isFromBudget;
    }

    public void setIsFromBudget(Integer isFromBudget) {
        this.isFromBudget = isFromBudget;
    }
}
