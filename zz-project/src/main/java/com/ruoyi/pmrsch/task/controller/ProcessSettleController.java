package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.common.RequiresAnyPermissions;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.task.domain.ProjectTaskBudget;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.service.IProjectTaskSettleService;
import com.ruoyi.pmrsch.task.service.IProjectTasksService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 项目任务决算Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/settle")
public class ProcessSettleController extends BaseController{
    private String prefix = "task/settle";

    @Autowired
    private IProjectTasksService projectTasksService;
    @Autowired
    private IProjectTaskSettleService taskSettleService;

    /**
     * 申请列表页
     * @return
     */
    @RequiresPermissions("task:settle:apply:view")
    @GetMapping("apply")
    public String appyList(ModelMap mmap){
        mmap.put("review",ReviewStatus.提审.getValue());
        return prefix + "/apply/settle";
    }

    /**
     * 申请列表
     */
    @RequiresPermissions("task:settle:apply:list")
    @PostMapping("apply")
    @ResponseBody
    public TableDataInfo appyList(ProjectTasks projectTasks){
        startPage();
        List<ProjectTasks> list = projectTasksService.selectProjectTasksList(projectTasks);
        return getDataTable(list);
    }

    /**
     * 修改申请页
     */
    @RequiresPermissions("task:settle:apply:edit")
    @GetMapping("apply/edit/{id}")
    public String appyEdit(@PathVariable("id") String id, ModelMap mmap){
        ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(id);
        mmap.put("projectTasks", projectTasks);
        return prefix + "/apply/edit";
    }

    /**
     * 修改申请
     */
    @RequiresPermissions("task:settle:apply:edit")
    @Log(title = "修改决算申请", businessType = BusinessType.UPDATE)
    @PostMapping("apply/edit")
    @ResponseBody
    public AjaxResult applyEdit(ProjectTasks projectTasks){
        return toAjax(taskSettleService.applyEdit(projectTasks));
    }

    /**
     * 导出申请列表
     */
    @RequiresPermissions("task:settle:apply:export")
    @Log(title = "项目阶段任务", businessType = BusinessType.EXPORT)
    @PostMapping("apply/export")
    @ResponseBody
    public AjaxResult applyExport(ProjectTasks projectTasks){
        List<ProjectTasks> list = projectTasksService.selectProjectTasksList(projectTasks);
        ExcelUtil<ProjectTasks> util = new ExcelUtil<ProjectTasks>(ProjectTasks.class);
        return util.exportExcel(list, "项目阶段任务数据");
    }

    /**
     * 申请审批(主管)列表页
     * @return
     */
    @RequiresPermissions("task:settle:review:manager")
    @GetMapping("review/manager")
    public String reviewManagerList(ModelMap mmap){
        mmap.put("review",ReviewStatus.主管审核.getValue());
        mmap.put("filterStatus", ReviewStatus.提审.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 申请审批(财务)列表页
     * @return
     */
    @RequiresPermissions("task:settle:review:finance")
    @GetMapping("review/finance")
    public String reviewFinanceList(ModelMap mmap){
        mmap.put("review",ReviewStatus.财务审核.getValue());
        mmap.put("filterStatus",ReviewStatus.主管审核.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 申请审批(老板)列表页
     * @return
     */
    @RequiresPermissions("task:settle:review:ceo")
    @GetMapping("review/ceo")
    public String reviewCeoList(ModelMap mmap){
        mmap.put("review",ReviewStatus.老板审核.getValue());
        mmap.put("filterStatus",ReviewStatus.财务审核.getValue());
        return prefix + "/review/reviewList";
    }

    /**
     * 审批列表数据
     */
    @RequiresAnyPermissions({"task:settle:review:manager","task:settle:review:finance","task:settle:review:ceo"})
    @PostMapping("review")
    @ResponseBody
    public TableDataInfo reviewManagerList(ProjectTasks projectTasks){
        startPage();
        List<ProjectTasks> list = projectTasksService.selectProjectTasksList(projectTasks);
        return getDataTable(list);
    }

    /**
     * 审批操作
     */
    @RequiresAnyPermissions({"task:settle:review:manager","task:settle:review:finance","task:settle:review:ceo"})
    @GetMapping("{id}/review")
    public String review(@PathVariable("id") String id,@RequestParam String review, ModelMap mmap){
        ProjectTasks projectTasks = projectTasksService.selectProjectTasksById(id);
        mmap.put("projectTasks", projectTasks);
        mmap.put("review",review);
        return prefix + "/review/review";
    }

    /**
     * 审批操作
     */
    @Log(title = "项目阶段任务", businessType = BusinessType.UPDATE)
    @PostMapping("{id}/review")
    @ResponseBody
    public AjaxResult review(@PathVariable("id") String id,Integer review,String remark){
        Subject subject = ShiroUtils.getSubject();

        ReviewStatus reviewStatus = ReviewStatus.getByValue(review);
        boolean hasPrivilege = false;
        switch (reviewStatus){
            case 提审:
                hasPrivilege = subject.isPermitted("task:settle:apply:edit");
                break;
            case 主管审核:
                hasPrivilege = subject.isPermitted("task:settle:review:manager");
                break;
            case 财务审核:
                hasPrivilege = subject.isPermitted("task:settle:review:finance");
                break;
            case 老板审核:
                hasPrivilege = subject.isPermitted("task:settle:review:ceo");
                break;
            case 取消:
                hasPrivilege = subject.isPermitted("task:settle:apply:edit");
                break;
            case 拒绝:
                hasPrivilege = checkRejectPrivilege(subject,id);
                break;
        }

        if(!hasPrivilege){
            throw new ApiException("没有操作权限",ApiException.AUTH_DENIED);
        }

        return toAjax(taskSettleService.review(reviewStatus,id,ShiroUtils.getUserId()+"",remark));
    }

    private boolean checkRejectPrivilege(Subject subject, String taskId) {
        ProjectTasks projectTasks = ParamUtil.requireNotNull(projectTasksService.selectProjectTasksById(taskId), "找不到任务信息.taskId:" + taskId);
        ReviewStatus curStatus = ReviewStatus.getByValue(Optional.ofNullable(projectTasks.getSettleStatusCode()).orElse(ReviewStatus.待提审.getValue()));
        switch (curStatus){
            case 提审:
                return subject.isPermitted("task:settle:review:manager");
            case 主管审核:
                return subject.isPermitted("task:settle:review:finance");
            case 财务审核:
                return subject.isPermitted("task:settle:review:ceo");
            default:
                throw new IllegalStateException("Unexpected value: " + curStatus);
        }
    }
}
