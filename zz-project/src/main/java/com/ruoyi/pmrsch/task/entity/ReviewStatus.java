package com.ruoyi.pmrsch.task.entity;

import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;

/**
 * <AUTHOR>
 * @Date 2025/2/11
 */
public enum ReviewStatus implements EnumIntegerInterface {
    待提审(0),
    提审(1),
    主管审核(2),
    财务审核(3),
    老板审核(4),
    取消(-1),
    拒绝(-2);

    private Integer value;

    ReviewStatus(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static ReviewStatus getByValue(int value) {
        return EnumIntegerInterface.getByValue(value, ReviewStatus.class);
    }

    public String formatName() {
        switch (this){
            case 待提审:
                return "待提审";
            case 提审:
                return "待审核(主管)";
            case 主管审核:
                return "待审核(财务)";
            case 财务审核:
                return "待审核(老板)";
            case 老板审核:
                return "已完成";
            case 取消:
                return "已取消";
            case 拒绝:
                return "已拒绝";
            default:
                throw new IllegalStateException("Unexpected value: " + this);
        }
    }
}