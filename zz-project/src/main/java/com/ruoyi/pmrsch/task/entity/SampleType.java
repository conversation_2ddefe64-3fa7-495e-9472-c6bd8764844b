package com.ruoyi.pmrsch.task.entity;

import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;

/**
 * <AUTHOR>
 * @Date 2025/2/11
 */
public enum SampleType implements EnumIntegerInterface {
    定量(0),
    定性(1);

    private Integer value;

    SampleType(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static SampleType getByValue(int value) {
        return EnumIntegerInterface.getByValue(value, SampleType.class);
    }
}