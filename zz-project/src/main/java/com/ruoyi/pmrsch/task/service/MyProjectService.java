package com.ruoyi.pmrsch.task.service;/**
 * <AUTHOR>
 * @Date 2025/2/25
 */

import com.ruoyi.pmrsch.common.MoneyUtil;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.service.impl.ProjectInfoServiceImpl;
import com.ruoyi.pmrsch.task.domain.ExpertConsultation;
import com.ruoyi.pmrsch.task.domain.ProjectTaskLabor;
import com.ruoyi.pmrsch.task.domain.ProjectTaskSample;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ApplyStatus;
import com.ruoyi.pmrsch.task.entity.ReviewStatus;
import com.ruoyi.pmrsch.task.entity.TaskTotal;
import com.ruoyi.pmrsch.task.service.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ruoyi.pmrsch.task.entity.ReviewStatus.主管审核;
import static com.ruoyi.pmrsch.task.entity.ReviewStatus.财务审核;

/**
 * 描述
 * @className MyProjectService
 * <AUTHOR>
 * @date 2025年02月25日 14:05
 */
@Service
public class MyProjectService {
    @Autowired
    private ProjectInfoServiceImpl projectInfoService;
    @Autowired
    private ProjectTasksServiceImpl tasksService;
    @Autowired
    private ExpertConsultationServiceImpl consultationService;
    @Autowired
    private ProjectTaskSampleServiceImpl sampleService;
    @Autowired
    private ProjectTaskLaborServiceImpl laborService;

    public List<ProjectInfo> getMyProjects(ProjectInfo projectInfo) {
        List<ProjectInfo> projectInfos = projectInfoService.selectProjectInfoList(projectInfo);

        List<ReviewStatus> reviewingStatus = Arrays.asList(主管审核, 财务审核);
        for (ProjectInfo info : projectInfos) {
            ProjectTasks q = new ProjectTasks();
            q.setProjectInfoId(info.getId());
            List<ProjectTasks> tasks = tasksService.selectProjectTasksList(q);

            info.setTaskTotalList(
                    tasks.stream().map(t -> {
                        TaskTotal taskTotal = new TaskTotal();
                        taskTotal.setId(t.getId());
                        taskTotal.setTaskName(t.getTaskName());
                        taskTotal.setTaskStatus(getTaskFormatStatus(t));

                        taskTotal.setBudgetNum(t.getTaskBudgets().size());
                        taskTotal.setBudgetNumWaitSubmit(t.getTaskBudgets().stream().filter(b -> Objects.equals(ReviewStatus.待提审.getValue(),b.getBudgetStatusCode())).count());

                        taskTotal.setBudgetNumReviewing(t.getTaskBudgets().stream().filter(b -> reviewingStatus.contains(ReviewStatus.getByValue(b.getBudgetStatusCode()))).count());

                        taskTotal.setBudgetNumPass(t.getTaskBudgets().stream().filter(b -> Objects.equals(ReviewStatus.老板审核.getValue(),b.getBudgetStatusCode())).count());
                        taskTotal.setBudgetNumReject(t.getTaskBudgets().stream().filter(b -> Objects.equals(ReviewStatus.拒绝.getValue(),b.getBudgetStatusCode())).count());

                        taskTotal.setLoansNum(t.getTaskBudgets().stream().mapToInt(b -> b.getTaskLoans().size()).sum());
                        taskTotal.setLoansNumWaitSubmit(t.getTaskBudgets()
                                .stream()
                                .mapToLong(b -> b.getTaskLoans().stream().filter(l -> Objects.equals(ReviewStatus.待提审.getValue(),l.getLoansStatusCode())).count())
                                .sum());

                        taskTotal.setLoansNumReviewing(t.getTaskBudgets()
                                .stream()
                                .mapToLong(b -> b.getTaskLoans().stream().filter(l -> reviewingStatus.contains(ReviewStatus.getByValue(l.getLoansStatusCode()))).count())
                                .sum());

                        taskTotal.setLoansNumPass(t.getTaskBudgets()
                                .stream()
                                .mapToLong(b -> b.getTaskLoans().stream().filter(l -> Objects.equals(ReviewStatus.老板审核.getValue(),l.getLoansStatusCode())).count())
                                .sum());
                        taskTotal.setLoansNumReject(t.getTaskBudgets()
                                .stream()
                                .mapToLong(b -> b.getTaskLoans().stream().filter(l -> Objects.equals(ReviewStatus.拒绝.getValue(),l.getLoansStatusCode())).count())
                                .sum());

                        taskTotal.setTotalLoans(MoneyUtil.format(t.getTotalLoans(),2));
                        taskTotal.setContractTotal(MoneyUtil.format(t.getContractTotal(),2));
                        taskTotal.setBudgetTotal(MoneyUtil.format(t.getTotalBudget(),2));
                        taskTotal.setBudgetRatio(MoneyUtil.format(t.getBudgetRatio()*100,2));
                        taskTotal.setCostRatio(MoneyUtil.format(t.getCostRatio()*100,2));

                        boolean taskApply = ApplyStatus.审核.equals(ApplyStatus.getByValue(t.getApplyStatusCode()));
                        boolean taskEditAble = Arrays.asList(ApplyStatus.拒绝,ApplyStatus.待提审).contains(ApplyStatus.getByValue(t.getApplyStatusCode()));
                        boolean budgetEditAble = taskApply && Arrays.asList(ApplyStatus.拒绝,ApplyStatus.待提审).contains(ApplyStatus.getByValue(t.getSettleStatusCode()));
                        boolean settleEditAble = taskTotal.getBudgetNumPass() > 0 && budgetEditAble;

                        taskTotal.setBtnCommitTask(taskEditAble);
                        taskTotal.setBtnEditTask(taskEditAble);
                        taskTotal.setBtnDelTask(taskEditAble);

                        taskTotal.setBtnBudget(budgetEditAble);

                        taskTotal.setBtnApplySettle(settleEditAble);
                        taskTotal.setBtnCommitSettle(settleEditAble);

                        taskTotal.setBtnLabor(settleEditAble);
                        taskTotal.setBtnRespondent(settleEditAble);

                        taskTotal.setApplyStatusCode(t.getApplyStatusCode());
                        taskTotal.setSettleStatusCode(t.getSettleStatusCode()); 
                        taskTotal.setIsBudgetPass(taskTotal.getBudgetNumPass() > 0);

                        return taskTotal;
                    }).collect(Collectors.toList()));
        }

        return projectInfos;
    }

    public void genSettleByExpertBill(String taskId){
        ExpertConsultation q = new ExpertConsultation();
        q.setProjectTaskId(taskId);
        List<ExpertConsultation> consultations = consultationService.selectExpertConsultationList(q);

        sampleService.delByTaskId(taskId);
        laborService.delByTaskId(taskId);

        for (ExpertConsultation consultation : consultations) {
            ProjectTaskSample taskSample = new ProjectTaskSample();
            taskSample.setProjectTaskId(taskId);
            taskSample.setSampleTypeCode(consultation.getSampleTypeCode());
            taskSample.setSampleName(consultation.getProjectInfo().getpName()+":"+consultation.getExpertInfo().getExpertName());
            taskSample.setSamplePrice(consultation.getUnitPrice().doubleValue());
            taskSample.setSampleQuantity(consultation.getQuantity());
            taskSample.setRemarks(consultation.getRemarks());
            sampleService.insertProjectTaskSample(taskSample);

            ProjectTaskLabor taskLabor = new ProjectTaskLabor();
            taskLabor.setProjectTaskId(taskId);
            taskLabor.setName(consultation.getExpertName());
            taskLabor.setJob("渠道");
            taskLabor.setWorkDays(new Double(consultation.getConsultationTime()+""));
            taskLabor.setRecipientName(consultation.getChannel());
            taskLabor.setContactPhone(consultation.getExpertPhone());
            //taskLabor.setPaymentAccount();
            taskLabor.setTaxTate(0.03);

            laborService.insertProjectTaskLabor(taskLabor);
        }
    }

    private String getTaskFormatStatus(ProjectTasks tasks) {
        ApplyStatus curApplyStatus = ApplyStatus.getByValue(tasks.getApplyStatusCode());
        ReviewStatus curSettleStatus = ReviewStatus.getByValue(tasks.getSettleStatusCode());

        switch (curApplyStatus){
            case 待提审:case 提审:case 取消:case 拒绝:
                return curApplyStatus.formatName();

            case 审核: {
                switch (curSettleStatus){
                    case 待提审:
                        return "进行中";
                    case 提审:case 主管审核:case 财务审核:case 取消:case 拒绝:
                        return "结算".concat(curSettleStatus.formatName());
                    case 老板审核:
                        return "已完成";

                    default:
                        throw new IllegalStateException("Unexpected value: " + curSettleStatus);
                }
            }

            default:
                throw new IllegalStateException("Unexpected value: " + curApplyStatus);
        }
    }
}
