package com.ruoyi.pmrsch.task.entity;

import com.ruoyi.pmrsch.common.entity.EnumIntegerInterface;

/**
 * <AUTHOR>
 * @Date 2025/1/31
 */
public enum ReviewBType implements EnumIntegerInterface {
    任务(0),
    预算(1),
    借款(2),
    决算(3);

    private Integer value;

    ReviewBType(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static ReviewBType getByValue(int value) {
        return EnumIntegerInterface.getByValue(value, ReviewBType.class);
    }
}