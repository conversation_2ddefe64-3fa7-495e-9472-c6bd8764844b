package com.ruoyi.pmrsch.task.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.pmrsch.task.domain.ProjectTaskSample;
import com.ruoyi.pmrsch.task.service.IProjectTaskSampleService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目任务样本Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Controller
@RequestMapping("/task/sample")
public class SampleController extends BaseController{
    private String prefix = "task/sample";

    @Autowired
    private IProjectTaskSampleService projectTaskSampleService;

    @RequiresPermissions("task:sample:view")
    @GetMapping()
    public String sample(){
        return prefix + "/sample";
    }

    /**
     * 查询项目任务样本列表
     */
    @RequiresPermissions("task:sample:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProjectTaskSample projectTaskSample){
        startPage();
        List<ProjectTaskSample> list = projectTaskSampleService.selectProjectTaskSampleList(projectTaskSample);
        return getDataTable(list);
    }

    /**
     * 导出项目任务样本列表
     */
    @RequiresPermissions("task:sample:export")
    @Log(title = "项目任务样本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProjectTaskSample projectTaskSample){
        List<ProjectTaskSample> list = projectTaskSampleService.selectProjectTaskSampleList(projectTaskSample);
        ExcelUtil<ProjectTaskSample> util = new ExcelUtil<ProjectTaskSample>(ProjectTaskSample.class);
        return util.exportExcel(list, "项目任务样本数据");
    }

    /**
     * 新增项目任务样本
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/add";
    }

    /**
     * 新增保存项目任务样本
     */
    @RequiresPermissions("task:sample:add")
    @Log(title = "项目任务样本", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProjectTaskSample projectTaskSample){
        return toAjax(projectTaskSampleService.insertProjectTaskSample(projectTaskSample));
    }

    /**
     * 修改项目任务样本
     */
    @RequiresPermissions("task:sample:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap){
        ProjectTaskSample projectTaskSample = projectTaskSampleService.selectProjectTaskSampleById(id);
        mmap.put("projectTaskSample", projectTaskSample);
        return prefix + "/edit";
    }

    /**
     * 修改保存项目任务样本
     */
    @RequiresPermissions("task:sample:edit")
    @Log(title = "项目任务样本", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProjectTaskSample projectTaskSample){
        return toAjax(projectTaskSampleService.updateProjectTaskSample(projectTaskSample));
    }

    /**
     * 删除项目任务样本
     */
    @RequiresPermissions("task:sample:remove")
    @Log(title = "项目任务样本", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids){
        return toAjax(projectTaskSampleService.deleteProjectTaskSampleByIds(ids));
    }
}
