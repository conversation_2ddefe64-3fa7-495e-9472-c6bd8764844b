package com.ruoyi.pmrsch.task.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.pmrsch.task.domain.ProjectPayCollectionLogs;
import com.ruoyi.pmrsch.task.mapper.ProjectPayCollectionLogsMapper;
import com.ruoyi.pmrsch.task.service.IPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收款记录 服务层实现
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class PaymentServiceImpl implements IPaymentService {
    @Autowired
    private ProjectPayCollectionLogsMapper projectPayCollectionLogsMapper;

    /**
     * 查询收款记录信息
     * 
     * @param id 收款记录ID
     * @return 收款记录信息
     */
    @Override
    public ProjectPayCollectionLogs selectProjectPayCollectionLogsById(String id) {
        return projectPayCollectionLogsMapper.selectProjectPayCollectionLogsById(id);
    }

    /**
     * 查询收款记录列表
     * 
     * @param projectPayCollectionLogs 收款记录信息
     * @return 收款记录集合
     */
    @Override
    public List<ProjectPayCollectionLogs> selectProjectPayCollectionLogsList(ProjectPayCollectionLogs projectPayCollectionLogs) {
        return projectPayCollectionLogsMapper.selectProjectPayCollectionLogsList(projectPayCollectionLogs);
    }

    /**
     * 新增收款记录
     * 
     * @param projectPayCollectionLogs 收款记录信息
     * @return 结果
     */
    @Override
    public int insertProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs) {
        if (StringUtils.isEmpty(projectPayCollectionLogs.getId())) {
            projectPayCollectionLogs.setId(IdUtils.fastSimpleUUID());
        }
        projectPayCollectionLogs.setCreateTime(DateUtils.getNowDate());
        return projectPayCollectionLogsMapper.insertProjectPayCollectionLogs(projectPayCollectionLogs);
    }

    /**
     * 修改收款记录
     * 
     * @param projectPayCollectionLogs 收款记录信息
     * @return 结果
     */
    @Override
    public int updateProjectPayCollectionLogs(ProjectPayCollectionLogs projectPayCollectionLogs) {
        projectPayCollectionLogs.setUpdateTime(DateUtils.getNowDate());
        return projectPayCollectionLogsMapper.updateProjectPayCollectionLogs(projectPayCollectionLogs);
    }

    /**
     * 删除收款记录对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteProjectPayCollectionLogsByIds(String ids) {
        return projectPayCollectionLogsMapper.deleteProjectPayCollectionLogsByIds(Convert.toStrArray(ids));
    }
} 