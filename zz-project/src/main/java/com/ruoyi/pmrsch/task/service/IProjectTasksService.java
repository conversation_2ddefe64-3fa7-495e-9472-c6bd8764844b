package com.ruoyi.pmrsch.task.service;

import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.entity.ApplyStatus;

import java.util.List;
import java.util.Map;

/**
 * 项目阶段任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IProjectTasksService {
    /**
     * 查询项目阶段任务
     * 
     * @param id 项目阶段任务主键
     * @return 项目阶段任务
     */
    public ProjectTasks selectProjectTasksById(String id);

    /**
     * 查询项目阶段任务列表
     * 
     * @param projectTasks 项目阶段任务
     * @return 项目阶段任务集合
     */
    public List<ProjectTasks> selectProjectTasksList(ProjectTasks projectTasks);

    /**
     * 新增项目阶段任务
     * 
     * @param projectTasks 项目阶段任务
     * @return 结果
     */
    public int apply(ProjectTasks projectTasks);

    /**
     * 修改项目阶段任务
     * 
     * @param projectTasks 项目阶段任务
     * @return 结果
     */
    public int applyEdit(ProjectTasks projectTasks);

    /**
     * 批量删除项目阶段任务
     * 
     * @param ids 需要删除的项目阶段任务主键集合
     * @return 结果
     */
    public int deleteProjectTasksByIds(String ids);

    /**
     * 删除项目阶段任务信息
     * 
     * @param id 项目阶段任务主键
     * @return 结果
     */
    public int deleteProjectTasksById(String id);

    /**
     * 审核任务
     * @param reviewStatus 审核操作
     * @param taskId 任务id
     * @param userId 审核用户
     * @param remark 审核备注
     * @return
     */
    int review(ApplyStatus reviewStatus,String taskId, String userId, String remark);

    int updateProjectTasks(ProjectTasks projectTasks);

    /**
     * 获取项目任务下拉列表
     * 
     * @param keyword 搜索关键词
     * @return 项目任务列表
     */
    public List<Map<String, Object>> suggestList(String keyword);
}
