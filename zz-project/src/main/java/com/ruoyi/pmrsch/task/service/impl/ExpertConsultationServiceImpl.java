package com.ruoyi.pmrsch.task.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.pmrsch.task.mapper.ExpertConsultationMapper;
import com.ruoyi.pmrsch.common.exception.ApiException;
import com.ruoyi.pmrsch.common.tools.ParamUtil;
import com.ruoyi.pmrsch.common.tools.RandomStringUtil;
import com.ruoyi.pmrsch.pmdata.domain.Customer;
import com.ruoyi.pmrsch.pmdata.domain.ExpertInfo;
import com.ruoyi.pmrsch.pmdata.domain.ProjectInfo;
import com.ruoyi.pmrsch.pmdata.mapper.CustomerMapper;
import com.ruoyi.pmrsch.pmdata.mapper.ExpertInfoMapper;
import com.ruoyi.pmrsch.pmdata.mapper.ProjectInfoMapper;
import com.ruoyi.pmrsch.task.domain.ExpertConsultation;
import com.ruoyi.pmrsch.task.domain.ExpertConsultationBilling;
import com.ruoyi.pmrsch.task.domain.ProjectTasks;
import com.ruoyi.pmrsch.task.mapper.ProjectTasksMapper;
import com.ruoyi.pmrsch.task.service.IExpertConsultationService;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.ShiroUtils;

/**
 * 专家咨询记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
@Service
public class ExpertConsultationServiceImpl implements IExpertConsultationService 
{
    @Autowired
    private ExpertConsultationMapper expertConsultationMapper;
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    @Autowired
    private ProjectTasksMapper projectTasksMapper;

    /**
     * 查询专家咨询记录
     * 
     * @param id 专家咨询记录主键
     * @return 专家咨询记录
     */
    @Override
    public ExpertConsultation selectExpertConsultationById(Long id)
    {
        ExpertConsultation expertConsultation = expertConsultationMapper.selectExpertConsultationById(id);
        supplement(expertConsultation);
        return expertConsultation;
    }

    /**
     * 查询专家咨询记录列表
     * 
     * @param expertConsultation 专家咨询记录
     * @return 专家咨询记录
     */
    @DataScope(deptAlias = "t", userAlias = "t")
    @Override
    public List<ExpertConsultation> selectExpertConsultationList(ExpertConsultation expertConsultation)
    {
        List<ExpertConsultation> list = expertConsultationMapper.selectExpertConsultationList(expertConsultation);
        list.forEach(this::supplement);
        return list;
    }

    /**
     * 新增专家咨询记录
     * 
     * @param expertConsultation 专家咨询记录
     * @return 结果
     */
    @Override
    public int insertExpertConsultation(ExpertConsultation expertConsultation)
    {
        ParamUtil.checkObjParam(expertConsultation, "expertConsultation");

        if(StringUtils.isBlank(expertConsultation.getProjectTaskId())){
            ParamUtil.checkObjParam(expertConsultation.getProjectNo(), "projectNo");

            ProjectInfo qProject = new ProjectInfo();
            qProject.setpNo(expertConsultation.getProjectNo());
            List<ProjectInfo> projectInfos = projectInfoMapper.selectProjectInfoList(qProject);
            ParamUtil.checkApiArrParam(projectInfos, "projectInfos");

            ProjectTasks qTask = new ProjectTasks();
            qTask.setProjectInfoId(projectInfos.get(0).getId());
            List<ProjectTasks> projectTasks = projectTasksMapper.selectProjectTasksList(qTask);
            ParamUtil.checkApiArrParam(projectTasks, "projectTasks");

            if(projectTasks.size() > 1){
                throw new ApiException("项目存在多个关联的任务",ApiException.BAD_REQUEST);
            }

            expertConsultation.setProjectTaskId(projectTasks.get(0).getId());
        }

        if(StringUtils.isBlank(expertConsultation.getProjectNo())){
            ParamUtil.checkObjParam(expertConsultation.getProjectTaskId(), "ProjectTaskId");

            ProjectTasks projectTask = projectTasksMapper.selectProjectTasksById(expertConsultation.getProjectTaskId());
            ParamUtil.checkObjParam(projectTask, "projectTask");

            ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(projectTask.getProjectInfoId());
            ParamUtil.checkObjParam(projectInfo, "projectInfo");

            expertConsultation.setProjectInfoId(projectInfo.getId());
        }

        expertConsultation.setId(RandomStringUtil.gen18UUID());
        expertConsultation.setCreatedAt(new Date());
        expertConsultation.setUserId(ShiroUtils.getUserId());
        expertConsultation.setDeptId(ShiroUtils.getSysUser().getDeptId());

        return expertConsultationMapper.insertExpertConsultation(expertConsultation);
    }

    /**
     * 修改专家咨询记录
     * 
     * @param expertConsultation 专家咨询记录
     * @return 结果
     */
    @Override
    public int updateExpertConsultation(ExpertConsultation expertConsultation)
    {
        expertConsultation.setUpdateBy(ShiroUtils.getUserId()+"");
        expertConsultation.setUpdateTime(new Date());
        return expertConsultationMapper.updateExpertConsultation(expertConsultation);
    }

    /**
     * 批量删除专家咨询记录
     * 
     * @param ids 需要删除的专家咨询记录主键
     * @return 结果
     */
    @Override
    public int deleteExpertConsultationByIds(String ids)
    {
        return expertConsultationMapper.deleteExpertConsultationByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除专家咨询记录信息
     * 
     * @param id 专家咨询记录主键
     * @return 结果
     */
    @Override
    public int deleteExpertConsultationById(Long id)
    {
        return expertConsultationMapper.deleteExpertConsultationById(id);
    }
    
    /**
     * 查询专家咨询账单统计列表
     * 
     * @param billing 统计参数
     * @return 账单统计结果集合
     */
    @Override
    public List<ExpertConsultationBilling> selectExpertConsultationBillingList(ExpertConsultationBilling billing)
    {
        return expertConsultationMapper.selectExpertConsultationBillingList(billing);
    }

    private void supplement(ExpertConsultation expertConsultation) {
        if(expertConsultation == null){
            return;
        }
        
        ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(expertConsultation.getProjectInfoId());
        expertConsultation.setProjectInfo(projectInfo);

        // 获取项目任务信息
        ProjectTasks projectTask = projectTasksMapper.selectProjectTasksById(expertConsultation.getProjectTaskId());
        expertConsultation.setProjectTask(projectTask);
        
        // 通过项目任务获取客户信息
        if (projectInfo != null) {
            Customer customer = customerMapper.selectCustomerById(projectInfo.getCustomerId());
            expertConsultation.setCustomer(customer);
        }

        ExpertInfo expertInfo = expertInfoMapper.selectExpertInfoById(expertConsultation.getExpertInfoId());
        expertConsultation.setExpertInfo(expertInfo);
    }
}
