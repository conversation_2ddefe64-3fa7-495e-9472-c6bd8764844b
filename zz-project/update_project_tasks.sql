-- 更新 zz_project_tasks 表，添加6个新字段
ALTER TABLE zz_project_tasks ADD COLUMN deduction_amount DECIMAL(10,2) COMMENT '扣款';
ALTER TABLE zz_project_tasks ADD COLUMN other_cost DECIMAL(10,2) COMMENT '其它需要支付费用';
ALTER TABLE zz_project_tasks ADD COLUMN prepaid_cost DECIMAL(10,2) COMMENT '己收预付费';
ALTER TABLE zz_project_tasks ADD COLUMN project_rebate DECIMAL(10,2) COMMENT '项目回扣';
ALTER TABLE zz_project_tasks ADD COLUMN customer_comment VARCHAR(255) COMMENT '客户评议';
ALTER TABLE zz_project_tasks ADD COLUMN of_mouth VARCHAR(20) COMMENT '所属月份';

-- 为新增字段设置默认值
UPDATE zz_project_tasks SET deduction_amount = 0 WHERE deduction_amount IS NULL;
UPDATE zz_project_tasks SET other_cost = 0 WHERE other_cost IS NULL;
UPDATE zz_project_tasks SET prepaid_cost = 0 WHERE prepaid_cost IS NULL;
UPDATE zz_project_tasks SET project_rebate = 0 WHERE project_rebate IS NULL;
