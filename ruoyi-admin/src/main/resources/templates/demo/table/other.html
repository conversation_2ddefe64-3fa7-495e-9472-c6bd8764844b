<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('其他操作')" />
</head>
<body class="gray-bg">
     <div class="container-div">
     	<div class="btn-group-sm" id="toolbar" role="group">
	        <a class="btn btn-success" onclick="$.table.showColumn('userName')">
	            <i class="fa fa-check"></i> 显示姓名
	        </a>
	        <a class="btn btn-danger" onclick="$.table.hideColumn('userName')">
	            <i class="fa fa-close"></i> 隐藏姓名
	        </a>
	        <a class="btn btn-info" onclick="selectColumns()">
	            <i class="fa fa-search"></i> 获取选中姓名
	        </a>
	        <a class="btn btn-warning" onclick="$.table.refresh()">
	            <i class="fa fa-refresh"></i> 刷新
	        </a>
	        <a class="btn btn-danger" onclick="$.table.destroy()">
	            <i class="fa fa-refresh"></i> 销毁
	        </a>
	        <a class="btn btn-primary" onclick="selectFirstColumns()">
	            <i class="fa fa-search"></i> 获取选中首列值
	        </a>
        </div>
		<div class="row">
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "demo/table";
        var datas = [[${@dict.getType('sys_normal_disable')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'userId', 
					title : '用户ID'
				},
				{
					field : 'userCode', 
					title : '用户编号'
				},
				{
					field : 'userName', 
					title : '用户姓名'
				},
				{
					field : 'userPhone', 
					title : '用户手机'
				},
				{
					field : 'userEmail', 
					title : '用户邮箱'
				},
				{
				    field : 'userBalance',
				    title : '用户余额'
				},
				{
                    field: 'status',
                    title: '用户状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                    	return $.table.selectDictLabel(datas, value);
                    }
                },
		        {
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	var actions = [];
		            	actions.push('<a class="btn btn-success btn-xs" href="javascript:;"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:;"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
		            }
		        }]
            };
            $.table.init(options);
        });
        
        function selectColumns() {
        	var column = $.table.selectColumns('userName');
        	alert(column);
        }
        
        function selectFirstColumns() {
        	var column = $.table.selectFirstColumns();
        	alert(column);
        }
    </script>
</body>
</html>