<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('菜单列表')" />
</head>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="menu-form">
					<div class="select-list">
						<ul>
							<li>
								菜单名称：<input type="text" name="menuName"/>
							</li>
							<li>
								菜单状态：<select name="visible" th:with="type=${@dict.getType('sys_show_hide')}">
									<option value="">所有</option>
									<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
								</select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.treeTable.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
                
            <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-success" onclick="$.operate.add(0)" shiro:hasPermission="system:menu:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary" onclick="$.operate.edit()" shiro:hasPermission="system:menu:edit">
		            <i class="fa fa-edit"></i> 修改
		        </a>
                <a class="btn btn-info" id="expandAllBtn">
                    <i class="fa fa-exchange"></i> 展开/折叠
                </a>
	        </div>
       		 <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-tree-table"></table>
	        </div>
	    </div>
	</div>
	
	<th:block th:include="include :: footer" />
	<script th:inline="javascript">
		var addFlag = [[${@permission.hasPermi('system:menu:add')}]];
		var editFlag = [[${@permission.hasPermi('system:menu:edit')}]];
		var removeFlag = [[${@permission.hasPermi('system:menu:remove')}]];
		var datas = [[${@dict.getType('sys_show_hide')}]];
		var prefix = ctx + "system/menu";

		$(function() {
		    var options = {
		        code: "menuId",
		        parentCode: "parentId",
		        uniqueId: "menuId",
		        expandAll: false,
		        expandFirst: false,
		        url: prefix + "/list",
		        createUrl: prefix + "/add/{id}",
		        updateUrl: prefix + "/edit/{id}",
		        removeUrl: prefix + "/remove/{id}",
		        modalName: "菜单",
		        columns: [{
                    field: 'selectItem', 
                    radio: true
                 },
                 {
		            title: '菜单名称',
		            field: 'menuName',
		            width: '20',
		            widthUnit: '%',
		            formatter: function(value, row, index) {
		                if ($.common.isEmpty(row.icon)) {
		                    return row.menuName;
		                } else {
		                    return '<i class="' + row.icon + '"></i> <span class="nav-label">' + row.menuName + '</span>';
		                }
		            }
		        },
		        {
		            field: 'orderNum',
		            title: '排序',
		            width: '10',
		            widthUnit: '%',
		            align: "left"
		        },
		        {
		            field: 'url',
		            title: '请求地址',
		            width: '15',
		            widthUnit: '%',
		            align: "left",
		            formatter: function(value, row, index) {
                    	return $.table.tooltip(value);
                    }
		        },
		        {
		            title: '类型',
		            field: 'menuType',
		            width: '10',
		            widthUnit: '%',
		            align: "left",
		            formatter: function(value, item, index) {
		                if (item.menuType == 'M') {
		                    return '<span class="label label-success">目录</span>';
		                }
		                else if (item.menuType == 'C') {
		                    return '<span class="label label-primary">菜单</span>';
		                }
		                else if (item.menuType == 'F') {
		                    return '<span class="label label-warning">按钮</span>';
		                }
		            }
		        },
		        {
		            field: 'visible',
		            title: '可见',
		            width: '10',
		            widthUnit: '%',
		            align: "left",
		            formatter: function(value, row, index) {
		            	if (row.menuType == 'F') {
		                    return '-';
		                }
		            	return $.table.selectDictLabel(datas, row.visible);
		            }
		        },
		        {
		            field: 'perms',
		            title: '权限标识',
		            width: '15',
		            widthUnit: '%',
		            align: "left",
		            formatter: function(value, row, index) {
                    	return $.table.tooltip(value);
                    }
		        },
		        {
		            title: '操作',
		            width: '20',
		            widthUnit: '%',
		            align: "left",
		            formatter: function(value, row, index) {
		                var actions = [];
		                actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.menuId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
		                actions.push('<a class="btn btn-info btn-xs ' + addFlag + '" href="javascript:void(0)" onclick="$.operate.add(\'' + row.menuId + '\')"><i class="fa fa-plus"></i>新增</a> ');
		                actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.menuId + '\')"><i class="fa fa-trash"></i>删除</a>');
		                return actions.join('');
		            }
		        }]
		    };
		    $.treeTable.init(options);
		});
	</script>
</body>
</html>