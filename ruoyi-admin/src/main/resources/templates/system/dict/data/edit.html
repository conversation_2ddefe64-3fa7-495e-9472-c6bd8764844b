<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改字典数据')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-dict-edit" th:object="${dict}">
			<input name="dictCode"  type="hidden"  th:field="*{dictCode}" />
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">字典标签：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="dictLabel" id="dictLabel" th:field="*{dictLabel}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">字典键值：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="dictValue" id="dictValue" th:field="*{dictValue}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">字典类型：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" readonly="true" th:field="*{dictType}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">样式属性：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" id="cssClass" name="cssClass" th:field="*{cssClass}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">字典排序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="dictSort" th:field="*{dictSort}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">回显样式：</label>
				<div class="col-sm-8">
					<select name="listClass" class="form-control m-b">
					    <option value=""        th:field="*{listClass}">---请选择---</option>
	                    <option value="default" th:field="*{listClass}">默认</option>
	                    <option value="primary" th:field="*{listClass}">主要</option>
	                    <option value="success" th:field="*{listClass}">成功</option>
	                    <option value="info"    th:field="*{listClass}">信息</option>
	                    <option value="warning" th:field="*{listClass}">警告</option>
	                    <option value="danger"  th:field="*{listClass}">危险</option>
	                </select>
	                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> table表格字典列显示样式属性</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">系统默认：</label>
				<div class="col-sm-8">
					<div class="radio-box" th:each="dict : ${@dict.getType('sys_yes_no')}">
						<input type="radio" th:id="${dict.dictCode}" name="isDefault" th:value="${dict.dictValue}" th:field="*{isDefault}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">状态：</label>
				<div class="col-sm-8">
					<div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<textarea id="remark" name="remark" class="form-control">[[*{remark}]]</textarea>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<script type="text/javascript">
		var prefix = ctx + "system/dict/data";
	
		$("#form-dict-edit").validate({
			rules:{
				dictSort:{
					digits:true
				},
			},
			focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/edit", $('#form-dict-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
