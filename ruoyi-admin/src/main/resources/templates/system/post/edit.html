<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改岗位')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-post-edit" th:object="${post}">
			<input id="postId" name="postId" type="hidden" th:field="*{postId}"/>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">岗位名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="postName" id="postName" th:field="*{postName}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">岗位编码：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="postCode" id="postCode" th:field="*{postCode}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">显示顺序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="postSort" id="postSort" th:field="*{postSort}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">岗位状态：</label>
				<div class="col-sm-8">
					<div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<textarea id="remark" name="remark" class="form-control">[[*{remark}]]</textarea>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<script type="text/javascript">
		var prefix = ctx + "system/post";
		
		$("#form-post-edit").validate({
			onkeyup: false,
			rules:{
				postName:{
					remote: {
		                url: ctx + "system/post/checkPostNameUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"postId": function() {
							    return $("input[name='postId']").val();
							},
		                	"postName" : function() {
		                        return $.common.trim($("#postName").val());
		                    }
		                }
		            }
				},
				postCode:{
					remote: {
		                url: ctx + "system/post/checkPostCodeUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"postId": function() {
							    return $("input[name='postId']").val();
							},
		                	"postCode" : function() {
		                		return $.common.trim($("#postCode").val());
		                    }
		                }
		            }
				},
				postSort:{
					digits:true
				},
			},
			messages: {
		        "postCode": {
		            remote: "岗位编码已经存在"
		        },
		        "postName": {
		            remote: "岗位名称已经存在"
		        }
		    },
		    focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/edit", $('#form-post-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
