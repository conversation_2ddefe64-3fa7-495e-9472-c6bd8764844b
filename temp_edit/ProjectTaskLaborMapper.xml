<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pmrsch.task.mapper.ProjectTaskLaborMapper">
    
    <resultMap type="ProjectTaskLabor" id="ProjectTaskLaborResult">
        <result property="id"    column="id"    />
        <result property="projectTaskId"    column="project_task_id"    />
        <result property="name"    column="name"    />
        <result property="job"    column="job"    />
        <result property="workDays"    column="work_days"    />
        <result property="recipientName"    column="recipient_name"    />
        <result property="laborPrice"    column="labor_price"    />
        <result property="overtimeAmount"    column="overtime_amount"    />
        <result property="taxTate"    column="tax_tate"    />
        <result property="amountPayable"    column="amount_payable"    />
        <result property="amountPaid"    column="amount_paid"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="paymentAccount"    column="payment_account"    />
        <result property="paymentDescription"    column="payment_description"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectProjectTaskLaborVo">
        select t.id, t.project_task_id, t.name, t.job, t.work_days, t.recipient_name, t.labor_price, t.overtime_amount, t.tax_tate, t.amount_payable, t.amount_paid, t.payment_amount, DATE_FORMAT(t.payment_time, '%Y-%m-%d') as payment_time, t.payment_account, t.payment_description, t.contact_phone, t.remarks
        from zz_project_task_labor t
        left join zz_project_tasks p on p.id = t.project_task_id
    </sql>

    <select id="selectProjectTaskLaborList" parameterType="ProjectTaskLabor" resultMap="ProjectTaskLaborResult">
        <include refid="selectProjectTaskLaborVo"/>
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and project_task_id = #{projectTaskId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="job != null  and job != ''"> and job = #{job}</if>
            <if test="workDays != null"> and work_days = #{workDays}</if>
            <if test="recipientName != null and recipientName != ''"> and recipient_name like concat('%', #{recipientName}, '%')</if>
            <if test="laborPrice != null"> and labor_price = #{laborPrice}</if>
            <if test="overtimeAmount != null"> and overtime_amount = #{overtimeAmount}</if>
            <if test="taxTate != null"> and tax_tate = #{taxTate}</if>
            <if test="amountPayable != null"> and amount_payable = #{amountPayable}</if>
            <if test="amountPaid != null"> and amount_paid = #{amountPaid}</if>
            <if test="paymentAmount != null"> and payment_amount = #{paymentAmount}</if>
            <if test="paymentTime != null"> and payment_time = #{paymentTime}</if>
            <if test="paymentAccount != null  and paymentAccount != ''"> and payment_account = #{paymentAccount}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(payment_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(payment_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.hasUnpaidAmount != null and params.hasUnpaidAmount == 'true'">
                and (amount_paid > IFNULL(payment_amount, 0) or payment_amount is null)
            </if>
            <if test="settleStatusCode != null "> and p.settle_status_code = #{settleStatusCode}</if>
        </where>
    </select>
    
    <select id="selectProjectTaskLaborById" parameterType="String" resultMap="ProjectTaskLaborResult">
        <include refid="selectProjectTaskLaborVo"/>
        where id = #{id}
    </select>

    <insert id="insertProjectTaskLabor" parameterType="ProjectTaskLabor">
        insert into zz_project_task_labor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id,</if>
            <if test="name != null">name,</if>
            <if test="job != null">job,</if>
            <if test="workDays != null">work_days,</if>
            <if test="recipientName != null">recipient_name,</if>
            <if test="laborPrice != null">labor_price,</if>
            <if test="overtimeAmount != null">overtime_amount,</if>
            <if test="taxTate != null">tax_tate,</if>
            <if test="amountPayable != null">amount_payable,</if>
            <if test="amountPaid != null">amount_paid,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="paymentAccount != null">payment_account,</if>
            <if test="paymentDescription != null">payment_description,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectTaskId != null and projectTaskId != ''">#{projectTaskId},</if>
            <if test="name != null">#{name},</if>
            <if test="job != null">#{job},</if>
            <if test="workDays != null">#{workDays},</if>
            <if test="recipientName != null">#{recipientName},</if>
            <if test="laborPrice != null">#{laborPrice},</if>
            <if test="overtimeAmount != null">#{overtimeAmount},</if>
            <if test="taxTate != null">#{taxTate},</if>
            <if test="amountPayable != null">#{amountPayable},</if>
            <if test="amountPaid != null">#{amountPaid},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="paymentAccount != null">#{paymentAccount},</if>
            <if test="paymentDescription != null">#{paymentDescription},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateProjectTaskLabor" parameterType="ProjectTaskLabor">
        update zz_project_task_labor
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectTaskId != null and projectTaskId != ''">project_task_id = #{projectTaskId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="job != null">job = #{job},</if>
            <if test="workDays != null">work_days = #{workDays},</if>
            <if test="recipientName != null">recipient_name = #{recipientName},</if>
            <if test="laborPrice != null">labor_price = #{laborPrice},</if>
            <if test="overtimeAmount != null">overtime_amount = #{overtimeAmount},</if>
            <if test="taxTate != null">tax_tate = #{taxTate},</if>
            <if test="amountPayable != null">amount_payable = #{amountPayable},</if>
            <if test="amountPaid != null">amount_paid = #{amountPaid},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="paymentAccount != null">payment_account = #{paymentAccount},</if>
            <if test="paymentDescription != null">payment_description = #{paymentDescription},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectTaskLaborById" parameterType="String">
        delete from zz_project_task_labor where id = #{id}
    </delete>

    <delete id="deleteProjectTaskLaborByIds" parameterType="String">
        delete from zz_project_task_labor where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateProjectTaskLaborPayment" parameterType="ProjectTaskLabor">
        update zz_project_task_labor
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="paymentAccount != null">payment_account = #{paymentAccount},</if>
            <if test="paymentDescription != null">payment_description = #{paymentDescription},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectProjectTaskLaborTotal" parameterType="ProjectTaskLabor" resultType="java.util.Map">
        select 
            SUM(IFNULL(work_days, 0)) as workDays,
            SUM(IFNULL(overtime_amount, 0)) as overtimeAmount,
            SUM(IFNULL(amount_payable, 0)) as amountPayable,
            SUM(IFNULL(amount_paid, 0)) as amountPaid,
            SUM(IFNULL(payment_amount, 0)) as paymentAmount
        from zz_project_task_labor
        <where>  
            <if test="projectTaskId != null  and projectTaskId != ''"> and project_task_id = #{projectTaskId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="job != null  and job != ''"> and job = #{job}</if>
            <if test="workDays != null"> and work_days = #{workDays}</if>
            <if test="recipientName != null and recipientName != ''"> and recipient_name like concat('%', #{recipientName}, '%')</if>
            <if test="laborPrice != null"> and labor_price = #{laborPrice}</if>
            <if test="overtimeAmount != null"> and overtime_amount = #{overtimeAmount}</if>
            <if test="taxTate != null"> and tax_tate = #{taxTate}</if>
            <if test="amountPayable != null"> and amount_payable = #{amountPayable}</if>
            <if test="amountPaid != null"> and amount_paid = #{amountPaid}</if>
            <if test="paymentAmount != null"> and payment_amount = #{paymentAmount}</if>
            <if test="paymentTime != null"> and payment_time = #{paymentTime}</if>
            <if test="paymentAccount != null  and paymentAccount != ''"> and payment_account = #{paymentAccount}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(payment_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(payment_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.hasUnpaidAmount != null and params.hasUnpaidAmount == 'true'">
                and (amount_paid > IFNULL(payment_amount, 0) or payment_amount is null)
            </if>
        </where>
    </select>

</mapper>