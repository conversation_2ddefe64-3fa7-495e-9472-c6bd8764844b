-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('受访者查询', '2000', '7', '/task/respondent/query', 'C', '0', 'task:respondent:query', '#', 'admin', sysdate(), '', null, '受访者查询菜单');

-- 按钮权限 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('受访者查询列表', @parentId, '1', '#', 'F', '0', 'task:respondent:list', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('受访者查询导出', @parentId, '2', '#', 'F', '0', 'task:respondent:export', '#', 'admin', sysdate(), '', null, ''); 