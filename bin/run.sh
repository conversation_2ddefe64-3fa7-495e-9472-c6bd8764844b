#!/bin/bash

echo "[信息] 使用后台方式启动Web服务，日志将记录到pmrsch.log文件。"
echo

# 获取脚本所在目录
cd "$(dirname "$0")"

# JVM参数
JAVA_OPTS="-Xms256m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
LOG_FILE="pmrsch.log"
PID_FILE="pmrsch.pid"

# 检查服务是否已经运行
if [ -f "$PID_FILE" ]; then
    pid=$(cat "$PID_FILE")
    if ps -p "$pid" > /dev/null 2>&1; then
        echo "[警告] 服务已经在运行中，PID: $pid"
        exit 1
    else
        rm -f "$PID_FILE"
    fi
fi

# 后台启动服务并记录PID
nohup java $JAVA_OPTS -jar ruoyi-admin.jar >> "$LOG_FILE" 2>&1 &
echo $! > "$PID_FILE"

echo "[信息] Web服务已在后台启动，PID: $(cat "$PID_FILE")"
echo "[信息] 日志文件: $LOG_FILE"
echo 