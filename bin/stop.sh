#!/bin/bash

echo "[信息] 正在停止Web服务..."
echo

# 获取脚本所在目录
cd "$(dirname "$0")"
# cd ../ruoyi-admin/target

PID_FILE="pmrsch.pid"

if [ ! -f "$PID_FILE" ]; then
    echo "[警告] 找不到PID文件，服务可能没有运行。"
    exit 1
fi

pid=$(cat "$PID_FILE")

if ! ps -p "$pid" > /dev/null 2>&1; then
    echo "[警告] 服务未运行，PID: $pid"
    rm -f "$PID_FILE"
    exit 1
fi

# 先尝试正常停止
kill "$pid"

# 等待最多10秒
count=0
while [ $count -lt 10 ]; do
    if ! ps -p "$pid" > /dev/null 2>&1; then
        rm -f "$PID_FILE"
        echo "[信息] Web服务已停止"
        echo
        exit 0
    fi
    let count++
    sleep 1
done

# 如果还在运行，强制终止
if ps -p "$pid" > /dev/null 2>&1; then
    echo "[警告] 服务未能正常停止，正在强制终止..."
    kill -9 "$pid"
    rm -f "$PID_FILE"
fi

echo "[信息] Web服务已停止"
echo 